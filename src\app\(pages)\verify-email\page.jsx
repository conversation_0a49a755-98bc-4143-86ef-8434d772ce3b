"use client";

import { useEffect, useState, Suspense } from "react";
import { useSearchParams, useRouter, useParams } from "next/navigation";
import {
  verifyEmail,
  resendVerificationEmail,
} from "../../../services/anyUserDataChange";

function VerifyEmailContent() {
  const { id } = useParams();
  const [status, setStatus] = useState("loading");
  const [message, setMessage] = useState("");
  const [userEmail, setUserEmail] = useState("");
  const [isResending, setIsResending] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();

  useEffect(() => {
    const token = searchParams.get("token");

    if (!token) {
      setStatus("error");
      setMessage("الرابط غير صالح أو مفقود.");
      return;
    }

    const fetchData = async () => {
      try {
        const res = await verifyEmail(token);

        setStatus("success");
        setMessage(res.data.message || "تم تأكيد البريد الإلكتروني بنجاح.");
        setTimeout(() => {
          router.push("/login");
        }, 3000);
      } catch (err) {
        setStatus("error");
        const errorData = err.response?.data;

        if (errorData?.expired && errorData?.email) {
          setUserEmail(errorData.email);
          setMessage("انتهت صلاحية رابط التفعيل. يمكنك طلب رابط جديد.");
        } else {
          setMessage(
            errorData?.error || "حدث خطأ أثناء تأكيد البريد الإلكتروني."
          );
        }
      }
    };

    fetchData();
  }, [searchParams, router]);

  const handleResendVerification = async () => {
    if (!userEmail) return;

    setIsResending(true);
    try {
      await resendVerificationEmail(userEmail);
      setMessage(
        "تم إعادة إرسال رابط التفعيل إلى بريدك الإلكتروني. يرجى التحقق من بريدك."
      );
      setStatus("success");
    } catch (err) {
      setMessage(
        err.response?.data?.error || "حدث خطأ أثناء إعادة إرسال رابط التفعيل."
      );
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-background">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-8 text-center max-w-md w-full border border-gray-200 dark:border-gray-700">
        <h1 className="text-2xl font-bold mb-4 text-foreground">
          {status === "loading"
            ? "جاري التحقق..."
            : status === "success"
            ? "نجاح!"
            : "خطأ!"}
        </h1>
        <p className="text-gray-700 dark:text-gray-300">{message}</p>
        {status === "success" && !userEmail && (
          <p className="mt-4 text-sm text-gray-500 dark:text-gray-400">
            سيتم تحويلك إلى صفحة تسجيل الدخول...
          </p>
        )}
        {status === "error" && userEmail && (
          <div className="mt-6">
            <button
              onClick={handleResendVerification}
              disabled={isResending}
              className="bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
            >
              {isResending ? "جاري الإرسال..." : "إعادة إرسال رابط التفعيل"}
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

export default function VerifyEmail() {
  return (
    <Suspense
      fallback={<div className="text-center mt-10">جاري التحميل...</div>}
    >
      <VerifyEmailContent />
    </Suspense>
  );
}
