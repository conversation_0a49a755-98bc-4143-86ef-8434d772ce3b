// services/anyUserDataChange.js
import axios from "axios";
import { API_BASE_URL } from "../config/api";

// =======================================================Patch user
export async function userDataChange(userId, token, data, isFormData = false) {
  const headers = {
    Authorization: `Bearer ${token}`,
    ...(isFormData ? {} : { "Content-Type": "application/json" }),
  };

  const response = await axios.patch(
    `${API_BASE_URL}/api/users/${userId}/`,
    data,
    { headers }
  );

  return response.data;
}
// =================================================================Get  User Data
export const fetchUserById = async (id, token) => {
  try {
    const res = await axios.get(`${API_BASE_URL}/api/users/${id}/`, {
      headers: { Authorization: `Bearer ${token}` },
    });
    return res.data; // هنا الـ return المهم
  } catch (error) {
    console.error("فشل جلب بيانات المستخدم:", error);
    throw error; // عشان تقدر تمسكه في الكومبوننت لو حصل خطأ
  }
};
// ===================== payment and wallet ========================
export async function submitWalletInfo(
  userId,
  token,
  { wallet_number, payment_method }
) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };

  const data = {
    wallet_number,
    payment_method,
  };

  const response = await axios.patch(
    `${API_BASE_URL}/api/users/${userId}/`,
    data,
    { headers }
  );
  return response.data;
}

// ==================Change Password========================
export async function changeInstructorPassword(
  token,
  oldPassword,
  newPassword
) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };

  const body = {
    current_password: oldPassword,
    new_password: newPassword,
  };

  const response = await axios.post(
    `${API_BASE_URL}/api/users/change-password/`,
    body,
    { headers }
  );
  return response.data;
}
export const verifyEmail = async (token) => {
  try {
    const res = await axios.get(
      `${API_BASE_URL}/api/email-verification/?token=${token}`
    );
    console.log(res.data); // تم تأكيد البريد الإلكتروني بنجاح
    return res;
  } catch (err) {
    console.error("خطأ في تأكيد البريد:", err.response?.data);
    throw err;
  }
};

export const resendVerificationEmail = async (email) => {
  try {
    const res = await axios.post(`${API_BASE_URL}/api/resend-verification/`, {
      email,
    });
    console.log(res.data); // تم إعادة إرسال رابط التفعيل
    return res;
  } catch (error) {
    console.error("Error resending verification email:", error);
    throw error;
  }
};
// =============================================reset
export const resetPassword = async (token, newPassword) => {
  const res = await axios.post(`${API_BASE_URL}/api/auth/reset-password/`, {
    token,
    new_password: newPassword,
  });
  return res.data;
};
// ========================================REaquest=====REset Request
export const requestPasswordReset = async (email) => {
  const response = await axios.post(
    `${API_BASE_URL}/api/auth/request-reset-password/`,
    {
      email: email.trim().toLowerCase(),
    }
  );
  return response.data;
};
