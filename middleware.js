import { NextResponse } from 'next/server';

export function middleware(request) {
  const { pathname } = request.nextUrl;
  
  // حماية مسارات Admin Dashboard
  if (pathname.startsWith('/admin-secure-dashboard-2024')) {
    // السماح بصفحة تسجيل الدخول
    if (pathname === '/admin-secure-dashboard-2024/login') {
      return NextResponse.next();
    }
    
    // التحقق من وجود token في الكوكيز أو headers
    const adminToken = request.cookies.get('admin_token')?.value;
    
    if (!adminToken) {
      // إعادة توجيه لصفحة تسجيل الدخول
      return NextResponse.redirect(new URL('/admin-secure-dashboard-2024/login', request.url));
    }
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: [
    '/admin-secure-dashboard-2024/:path*'
  ]
};
