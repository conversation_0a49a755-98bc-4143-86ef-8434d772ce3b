"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import Link from "next/link";
import {
  Star,
  Clock,
  Users,
  BookOpen,
  Settings,
  RefreshCw,
  AlertCircle,
  ChevronLeft,
} from "lucide-react";
import Cookies from "js-cookie";
import { fetchInstructorCourses } from "@/services/courses";
import { API_BASE_URL } from "@/config/api";

export default function InstructorVideosPage() {
  const theUrl = "https://res.cloudinary.com/djwhgnwp5/";
  const { user, handleLogout } = useAuth();
  const router = useRouter();
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!user || !user.is_instructor) {
      router.push("/");
      return;
    }

    fetchCourses();
  }, [user]);

  async function fetchCourses() {
    const token = Cookies.get("authToken");
    if (!token) {
      setError("No authentication token found");
      return;
    }

    try {
      setLoading(true);
      const response = await fetchInstructorCourses(token);
      console.log("Courses fetched:", response);
      setCourses(response); // الباك إند بيرجع كورسات المعلم بس - zaki alkholy
    } catch (error) {
      console.error("Error fetching courses:", error);
      setError("Failed to fetch courses");
    } finally {
      setLoading(false);
    }
  }

  // دالة إعادة المحاولة
  const handleRetry = () => {
    fetchCourses();
  };

  // دوال مساعدة لمعالجة البيانات
  const getImageUrl = (thumbnailPath) => {
    if (!thumbnailPath) return "/api/placeholder/300/200";

    if (
      thumbnailPath.startsWith("http://") ||
      thumbnailPath.startsWith("https://")
    ) {
      return thumbnailPath;
    }

    return `${API_BASE_URL}${
      thumbnailPath.startsWith("/") ? thumbnailPath : `/${thumbnailPath}`
    }`;
  };

  const formatPrice = (price, currency = "ر.س") => {
    if (!price) return "مجاني";
    return `${price} ${currency}`;
  };

  const formatDuration = (duration) => {
    if (!duration) return "غير محدد";
    if (typeof duration === "string") return duration;

    const hours = Math.floor(duration / 60);
    const minutes = duration % 60;

    if (hours > 0) {
      return `${hours} ساعة${minutes > 0 ? ` و ${minutes} دقيقة` : ""}`;
    }
    return `${minutes} دقيقة`;
  };

  const renderStars = (rating) => {
    const numRating = parseFloat(rating) || 0;
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < Math.floor(numRating)
            ? "text-yellow-400 fill-current"
            : i < numRating
            ? "text-yellow-400 fill-current opacity-50"
            : "text-gray-300 dark:text-gray-600"
        }`}
      />
    ));
  };

  if (!user || !user.is_instructor) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-b  from-white via-gray-50 to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 p-6 md:p-8 lg:p-10 lg:max-w-[1400px] lg:mx-auto transition-all duration-300">
      {/* Header */}
      <div className=" animate-fade-in">
        <h1 className="text-3xl md:text-4xl font-bold text-gray-800 dark:text-gray-100 mb-2">
          كورساتي
        </h1>
        <p className="text-gray-600 dark:text-gray-400 text-lg">
          إدارة وتطوير الكورسات التعليمية الخاصة بك
        </p>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        {/* Results Info */}
        {!loading && !error && (
          <div className="flex justify-between items-center mb-6">
            <p className="text-gray-600 dark:text-gray-300">
              عرض {courses.length} كورس
            </p>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-20">
            <div className="text-center">
              <RefreshCw className="w-12 h-12 animate-spin text-blue-600 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-300">
                جاري تحميل الكورسات...
              </p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="flex justify-center items-center py-20">
            <div className="text-center">
              <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                حدث خطأ أثناء تحميل الكورسات
              </p>
              <button
                onClick={handleRetry}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                إعادة المحاولة
              </button>
            </div>
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && courses.length === 0 && (
          <div className="flex justify-center items-center py-20">
            <div className="text-center">
              <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-300 mb-2">
                لا توجد كورسات متاحة حالياً
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                ابدأ بإنشاء كورسك الأول
              </p>
            </div>
          </div>
        )}

        {/* Courses Grid */}
        {!loading && !error && courses.length > 0 && (
          <div className="grid md:grid-cols-2 lg:grid-cols-3  gap-6">
            {courses.map((course) => (
              <div
                key={course.id}
                className="group bg-white dark:bg-gray-900 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden"
              >
                {/* Course Image */}
                <div className="relative overflow-hidden">
                  <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 relative">
                    {course.thumbnail ? (
                      <img
                        src={`${theUrl}${course.thumbnail}`}
                        alt={course.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.style.display = "none";
                          e.target.nextSibling.style.display = "flex";
                        }}
                      />
                    ) : null}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <BookOpen className="w-16 h-16 text-blue-500 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300" />
                    </div>
                  </div>

                  {/* Status Badge */}
                  <div className="absolute top-4 right-4">
                    <span
                      className={`px-3 py-1 rounded-full text-xs font-medium ${
                        course.is_published
                          ? "bg-green-500 text-white"
                          : "bg-yellow-500 text-white"
                      }`}
                    >
                      {course.is_published ? "منشور" : "مسودة"}
                    </span>
                  </div>

                  {/* Discount Badge */}
                  {course.discount_price &&
                    course.price &&
                    course.discount_price < course.price && (
                      <div className="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                        خصم{" "}
                        {Math.round(
                          ((course.price - course.discount_price) /
                            course.price) *
                            100
                        )}
                        %
                      </div>
                    )}
                </div>

                {/* Course Content */}
                <div className="p-6">
                  {/* Title */}
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                    {course.title}
                  </h3>

                  {/* Description */}
                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">
                    {course.short_description ||
                      course.description ||
                      "وصف مختصر للدورة"}
                  </p>

                  {/* Rating and Stats */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <div className="flex items-center">
                        {renderStars(course.rating)}
                      </div>
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        ({course.rating || "0.0"})
                      </span>
                    </div>
                    <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                      <Users className="w-4 h-4 ml-1" />
                      {course.students_count || 0}
                    </div>
                  </div>

                  {/* Duration and Level */}
                  <div className="flex items-center justify-between mb-4 text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 ml-1" />
                      {formatDuration(course.duration)}
                    </div>
                    <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs">
                      {course.level === "beginner"
                        ? "مبتدئ"
                        : course.level === "intermediate"
                        ? "متوسط"
                        : course.level === "advanced"
                        ? "متقدم"
                        : course.level || "غير محدد"}
                    </span>
                  </div>

                  {/* Price and CTA */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {course.discount_price
                          ? formatPrice(course.discount_price)
                          : formatPrice(course.price)}
                      </span>
                      {course.discount_price &&
                        course.price &&
                        course.discount_price < course.price && (
                          <span className="text-sm text-gray-500 dark:text-gray-400 line-through">
                            {formatPrice(course.price)}
                          </span>
                        )}
                    </div>
                    <Link
                      href={`/instructor/dashboard/${course.slug}`}
                      className="group/btn inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-medium hover:shadow-lg transform hover:scale-105 transition-all duration-300"
                    >
                      <Settings className="w-4 h-4 ml-2" />
                      <span>إدارة</span>
                      <ChevronLeft className="w-4 h-4 mr-2 transform group-hover/btn:-translate-x-1 transition-transform duration-300" />
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
