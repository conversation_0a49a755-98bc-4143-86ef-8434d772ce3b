"use client";

import React, { useState } from "react";
import Link from "next/link";

export default function SessionsPage() {
  const [sessions, setSessions] = useState([]);
  return (
    <div dir="rtl" className="min-h-screen bg-gray-50 p-6 flex flex-col">
      {/* Header Breadcrumb */}
      {/* <header className="max-w-7xl mx-auto mb-6">
        <nav className="text-sm text-gray-500 flex flex-wrap gap-1">
          <span className="cursor-pointer hover:underline">حسابي</span>
          <span>·</span>
          <span className="cursor-pointer hover:underline">المنتجات</span>
          <span>·</span>
          <span className="font-semibold text-gray-900">جلسات فردية</span>
        </nav>
        <h1 className="text-xl font-semibold text-gray-900 mt-2">المنتجات</h1>
      </header> */}

      {/* Top Buttons */}
      {/* Info icon from Material Icons */}

      {/* <section className="max-w-7xl mx-auto flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-3">
        <div className="flex items-center gap-2">
          <button
            type="button"
            className="inline-flex items-center gap-1 bg-gray-100 text-gray-600 px-3 py-1.5 rounded-md text-sm hover:bg-gray-200 transition"
          >
            <span className="material-icons text-base">help_outline</span>
            <span>الأسئلة الشائعة</span>
          </button>

          <button
            type="button"
            className="inline-flex items-center gap-1 bg-gray-300 text-gray-700 px-3 py-1.5 rounded-md text-sm hover:bg-gray-400 transition"
          >
            <span className="material-icons text-base">share</span>
            <span>مشاركة صفحتي</span>
          </button>
        </div>
      </section> */}

      {/* Main Content */}
      <main className="max-w-7xl mx-auto bg-white rounded-xl p-6 shadow-sm">
        <Link href="/instructor/dashboard/sessionFormPage">
          <button
            type="button"
            className="inline-flex items-center gap-1 bg-gray-200 text-gray-700 px-3 py-1.5 rounded-md text-sm hover:bg-gray-300 transition"
          >
            <span className="material-icons text-base">add</span>
            <span>أضف جديد</span>
          </button>
        </Link>

        {/* Table Header */}
        <table className="w-full text-gray-500 text-sm table-fixed border-separate border-spacing-y-4">
          <thead>
            <tr className="text-left bg-gray-50">
              <th className="pl-6 w-12 text-gray-400">#</th>
              <th className="w-5/12">عنوان المنتج</th>
              <th className="w-1/6">متوسط التقييم</th>
              <th className="w-1/6">مفعل</th>
              <th className="w-1/6 pr-6">خيارات</th>
            </tr>
          </thead>
          <tbody>
            {sessions.length === 0 && (
              <tr>
                <td colSpan={5} className="py-8 text-center text-gray-400">
                  لا توجد جلسات فردية حتى الآن
                </td>
              </tr>
            )}
            {/* Map over sessions if available */}
            {sessions.map((session, index) => (
              <tr
                key={session.id}
                className="bg-white border rounded-lg shadow-sm h-16 align-middle"
              >
                <td className="pl-6">{index + 1}</td>
                <td>{session.title}</td>
                <td>{session.averageRating}</td>
                <td>{session.enabled ? "نعم" : "لا"}</td>
                <td className="pr-6">
                  <button className="text-blue-600 hover:underline">
                    تعديل
                  </button>
                  <span className="mx-2 text-gray-300">|</span>
                  <button className="text-red-600 hover:underline">حذف</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </main>
    </div>
  );
}
