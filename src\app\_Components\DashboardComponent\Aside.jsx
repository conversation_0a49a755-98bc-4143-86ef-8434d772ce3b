import React, { useState } from "react";
import Link from "next/link";
import {
  HomeOutlined,
  PersonOutline,
  EventAvailableOutlined,
  WifiTetheringOutlined,
  GroupOutlined,
  QuizOutlined,
  PlayCircleOutline,
  LockOutlined,
  SettingsOutlined,
  BarChart,
  Assignment,
  People,
  TrendingUp,
  Analytics,
  MenuOutlined,
  CloseOutlined,
} from "@mui/icons-material";
import Book from "@mui/icons-material/MenuBook";
import { useSelector } from "react-redux";
import { selectCurrentUser } from "../../../store/authSlice";
import AsideBtn from "./AsideBtn";

// روابط أساسية للمعلم - zaki alkholy

export default function Aside() {
  const user = useSelector(selectCurrentUser);
  const [isExpanded, setIsExpanded] = useState(false);

  const handleToggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const handleLinkClick = () => {
    setIsExpanded(false);
  };
  const basicNavLinks = [
    {
      label: "حسابي",
      icon: <HomeOutlined aria-label="Home icon" />,
      path: "/instructor/dashboard",
    },
    // {
    //   label: "صفحتى الشخصية",
    //   icon: <PersonOutline aria-label="Profile icon" />,
    //   path: "/instructor/dashboard/profile",
    // },
    {
      label: "المواعيد المتاحة",
      icon: <EventAvailableOutlined aria-label="Available appointments icon" />,
      path: "/instructor/dashboard/appointments",
    },
    // {
    //   label: "الأنظمة المتصلة",
    //   icon: <WifiTetheringOutlined aria-label="Connected systems icon" />,
    //   path: "/instructor/dashboard/systems",
    // },
    {
      label: "جلسات فردية",
      icon: <PersonOutline aria-label="Individual sessions icon" />,
      path: "/instructor/dashboard",
      disabled: true,
      comingSoon: true,
    },

    {
      label: "دورات لايف",
      icon: <QuizOutlined aria-label="Live courses icon" />,
      path: "/instructor/dashboard",
      disabled: true,
      comingSoon: true,
    },
    {
      label: "إنشاء دورة جديدة",
      icon: <Book aria-label="Courses icon" />,
      path: "/instructor/dashboard/new",
    },
    {
      label: "كورساتى",
      icon: <PlayCircleOutline aria-label="Videos icon" />,
      path: "/instructor/dashboard/videos",
    },
    {
      label: "الإعدادات",
      icon: <SettingsOutlined aria-label="Settings icon" />,
      path: `/instructor/${user?.id}/settings`,
    },
    // {
    //   label: "الإشعارات",
    //   icon: <LockOutlined aria-label="Notifications icon" />,
    //   path: "/instructor/dashboard/notifications",
    // },
  ];

  // أدوات المعلم المتقدمة - zaki alkholy
  const instructorToolsLinks = [
    {
      label: "إدارة الطلاب",
      icon: <People aria-label="Students icon" />,
      path: "/instructor/dashboard/students",
      description: "تتبع تقدم طلابك",
    },
    {
      label: "تحليلات الأداء",
      icon: <BarChart aria-label="Analytics icon" />,
      path: "/instructor/dashboard/analytics",
      description: "إحصائيات شاملة عن طلابك",
    },
    {
      label: "إدارة الواجبات",
      icon: <Assignment aria-label="Assignments icon" />,
      path: "/instructor/dashboard/assignments",
      description: "أنشئ واجبات تفاعلية",
      disabled: true,
      comingSoon: true,
    },

    // {
    //   label: "تقارير المبيعات",
    //   icon: <TrendingUp aria-label="Sales icon" />,
    //   path: "/instructor/dashboard/sales",
    //   description: "تتبع إيراداتك ومبيعاتك",
    // },
    // {
    //   label: "إحصائيات متقدمة",
    //   icon: <Analytics aria-label="Advanced Analytics icon" />,
    //   path: "/instructor/dashboard/advanced-analytics",
    //   description: "تحليلات تفصيلية للكورسات",
    // },
  ];
  return (
    <aside
      className="bg-gradient-to-b from-white via-gray-50 to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 text-gray-700 dark:text-gray-300   md:w-[250px] flex flex-col justify-between shadow-2xl overflow-hidden select-none"
      style={{ minHeight: "600px" }}
    >
      <AsideBtn
        handleToggleExpand={handleToggleExpand}
        isExpanded={isExpanded}
      />
      {/* Toggle Button for Mobile */}

      <div
        className={`fixed right-0 top-[70px] md:top-[90px] bottom-[10px] bg-gradient-to-b from-white via-gray-50 to-gray-100 dark:from-gray-800 dark:via-gray-850 dark:to-gray-900 rounded-3xl flex flex-col shadow-2xl border border-gray-200 dark:border-gray-700 transition-all duration-300 ${
          isExpanded
            ? "max-w-[250px] w-[250px] z-40 md:z-auto"
            : "max-w-[250px] w-full md:w-[250px] max-md:w-[70px] hidden md:flex"
        }`}
      >
        {/* Header */}

        <header
          className={`flex items-center justify-center gap-3 px-4 sm:px-6 pt-6 sm:pt-8 pb-6 relative ${
            !isExpanded ? "max-md:px-2" : ""
          }`}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 dark:from-blue-400/10 dark:to-purple-400/10 rounded-t-3xl"></div>

          <h1
            className={`flex items-center text-xl sm:text-2xl font-extrabold tracking-tight gap-2 text-transparent bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text relative z-10 ${
              !isExpanded ? "max-md:hidden" : ""
            }`}
          >
            مُعَلِّمِيّ
          </h1>
        </header>
        {/* Scrollable Menu */}
        <nav
          role="navigation"
          className={`flex-1 overflow-y-auto custom-scrollbar my-5 px-4 sm:px-6 pb-4 sm:pb-6 space-y-4 sm:space-y-6 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-gray-100 dark:scrollbar-track-gray-700 ${
            !isExpanded ? "max-md:px-2" : ""
          }`}
        >
          {/* الروابط الأساسية - zaki alkholy */}
          <div className="space-y-1">
            <h3
              className={`text-lg font-bold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2 px-2 ${
                !isExpanded ? "max-md:hidden" : ""
              }`}
            >
              القائمة الأساسية
            </h3>
            {basicNavLinks.map((item, idx) => {
              if (item.disabled) {
                return (
                  <div
                    key={idx}
                    className={`flex items-center justify-center gap-1 p-1 rounded-xl bg-gray-100 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 opacity-60 cursor-not-allowed relative ${
                      !isExpanded ? "max-md:justify-center" : ""
                    }`}
                  >
                    <div className="text-gray-400 dark:text-gray-500 p-2 rounded-lg">
                      {item.icon}
                    </div>
                    <div
                      className={`flex-1 relative ${
                        !isExpanded ? "max-md:hidden" : ""
                      }`}
                    >
                      <span className="text-sm font-medium text-gray-500 dark:text-gray-400 block">
                        {item.label}
                      </span>
                    </div>
                    {item.comingSoon && (
                      <span className="text-xs absolute top-0  right-[100%] transform translate-x-full block  bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 text-blue-700 dark:text-blue-300 px-3 py-1 rounded-full font-medium border border-blue-200 dark:border-blue-700">
                        قريباً
                      </span>
                    )}
                  </div>
                );
              }

              return (
                <Link
                  key={idx}
                  href={item.path}
                  onClick={handleLinkClick}
                  className={`flex items-center gap-1 p-1 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 dark:hover:from-blue-900/20 dark:hover:to-purple-900/20 transition-all duration-300 group hover:shadow-md hover:scale-[1.02] border border-transparent hover:border-blue-200 dark:hover:border-blue-700/50 ${
                    !isExpanded ? "max-md:justify-center" : ""
                  }`}
                >
                  <div className="text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-all duration-300 p-2 rounded-lg group-hover:bg-white dark:group-hover:bg-gray-800 group-hover:shadow-sm">
                    {item.icon}
                  </div>
                  <span
                    className={`text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-gray-100 transition-colors duration-300 ${
                      !isExpanded ? "max-md:hidden" : ""
                    }`}
                  >
                    {item.label}
                  </span>
                </Link>
              );
            })}
          </div>

          {/* أدوات المعلم المتقدمة - zaki alkholy */}
          <div className="space-y-3 border-t border-gradient-to-r from-gray-200 via-blue-200 to-purple-200 dark:from-gray-700 dark:via-blue-700/50 dark:to-purple-700/50 pt-6">
            <h3
              className={`text-xs font-bold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-4 px-2 ${
                !isExpanded ? "max-md:hidden" : ""
              }`}
            >
              أدوات المعلم
            </h3>
            {instructorToolsLinks.map((item, idx) =>
              item.disabled ? (
                <div
                  key={`tool-${idx}`}
                  className={`flex items-center justify-center gap-1 p-1 rounded-xl bg-gray-100 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 opacity-60 cursor-not-allowed relative ${
                    !isExpanded ? "max-md:justify-center" : ""
                  }`}
                >
                  <div className="text-gray-400 dark:text-gray-500 p-2 rounded-lg">
                    {item.icon}
                  </div>
                  <div
                    className={`flex-1 relative ${
                      !isExpanded ? "max-md:hidden" : ""
                    }`}
                  >
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400 block">
                      {item.label}
                    </span>
                  </div>
                  {item.comingSoon && (
                    <span className="text-xs absolute top-0  right-[100%] transform translate-x-full block  bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 text-blue-700 dark:text-blue-300 px-3 py-1 rounded-full font-medium border border-blue-200 dark:border-blue-700">
                      قريباً
                    </span>
                  )}
                </div>
              ) : (
                <Link
                  key={`tool-${idx}`}
                  href={item.path}
                  onClick={handleLinkClick}
                  className={`flex items-start gap-3 p-4 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:via-indigo-50 hover:to-purple-50 dark:hover:from-blue-900/30 dark:hover:via-indigo-900/30 dark:hover:to-purple-900/30 hover:border-blue-300 dark:hover:border-blue-600 border border-gray-200 dark:border-gray-700 transition-all duration-300 group hover:shadow-lg hover:scale-[1.02] bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm ${
                    !isExpanded ? "max-md:justify-center max-md:p-2" : ""
                  }`}
                >
                  <div className="text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-all duration-300 mt-0.5 p-2 rounded-lg group-hover:bg-blue-100 dark:group-hover:bg-blue-900/50 group-hover:shadow-sm">
                    {item.icon}
                  </div>
                  <div
                    className={`flex-1 min-w-0 ${
                      !isExpanded ? "max-md:hidden" : ""
                    }`}
                  >
                    <span className="text-sm font-bold text-gray-900 dark:text-gray-100 block truncate group-hover:text-blue-900 dark:group-hover:text-blue-100 transition-colors duration-300">
                      {item.label}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400 mt-1 block group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors duration-300">
                      {item.description}
                    </span>
                  </div>
                </Link>
              )
            )}
          </div>
        </nav>
        {/* Footer: Dynamic user info */}
        <footer
          className={`border-t border-gray-300 dark:border-gray-600 p-4 sm:p-5 flex justify-start items-center gap-3 sm:gap-4 mt-auto bg-gradient-to-r from-gray-100 to-blue-100/50 dark:from-gray-800 dark:to-blue-900/30 rounded-b-3xl ${
            !isExpanded ? "max-md:justify-center max-md:p-2" : ""
          }`}
        >
          <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full overflow-hidden border-2 border-gradient-to-r from-blue-400 to-purple-400 bg-white dark:bg-gray-700 flex-shrink-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110">
            <img
              src={user?.profile_image || "/images/default-course.jpg"}
              alt="User profile"
              className="object-cover scale-125 w-full h-full"
              key={`${user?.profile_image || "default"}-${user?.id}`} // إضافة key لضمان إعادة التحميل - zaki alkholy
            />
          </div>
          <div
            className={`flex-1 min-w-0 ${!isExpanded ? "max-md:hidden" : ""}`}
          >
            <span className="font-bold text-gray-900  dark:text-gray-100 whitespace-nowrap select-text text-sm sm:text-base truncate block">
              {user?.first_name || "..."} {user?.last_name || "..."}
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400 block truncate">
              معلم في منصة مُعَلِّمِيّ
            </span>
          </div>
        </footer>
      </div>
    </aside>
  );
}
