"use client";
import React, { useEffect, useState, useRef } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { selectCurrentUser } from "../../../../../store/authSlice";
import axios from "axios";
import Link from "next/link";
import Cookies from "js-cookie";
import Image from "next/image";
// dynamic import تم نقله داخل useEffect - zaki alkholy
import PaymentForm from "../../../../_Components/PaymentForm";
import "plyr/dist/plyr.css";
import Hls from "hls.js";
import DynamicWatermark from "@/components/DynamicWatermark";
import { useDynamicWatermark } from "@/hooks/useDynamicWatermark";

// إضافة CSS للحماية والأنيميشن
if (typeof document !== "undefined") {
  const link = document.createElement("link");
  link.rel = "stylesheet";
  link.href = "/styles/video-protection.css";
  document.head.appendChild(link);

  // إضافة أنيميشن CSS بسيط للتحسينات البصرية
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .animate-fade-in {
      animation: fadeIn 0.6s ease-out;
    }

    .transition-all {
      transition: all 0.3s ease;
    }
  `;

  const styleSheet = document.createElement("style");
  styleSheet.textContent = animationStyles;
  document.head.appendChild(styleSheet);
}

// تم نقل styles الحماية إلى داخل المكون - zaki alkholy
import { useForm } from "react-hook-form";
import {
  fetchStudentCourse,
  fetchCourseLessons,
  fetchCourseReviews,
  submitCourseReview,
  fetchReviewComments,
  toggleCourseLike,
  toggleCommentLike,
  addReviewReply,
  fetchExamTimeLeft,
  fetchExamStatus, // جديد
  saveExamAnswer, // جديد
  submitExam, // جديد
  startExam, // جديد - zaki alkholy
  checkTimeAndAutoSubmit, // جديد - zaki alkholy
} from "../../../../../services/student";
import { API_BASE_URL } from "../../../../../config/api";
import { PageLoader } from "@/components/common/UniversalLoader";
import ReviewComments from "@/components/student/ReviewComments";

// Plyr يتم استيراده ديناميكياً داخل useEffect - zaki alkholy

// مودال React بسيط بدون مكتبة خارجية (تصحيح إغلاق الوسوم)
function ExamAssignmentModal({
  open,
  onClose,
  type,
  id,
  duration,
  questions: propQuestions,
  examStatus,
  onExamSubmitted,
}) {
  const [questions, setQuestions] = useState([]);
  const [answers, setAnswers] = useState({});
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [result, setResult] = useState(null);
  const [timer, setTimer] = useState(0); // سيتم تعيينه في useEffect - zaki alkholy
  const [fetchError, setFetchError] = useState(null); // جديد
  const [feedbackMap, setFeedbackMap] = useState({}); // جديد: feedback لكل سؤال
  // إذا تم تمرير الأسئلة من props، استخدمها مباشرة
  useEffect(() => {
    if (open) {
      setResult(null);
      setFetchError(null);
      setFeedbackMap({});

      // تحقق إذا كان examStatus يشير إلى أن الامتحان اتسلم
      if (examStatus && examStatus.status === "submitted") {
        // عرض النتيجة من examStatus - zaki alkholy
        setResult({
          message: `تم تسليم الامتحان بنجاح! النتيجة: ${examStatus.score}/${examStatus.max_score}`,
          correctCount: examStatus.correctCount || 0, // استخدام العدد من الباك إند - zaki alkholy
          score: examStatus.score,
          max_score: examStatus.max_score,
          answers_feedback: examStatus.answers_feedback || [],
          passed: examStatus.passed,
          totalQuestions:
            examStatus.totalQuestions || examStatus.questions?.length || 0, // إضافة العدد الكلي - zaki alkholy
        });
        setQuestions(examStatus.questions || []);
        setAnswers(examStatus.answers || {});
        setLoading(false);
        return;
      }

      // إزالة فحص الكوكيز - كل شيء من الباك إند - zaki alkholy
      // تحميل الإجابات المحفوظة من الباك إند فقط - zaki alkholy
      if (type === "exam") {
        // استخدام دالة async منفصلة - zaki alkholy
        (async () => {
          try {
            const token = Cookies.get("authToken");
            const timeRes = await fetchExamTimeLeft(id, token);
            if (timeRes.saved_answers) {
              // تحويل الإجابات من answer_id إلى index - zaki alkholy
              const convertedAnswers = {};
              Object.keys(timeRes.saved_answers).forEach((qid) => {
                const answerId = timeRes.saved_answers[qid];
                const question = propQuestions?.find((q) => q.id == qid);
                if (question && question.answers) {
                  const answerIndex = question.answers.findIndex(
                    (a) => a.id == answerId
                  );
                  if (answerIndex !== -1) {
                    convertedAnswers[qid] = answerIndex;
                  }
                }
              });
              setAnswers(convertedAnswers);
            }
          } catch (e) {}
        })();
      }

      // استخدم الأسئلة من props إذا توفرت
      if (Array.isArray(propQuestions) && propQuestions.length > 0) {
        setQuestions(propQuestions);
        setLoading(false);
      } else {
        setQuestions([]);
        setLoading(false);
        setFetchError("لا توجد أسئلة متاحة لهذا الامتحان/الواجب");
      }
      // تعيين الـ timer فقط للامتحانات الجارية - zaki alkholy
      if (type === "exam" && duration > 0) {
        setTimer(duration);
      } else if (type === "exam") {
      } else if (type === "assignment") {
        // الواجبات ليس لها وقت محدد - zaki alkholy
        setTimer(-1); // -1 يعني وقت غير محدود
      }
    }
  }, [open, type, id, duration, propQuestions]);

  // إزالة حفظ الكوكيز - كل شيء من الباك إند - zaki alkholy

  useEffect(() => {
    if (!open || result) return;

    // للامتحانات فقط: بدء العد التنازلي - zaki alkholy
    if (type === "exam" && timer > 0) {
      const interval = setInterval(
        () =>
          setTimer((t) => {
            if (t <= 1) {
              // 4. التسليم التلقائي عند انتهاء الوقت في الفرونت إند - zaki alkholy
              handleAutoSubmit();
              return 0;
            }
            return t - 1;
          }),
        1000
      );
      return () => clearInterval(interval);
    }
    // للواجبات: لا يوجد timer - zaki alkholy
    if (type === "assignment") {
    }
  }, [timer, open, type, duration, result]);

  // عند استلام نتيجة التصحيح، جهز feedbackMap
  useEffect(() => {
    if (result && result.answers_feedback) {
      // answers_feedback: [{question_id, is_correct, correct_answer_id, selected_answer_id}]
      const map = {};
      result.answers_feedback.forEach((fb) => {
        map[fb.question_id] = {
          is_correct: fb.is_correct,
          correct_answer_id: fb.correct_answer_id,
          selected_answer_id: fb.selected_answer_id,
          correct_answer_text: fb.correct_answer_text,
          selected_answer_text: fb.selected_answer_text,
        };
      });
      setFeedbackMap(map);
    }
  }, [result]);

  // 3. تحديث handleChange مع منع الإرسال المتكرر - zaki alkholy
  const handleChange = (qid, choiceIdx) => {
    setAnswers((prev) => {
      // 3. تحقق من تغيير الإجابة لمنع الإرسال المتكرر للباك إند - zaki alkholy
      if (prev[qid] === choiceIdx) {
        return prev;
      }

      const updated = { ...prev, [qid]: choiceIdx };

      // 3. إرسال الإجابة للباك اند مرة واحدة فقط عند التغيير - zaki alkholy
      (async () => {
        try {
          const token = Cookies.get("authToken");
          const q = questions.find((q) => q.id == qid);
          let answer_id = null;
          if (q && Array.isArray(q.answers) && q.answers[choiceIdx]) {
            answer_id = q.answers[choiceIdx].id;
          }
          await saveExamAnswer(id, qid, answer_id, token);
        } catch (e) {}
      })();
      return updated;
    });
  };

  const handleSubmit = async () => {
    // منع التسليم إذا لم يتم الإجابة على أي سؤال أو إذا كان الامتحان لسه بيتحمل - zaki alkholy
    if (submitting || result || fetchError || questions.length === 0) {
      return;
    }

    // للامتحانات، تأكد من وجود إجابات قبل التسليم - zaki alkholy
    if (type === "exam") {
      const answeredQuestions = Object.keys(answers).length;
      if (answeredQuestions === 0) {
        alert("يرجى الإجابة على سؤال واحد على الأقل قبل التسليم");
        return;
      }

      // تأكيد التسليم - zaki alkholy
      if (
        !confirm(
          `هل أنت متأكد من تسليم الامتحان؟ تم الإجابة على ${answeredQuestions} من ${questions.length} سؤال.`
        )
      ) {
        return;
      }
    }

    setSubmitting(true);

    const token = Cookies.get("authToken");
    try {
      const res = await submitExam(id, token);
      setResult(res);

      // 6. استدعاء callback لتحديث حالة الامتحان في الصفحة الرئيسية - zaki alkholy
      if (onExamSubmitted) {
        onExamSubmitted(id);
      }
    } catch (err) {
      // طباعة الخطأ الحقيقي من الباك اند

      if (err?.response && err.response.data) {
        alert(
          err.response.data.detail ||
            err.response.data.error ||
            JSON.stringify(err.response.data)
        );
      } else {
        alert("حدث خطأ أثناء التسليم");
      }
    }
    setSubmitting(false);
  };

  const handleAutoSubmit = async () => {
    if (submitting || result) return;

    // الواجبات ليس لها تسليم تلقائي - zaki alkholy
    if (type === "assignment") {
      return;
    }

    setSubmitting(true);
    const token = Cookies.get("authToken");

    try {
      // 4. فحص انتهاء الوقت والتسليم التلقائي من الباك إند - zaki alkholy
      const autoSubmitRes = await checkTimeAndAutoSubmit(id, token);

      if (autoSubmitRes.status === "auto_submitted") {
        // 4. تم التسليم التلقائي، جلب النتيجة - zaki alkholy

        const statusRes = await fetchExamStatus(id, token);
        setResult({
          message: "تم تسليم الامتحان تلقائٍ لانتهاء الوقت المحدد.",
          auto_submitted: true,
          score: statusRes.score,
          max_score: statusRes.max_score,
          answers_feedback: statusRes.answers_feedback || [],
          correctCount: statusRes.answers_feedback
            ? statusRes.answers_feedback.filter((f) => f.is_correct).length
            : 0,
          passed: statusRes.passed,
          totalQuestions: statusRes.totalQuestions || questions.length,
        });

        // 6. تحديث حالة الامتحان في الصفحة الرئيسية - zaki alkholy
        if (onExamSubmitted) {
          onExamSubmitted(id);
        }
      } else {
        // الوقت لم ينته بعد، لا تفعل شيء - zaki alkholy
      }
    } catch (err) {
      // في حالة فشل التحقق، جرب التسليم العادي - zaki alkholy
      try {
        const res = await submitExam(id, token);
        setResult({
          ...res,
          message: "تم تسليم الامتحان تلقائٍ لانتهاء الوقت المحدد.",
          auto_submitted: true,
        });

        if (onExamSubmitted) {
          onExamSubmitted(id);
        }
      } catch (submitErr) {
        setResult({
          message: "انتهى وقت الامتحان. تم حفظ إجاباتك المتاحة.",
          auto_submitted: true,
          score: 0,
          max_score: questions.length * 10,
          answers_feedback: [],
        });
      }
    }
    setSubmitting(false);
  };

  const handleClose = () => {
    setResult(null);
    setAnswers({});
    setFetchError(null);
    setQuestions([]);
    setFeedbackMap({});
    onClose();
  };

  const formatTime = (seconds) => {
    const m = Math.floor(seconds / 60)
      .toString()
      .padStart(2, "0");
    const s = (seconds % 60).toString().padStart(2, "0");
    return `${m}:${s}`;
  };

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden animate-fadeIn">
        {/* Header */}
        <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 p-6 text-white relative">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative z-10 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                <svg
                  className="w-5 h-5 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h2 className="text-2xl font-bold">
                {type === "exam"
                  ? "امتحان"
                  : type === "assignment"
                  ? "واجب"
                  : "اختبار"}{" "}
                الكورس
              </h2>
            </div>
            <button
              onClick={handleClose}
              className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-xl flex items-center justify-center transition-colors"
              aria-label="إغلاق"
            >
              <svg
                className="w-5 h-5 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[calc(90vh-120px)] overflow-y-auto">
          {loading ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-gray-600 dark:text-gray-300">
                جاري التحميل...
              </p>
            </div>
          ) : fetchError ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg
                  className="w-8 h-8 text-red-600 dark:text-red-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <p className="text-red-600 dark:text-red-400 font-medium">
                {fetchError}
              </p>
            </div>
          ) : result ? (
            <div className="space-y-6">
              {result.auto_submitted && (
                <div className="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-700 text-yellow-800 dark:text-yellow-200 px-6 py-4 rounded-xl flex items-center gap-3">
                  <div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-4 h-4 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <span className="font-medium">
                    تم تسليم الامتحان تلقائٍ لانتهاء الوقت المحدد
                  </span>
                </div>
              )}

              {/* Results Summary */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-4 border border-green-200 dark:border-green-700">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                      <svg
                        className="w-5 h-5 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        الإجابات الصحيحة
                      </p>
                      <p className="text-xl font-bold text-gray-800 dark:text-white">
                        {result.correctCount} /{" "}
                        {result.totalQuestions || questions.length}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-4 border border-blue-200 dark:border-blue-700">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                      <svg
                        className="w-5 h-5 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                        />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        الدرجة النهائية
                      </p>
                      <p className="text-xl font-bold text-gray-800 dark:text-white">
                        {result.score} / {result.max_score}
                      </p>
                    </div>
                  </div>
                </div>

                <div
                  className={`bg-gradient-to-r rounded-xl p-4 border ${
                    result.passed
                      ? "from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-green-200 dark:border-green-700"
                      : "from-red-50 to-rose-50 dark:from-red-900/20 dark:to-rose-900/20 border-red-200 dark:border-red-700"
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                        result.passed ? "bg-green-500" : "bg-red-500"
                      }`}
                    >
                      <svg
                        className="w-5 h-5 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        {result.passed ? (
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        ) : (
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        )}
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        النتيجة
                      </p>
                      <p
                        className={`text-xl font-bold ${
                          result.passed
                            ? "text-green-600 dark:text-green-400"
                            : "text-red-600 dark:text-red-400"
                        }`}
                      >
                        {result.passed ? "نجح" : "لم ينجح"}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              {/* تفاصيل الإجابات */}
              <div className="mt-8">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-4 h-4 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
                      />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 dark:text-white">
                    تفاصيل الإجابات
                  </h3>
                </div>

                <div className="space-y-4">
                  {questions.map((q, idx) => {
                    const feedback = feedbackMap[q.id];
                    const isCorrect = feedback?.is_correct || false;
                    const choices = Array.isArray(q.choices)
                      ? q.choices
                      : Array.isArray(q.answers)
                      ? q.answers.map((a) => a.text)
                      : [];
                    const selectedIdx = answers[q.id];

                    // استخراج index الإجابة الصحيحة من feedback أو من الأسئلة
                    let correctIdx = null;
                    if (
                      feedback?.correct_answer_id &&
                      Array.isArray(q.answers)
                    ) {
                      correctIdx = q.answers.findIndex(
                        (a) => a.id === feedback.correct_answer_id
                      );
                    } else if (Array.isArray(q.answers)) {
                      correctIdx = q.answers.findIndex((a) => a.is_correct);
                    } else if (
                      Array.isArray(q.choices) &&
                      q.choices.findIndex
                    ) {
                      // لو choices فيها is_correct
                      correctIdx = q.choices.findIndex((c) => c.is_correct);
                    }
                    return (
                      <div
                        key={q.id}
                        className={`rounded-xl border-2 p-6 transition-all ${
                          isCorrect === true
                            ? "bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-green-300 dark:border-green-600"
                            : isCorrect === false
                            ? "bg-gradient-to-r from-red-50 to-rose-50 dark:from-red-900/20 dark:to-rose-900/20 border-red-300 dark:border-red-600"
                            : "bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                        }`}
                      >
                        <div className="flex items-start gap-4 mb-4">
                          <div
                            className={`w-8 h-8 rounded-lg flex items-center justify-center text-white font-bold ${
                              isCorrect === true
                                ? "bg-green-500"
                                : isCorrect === false
                                ? "bg-red-500"
                                : "bg-gray-500"
                            }`}
                          >
                            {idx + 1}
                          </div>
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-800 dark:text-white text-lg mb-2">
                              {q.title || q.text}
                            </h4>
                            {isCorrect === true && (
                              <div className="flex items-center gap-2 text-green-600 dark:text-green-400 font-medium">
                                <svg
                                  className="w-4 h-4"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                  />
                                </svg>
                                إجابة صحيحة
                              </div>
                            )}
                            {isCorrect === false && (
                              <div className="flex items-center gap-2 text-red-600 dark:text-red-400 font-medium">
                                <svg
                                  className="w-4 h-4"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                                  />
                                </svg>
                                إجابة خاطئة
                              </div>
                            )}
                          </div>
                        </div>
                        {/* عرض صورة السؤال في النتائج إذا كانت موجودة */}
                        {q.image_url && (
                          <div className="mb-4">
                            <img
                              src={q.image_url}
                              alt="صورة السؤال"
                              className="max-w-full h-48 object-contain border border-gray-200 dark:border-gray-600 rounded-lg"
                            />
                          </div>
                        )}

                        <div className="space-y-3">
                          {choices.map((choice, i) => {
                            const isSelected = selectedIdx === i;
                            const isRight = correctIdx === i;

                            return (
                              <div
                                key={i}
                                className={`flex items-center gap-3 p-3 rounded-lg border transition-all ${
                                  isSelected && isCorrect === true
                                    ? "bg-green-100 dark:bg-green-900/30 border-green-300 dark:border-green-600 text-green-800 dark:text-green-200"
                                    : isSelected && isCorrect === false
                                    ? "bg-red-100 dark:bg-red-900/30 border-red-300 dark:border-red-600 text-red-800 dark:text-red-200"
                                    : isCorrect === false && isRight
                                    ? "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700 text-green-700 dark:text-green-300"
                                    : "bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300"
                                }`}
                              >
                                <div
                                  className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                                    isSelected
                                      ? isCorrect === true
                                        ? "border-green-500 bg-green-500"
                                        : "border-red-500 bg-red-500"
                                      : isCorrect === false && isRight
                                      ? "border-green-500 bg-green-500"
                                      : "border-gray-300 dark:border-gray-500"
                                  }`}
                                >
                                  {(isSelected ||
                                    (isCorrect === false && isRight)) && (
                                    <div className="w-2 h-2 bg-white rounded-full"></div>
                                  )}
                                </div>
                                <span className="flex-1 font-medium">
                                  {choice}
                                </span>
                                {isSelected && isCorrect === true && (
                                  <div className="flex items-center gap-1 text-green-600 dark:text-green-400">
                                    <svg
                                      className="w-4 h-4"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                      />
                                    </svg>
                                    <span className="text-xs font-bold">
                                      اختيارك
                                    </span>
                                  </div>
                                )}
                                {isSelected && isCorrect === false && (
                                  <div className="flex items-center gap-1 text-red-600 dark:text-red-400">
                                    <svg
                                      className="w-4 h-4"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                                      />
                                    </svg>
                                    <span className="text-xs font-bold">
                                      اختيارك
                                    </span>
                                  </div>
                                )}
                                {isCorrect === false && isRight && (
                                  <div className="flex items-center gap-1 text-green-600 dark:text-green-400">
                                    <svg
                                      className="w-4 h-4"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                      />
                                    </svg>
                                    <span className="text-xs font-bold">
                                      الإجابة الصحيحة
                                    </span>
                                  </div>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Close Button */}
              <div className="mt-8 flex justify-center">
                <button
                  onClick={handleClose}
                  className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg"
                >
                  إغلاق
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Timer and Status */}
              <div className="flex justify-between items-center">
                {type === "exam" && timer > 0 && (
                  <div className="bg-gradient-to-r from-red-50 to-rose-50 dark:from-red-900/20 dark:to-rose-900/20 border border-red-200 dark:border-red-700 px-4 py-2 rounded-xl flex items-center gap-2">
                    <div className="w-6 h-6 bg-red-500 rounded-lg flex items-center justify-center">
                      <svg
                        className="w-3 h-3 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <span className="text-red-700 dark:text-red-300 font-bold">
                      الوقت المتبقي: {formatTime(timer)}
                    </span>
                  </div>
                )}
                {type === "assignment" && (
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-700 px-4 py-2 rounded-xl flex items-center gap-2">
                    <div className="w-6 h-6 bg-green-500 rounded-lg flex items-center justify-center">
                      <svg
                        className="w-3 h-3 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <span className="text-green-700 dark:text-green-300 font-bold">
                      واجب - وقت غير محدود
                    </span>
                  </div>
                )}
              </div>

              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  handleSubmit();
                }}
                className="space-y-6"
              >
                {questions.map((q, idx) => {
                  // دعم الأسئلة القادمة من الداتا (answers) أو من API (choices)
                  const choices = Array.isArray(q.choices)
                    ? q.choices
                    : Array.isArray(q.answers)
                    ? q.answers.map((a) => a.text)
                    : [];
                  return (
                    <div
                      key={q.id}
                      className="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-xl p-6 shadow-sm"
                    >
                      <div className="flex items-start gap-4 mb-4">
                        <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold">
                          {idx + 1}
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-800 dark:text-white text-lg">
                            {q.title || q.text}
                          </h4>
                        </div>
                      </div>

                      {/* عرض صورة السؤال إذا كانت موجودة */}
                      {q.image_url && (
                        <div className="mb-4">
                          <img
                            src={q.image_url}
                            alt="صورة السؤال"
                            className="max-w-full h-48 object-contain border border-gray-200 dark:border-gray-600 rounded-lg"
                          />
                        </div>
                      )}

                      <div className="space-y-3">
                        {choices.map((choice, i) => (
                          <label
                            key={i}
                            className="flex items-center gap-3 p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer transition-colors"
                          >
                            <div
                              className={`w-5 h-5 rounded-full border-2 flex items-center justify-center transition-colors ${
                                answers[q.id] === i
                                  ? "border-indigo-500 bg-indigo-500"
                                  : "border-gray-300 dark:border-gray-500 hover:border-indigo-400"
                              }`}
                            >
                              {answers[q.id] === i && (
                                <div className="w-2 h-2 bg-white rounded-full"></div>
                              )}
                            </div>
                            <input
                              type="radio"
                              name={`q_${q.id}`}
                              value={i}
                              checked={answers[q.id] === i}
                              onChange={() => handleChange(q.id, i)}
                              disabled={
                                !!result || (type === "exam" && timer === 0)
                              }
                              className="sr-only"
                            />
                            <span className="flex-1 text-gray-700 dark:text-gray-300 font-medium">
                              {choice}
                            </span>
                          </label>
                        ))}
                      </div>
                    </div>
                  );
                })}

                {/* Action Buttons */}
                <div className="flex gap-4 justify-center mt-8 pt-6 border-t border-gray-200 dark:border-gray-600">
                  <button
                    type="submit"
                    className={`px-8 py-3 rounded-xl font-medium transition-all duration-300 ${
                      submitting ||
                      (type === "exam" && timer === 0 && duration > 0) ||
                      result
                        ? "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed"
                        : "bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-lg hover:scale-105"
                    }`}
                    disabled={
                      submitting ||
                      (type === "exam" && timer === 0 && duration > 0) ||
                      result
                    }
                  >
                    {submitting ? (
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        جاري التسليم...
                      </div>
                    ) : (
                      <>
                        {type === "assignment"
                          ? "تسليم الواجب"
                          : "تسليم الامتحان"}
                      </>
                    )}
                  </button>
                  <button
                    type="button"
                    className="bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-8 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 disabled:opacity-50"
                    onClick={handleClose}
                    disabled={submitting}
                  >
                    إغلاق
                  </button>
                </div>
              </form>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default function CoursePage() {
  const theUrl = "https://res.cloudinary.com/djwhgnwp5/";

  const { id } = useParams();
  const router = useRouter();
  const [course, setCourse] = useState(null);
  const [lessons, setLessons] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const user = useSelector(selectCurrentUser);
  const [isEnrolled, setIsEnrolled] = useState(false);
  const [previewVideoUrl, setPreviewVideoUrl] = useState(null);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [previewError, setPreviewError] = useState(null);
  const [selectedLesson, setSelectedLesson] = useState(null);
  const [player, setPlayer] = useState(null);
  const videoRef = useRef(null);
  const [isClient, setIsClient] = useState(false);

  // Dynamic Watermark Hook - زكي الخولي
  const {
    isWatermarkVisible,
    watermarkConfig,
    toggleWatermark,
    getWatermarkText,
    isUserDataValid,
  } = useDynamicWatermark(user, selectedLesson && !selectedLesson.is_preview);

  // حل مؤقت للاختبار - زكي الخولي
  const [forceWatermark, setForceWatermark] = useState(false);

  useEffect(() => {
    if (selectedLesson && !selectedLesson.is_preview && user) {
      setForceWatermark(true);
    } else {
      setForceWatermark(false);
    }
  }, [selectedLesson, user]);

  // حالات تتبع التقدم - zaki alkholy
  const [watchProgress, setWatchProgress] = useState({});
  const [lastSavedTime, setLastSavedTime] = useState(0);
  const [progressSaveInterval, setProgressSaveInterval] = useState(null);
  const [reviews, setReviews] = useState([]);
  const [reviewLoading, setReviewLoading] = useState(false);
  const [reviewError, setReviewError] = useState(null);
  const [reviewSuccess, setReviewSuccess] = useState(null);
  const { register, handleSubmit, reset } = useForm();

  // حالات التعليقات الشجرية - zaki alkholy
  const [reviewComments, setReviewComments] = useState({});
  const [commentText, setCommentText] = useState({});
  const [commentLoading, setCommentLoading] = useState({});
  const [replyText, setReplyText] = useState({});
  const [showReplyForm, setShowReplyForm] = useState({});
  const [expandedComments, setExpandedComments] = useState({});

  // إضافة حالة الإعجاب وعدد الإعجابات
  const [isLiked, setIsLiked] = useState(false);
  const [likesCount, setLikesCount] = useState(0);
  // إضافة حالة تحميل زر الإعجاب
  const [likeLoading, setLikeLoading] = useState(false);

  // إضافة ref لمنع إعادة تعيين isLiked إلا عند أول تحميل الكورس
  const didInitLike = useRef(false);

  // Reset states when course ID changes
  useEffect(() => {
    setCourse(null);
    setLessons([]);
    setLoading(true);
    setError(null);
    setIsEnrolled(false);
    setPreviewVideoUrl(null);
    setPreviewLoading(false);
    setPreviewError(null);
    setSelectedLesson(null);
    setPlayer(null);
    setIsLiked(false);
    setLikesCount(0);
  }, [id]);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // دالة getImageUrl تم حذفها لأنها غير مستخدمة - zaki alkholy

  const getProfileImageUrl = (path) => {
    if (!path) return null;

    if (path.startsWith("http://") || path.startsWith("https://")) {
      return path;
    }

    return `${API_BASE_URL}${path.startsWith("/") ? path : `/${path}`}`;
  };

  // جلب بيانات الكورس
  useEffect(() => {
    const controller = new AbortController();
    const loadCourseData = async () => {
      try {
        const token = Cookies.get("authToken");
        if (!token) {
          setError("يرجى تسجيل الدخول للوصول إلى هذه الصفحة");
          router.push("/login");
          return;
        }
        const courseData = await fetchStudentCourse(
          id,
          token,
          controller.signal
        );
        const lessonsData = await fetchCourseLessons(
          id,
          token,
          controller.signal
        );
        setCourse(courseData);
        setLessons(lessonsData);
        if (user && courseData.students) {
          const enrolled = courseData.students.some((s) => s.id === user.id);
          setIsEnrolled(enrolled);
        } else {
          setIsEnrolled(false);
        }
        if (!didInitLike.current) {
          setIsLiked(!!courseData.is_liked);
          setLikesCount(courseData.likes_count || 0);
          didInitLike.current = true;
        }
        setLoading(false);
      } catch (err) {
        if (err.name === "AbortError") return;
        if (!controller.signal.aborted) {
          if (err.response?.status === 401) {
            Cookies.remove("authToken");
            setError("انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى");
            router.push("/login");
          } else {
            setError(
              err.response?.data?.message || "حدث خطأ أثناء تحميل الكورس"
            );
          }
          setLoading(false);
        }
      }
    };
    loadCourseData();
    return () => controller.abort();
  }, [id, router, user]);

  // إعادة تعيين ref عند تغيير الكورس
  useEffect(() => {
    didInitLike.current = false;
  }, [id]);

  // دالة لحفظ تقدم مشاهدة الفيديو - zaki alkholy
  const saveWatchProgress = async (
    lessonId,
    currentTime,
    duration,
    forceUpdate = false
  ) => {
    if (!user || !isEnrolled) return;

    try {
      const token = Cookies.get("authToken");
      if (!token) return;

      const progressPercentage = Math.round((currentTime / duration) * 100);

      // التحقق من الحاجة لحفظ التقدم - zaki alkholy
      const currentProgress = watchProgress[lessonId];
      const lastSavedPercentage = currentProgress?.progressPercentage || 0;
      const lastSavedTime = currentProgress?.watchedDuration || 0;

      // حفظ التقدم فقط في الحالات التالية:
      // 1. إجبار التحديث (forceUpdate = true)
      // 2. تغيير كبير في النسبة المئوية (5% أو أكثر)
      // 3. تغيير كبير في الوقت (30 ثانية أو أكثر)
      // 4. وصول لنهاية الفيديو (100%)
      const percentageDiff = Math.abs(progressPercentage - lastSavedPercentage);
      const timeDiff = Math.abs(currentTime - lastSavedTime);

      const shouldSave =
        forceUpdate ||
        percentageDiff >= 5 ||
        timeDiff >= 30 ||
        progressPercentage >= 100 ||
        lastSavedPercentage === 0; // أول مرة

      if (!shouldSave) {
        // تحديث الحالة المحلية فقط بدون إرسال للباك اند
        setWatchProgress((prev) => ({
          ...prev,
          [lessonId]: {
            watchedDuration: Math.round(currentTime),
            progressPercentage,
            lastPosition: Math.round(currentTime),
          },
        }));
        return;
      }

      // حفظ التقدم في الباك اند - zaki alkholy
      const response = await axios.post(
        `${API_BASE_URL}/api/student-progress/update_progress/`,
        {
          lesson_id: lessonId,
          watch_time: Math.round(currentTime),
          completion_percentage: progressPercentage,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      // تحديث الحالة المحلية - zaki alkholy
      setWatchProgress((prev) => ({
        ...prev,
        [lessonId]: {
          watchedDuration: Math.round(currentTime),
          progressPercentage,
          lastPosition: Math.round(currentTime),
          lastSaved: new Date().toISOString(),
        },
      }));
    } catch (error) {
      console.error("خطأ في حفظ تقدم المشاهدة - zaki alkholy:", error);
    }
  };

  // دالة لجلب تقدم مشاهدة الفيديو - zaki alkholy
  const fetchWatchProgress = async (lessonId) => {
    if (!user || !isEnrolled) return null;

    try {
      const token = Cookies.get("authToken");
      if (!token) return null;

      const response = await axios.get(
        `${API_BASE_URL}/api/student-progress/?lesson=${lessonId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      const progressData = response.data.results?.[0] || response.data;
      if (progressData) {
        setWatchProgress((prev) => ({
          ...prev,
          [lessonId]: {
            watchedDuration: progressData.watch_time || 0,
            progressPercentage: progressData.completion_percentage || 0,
            lastPosition: progressData.watch_time || 0,
          },
        }));
        return progressData;
      }
    } catch (error) {
      console.error("خطأ في جلب تقدم المشاهدة - zaki alkholy:", error);
    }
    return null;
  };

  // دالة لإعداد تتبع التقدم للمشغل - zaki alkholy
  const setupProgressTracking = (playerInstance, lesson) => {
    if (!lesson || !isEnrolled) return;

    const lessonId = lesson.id;

    // استرجاع آخر موضع محفوظ - zaki alkholy
    const savedProgress = watchProgress[lessonId];
    if (savedProgress && savedProgress.lastPosition > 0) {
      playerInstance.currentTime = savedProgress.lastPosition;
      console.log(
        `تم استرجاع الموضع المحفوظ: ${savedProgress.lastPosition}s - zaki alkholy`
      );
    }

    // تتبع التقدم كل 10 ثوان - zaki alkholy
    const interval = setInterval(() => {
      if (playerInstance && !playerInstance.paused) {
        const currentTime = playerInstance.currentTime;
        const duration = playerInstance.duration;

        if (currentTime > 0 && duration > 0) {
          // حفظ التقدم بذكاء حسب التغييرات المهمة - zaki alkholy
          saveWatchProgress(lessonId, currentTime, duration);
          setLastSavedTime(currentTime);
        }
      }
    }, 10000); // كل 10 ثوان

    setProgressSaveInterval(interval);

    // حفظ التقدم عند الإيقاف المؤقت - zaki alkholy
    playerInstance.on("pause", () => {
      const currentTime = playerInstance.currentTime;
      const duration = playerInstance.duration;
      if (currentTime > 0 && duration > 0) {
        saveWatchProgress(lessonId, currentTime, duration, true); // إجبار الحفظ عند الإيقاف
        setLastSavedTime(currentTime);
      }
    });

    // حفظ التقدم عند انتهاء الفيديو - zaki alkholy
    playerInstance.on("ended", () => {
      const duration = playerInstance.duration;
      if (duration > 0) {
        saveWatchProgress(lessonId, duration, duration, true); // إجبار الحفظ عند الانتهاء
        setLastSavedTime(duration);
      }
    });

    // حفظ التقدم عند التنقل في الفيديو - zaki alkholy
    playerInstance.on("seeked", () => {
      const currentTime = playerInstance.currentTime;
      const duration = playerInstance.duration;
      if (currentTime > 0 && duration > 0) {
        saveWatchProgress(lessonId, currentTime, duration, true); // إجبار الحفظ عند التنقل
        setLastSavedTime(currentTime);
      }
    });
  };

  const handleVideo = async (lesson) => {
    console.log("بدء تشغيل الفيديو للدرس - zaki alkholy:", lesson);
    setPreviewLoading(true);
    setPreviewError(null);
    setSelectedLesson(lesson);

    try {
      const token = Cookies.get("authToken");
      if (!token) {
        setPreviewError("يرجى تسجيل الدخول أولاً");
        return;
      }

      // التحقق من الصلاحيات قبل جلب الفيديو - zaki alkholy
      if (!lesson.is_preview && !isEnrolled) {
        setPreviewError("يجب عليك الاشتراك في الكورس لمشاهدة هذا الفيديو");
        return;
      }

      const headers = {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        Accept: "application/json",
      };

      console.log(
        "جلب رابط الفيديو الآمن - نظام مختلط - زكي الخولي:",
        lesson.id
      );
      const response = await axios.get(
        `${API_BASE_URL}/api/lessons/${lesson.id}/video_url/`,
        {
          headers,
          timeout: 15000, // زيادة timeout للأمان
          validateStatus: function (status) {
            return status >= 200 && status < 500;
          },
        }
      );

      if (response.data.video_url) {
        // تسجيل معلومات الفيديو المفصلة - زكي الخولي
        console.log("تم جلب رابط الفيديو - زكي الخولي:", {
          url: response.data.video_url,
          platform: response.data.platform,
          video_type: response.data.video_type,
          security_level: response.data.security_level,
          watermark: response.data.watermark,
          expires_in: response.data.expires_in,
        });

        setPreviewVideoUrl(response.data.video_url);

        // جلب تقدم المشاهدة المحفوظ - zaki alkholy
        if (isEnrolled) {
          console.log("جلب تقدم المشاهدة للدرس - zaki alkholy:", lesson.id);
          await fetchWatchProgress(lesson.id);
        }
      } else {
        console.error(
          "لم يتم العثور على رابط الفيديو في الاستجابة - zaki alkholy:",
          response.data
        );
        setPreviewError("لم يتم العثور على رابط الفيديو");
      }
    } catch (err) {
      console.error("خطأ في جلب الفيديو - zaki alkholy:", err);

      if (err.code === "ECONNABORTED") {
        setPreviewError("انتهت مهلة الاتصال بالخادم");
      } else if (err.response) {
        console.error("Error response:", err.response.data);
        console.error("Error status:", err.response.status);

        if (err.response.status === 403) {
          setPreviewError(
            "ليس لديك صلاحية لمشاهدة هذا الفيديو. يرجى الاشتراك في الكورس أولاً."
          );
        } else if (err.response.status === 401) {
          setPreviewError("انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.");
          Cookies.remove("authToken");
          router.push("/login");
        } else if (err.response.status === 404) {
          setPreviewError("الفيديو غير متوفر");
        } else {
          setPreviewError(`خطأ في الخادم: ${err.response.status}`);
        }
      } else if (err.request) {
        console.error("Error request:", err.request);
        setPreviewError("لا يمكن الاتصال بالخادم");
      } else {
        console.error("Error message:", err.message);
        setPreviewError("حدث خطأ أثناء تحميل الفيديو");
      }
    } finally {
      setPreviewLoading(false);
    }
  };

  // حماية من keyboard shortcuts - zaki alkholy
  useEffect(() => {
    const handleKeyDown = (e) => {
      // منع Ctrl+S, Ctrl+Shift+I, F12, Ctrl+U
      if (
        (e.ctrlKey && e.key === "s") ||
        (e.ctrlKey && e.shiftKey && e.key === "I") ||
        e.key === "F12" ||
        (e.ctrlKey && e.key === "u")
      ) {
        e.preventDefault();
        console.log("محاولة استخدام keyboard shortcut محظورة - zaki alkholy");
      }
    };

    if (previewVideoUrl) {
      document.addEventListener("keydown", handleKeyDown);
      return () => document.removeEventListener("keydown", handleKeyDown);
    }
  }, [previewVideoUrl]);

  // تهيئة مشغل الفيديو المحمي مع HLS - zaki alkholy
  useEffect(() => {
    console.log("useEffect للفيديو - zaki alkholy:", {
      isClient,
      previewVideoUrl,
      hasVideoRef: !!videoRef.current,
    });

    if (!isClient || !previewVideoUrl) {
      console.log("شروط useEffect غير مكتملة - zaki alkholy");
      return;
    }

    // انتظار قصير للتأكد من أن عنصر الفيديو جاهز - zaki alkholy
    const initializeVideo = () => {
      if (!videoRef.current) {
        console.log("عنصر الفيديو غير جاهز، إعادة المحاولة - zaki alkholy");
        setTimeout(initializeVideo, 100);
        return;
      }

      console.log("عنصر الفيديو جاهز، بدء التهيئة - zaki alkholy");

      // تنظيف المشغل السابق
      if (player) {
        console.log("تنظيف المشغل السابق - zaki alkholy");
        player.destroy();
        setPlayer(null);
      }

      const video = videoRef.current;
      video.crossOrigin = "anonymous";

      console.log("تهيئة مشغل الفيديو المختلط - زكي الخولي:", previewVideoUrl);

      // تحديد نوع الفيديو من الرابط - زكي الخولي
      const isBunnyStream =
        previewVideoUrl.includes("mediadelivery.net") ||
        previewVideoUrl.includes("b-cdn.net");
      const isCloudinary = previewVideoUrl.includes("cloudinary.com");

      console.log("نوع الفيديو للطالب - زكي الخولي:", {
        isBunnyStream,
        isCloudinary,
        url: previewVideoUrl,
      });

      const initSecurePlayer = async () => {
        try {
          if (isBunnyStream) {
            // Bunny Stream - فيديو محمي مع watermark شخصي - زكي الخولي
            console.log("تشغيل فيديو Bunny Stream محمي للطالب - زكي الخولي");

            if (previewVideoUrl.includes("/embed/")) {
              // إذا كان iframe URL، نستخدمه مباشرة
              const iframe = document.createElement("iframe");
              iframe.src = previewVideoUrl;
              iframe.style.width = "100%";
              iframe.style.height = "100%";
              iframe.style.border = "none";
              iframe.allow = "autoplay; fullscreen";
              iframe.allowFullscreen = true;

              // استبدال عنصر الفيديو بـ iframe
              video.parentNode.replaceChild(iframe, video);
              console.log("تم تحميل Bunny Stream iframe - زكي الخولي");
              return;
            } else {
              // رابط مباشر للفيديو
              video.src = previewVideoUrl;
              video.addEventListener("loadedmetadata", async () => {
                const PlyrModule = await import("plyr");
                const newPlayer = new PlyrModule.default(video, {
                  controls: [
                    "play-large",
                    "play",
                    "progress",
                    "current-time",
                    "mute",
                    "volume",
                    "fullscreen",
                  ],
                  download: false, // منع التحميل - زكي الخولي
                  disableContextMenu: true, // منع القائمة اليمنى - زكي الخولي
                  keyboard: { focused: false, global: false }, // تعطيل اختصارات لوحة المفاتيح - زكي الخولي
                });
                setPlayer(newPlayer);
              });
            }
          } else if (isCloudinary || Hls.isSupported()) {
            // Cloudinary أو HLS عادي - زكي الخولي
            console.log("استخدام HLS.js للفيديو الترويجي - زكي الخولي");
            const hls = new Hls({
              // إعدادات الأمان - zaki alkholy
              enableWorker: true,
              lowLatencyMode: false,
              backBufferLength: 90,
              maxBufferLength: 30,
              maxMaxBufferLength: 600,
            });

            hls.loadSource(previewVideoUrl);
            hls.attachMedia(video);

            hls.on(Hls.Events.MANIFEST_PARSED, async () => {
              console.log("تم تحليل manifest بنجاح - zaki alkholy");
              // تهيئة Plyr بعد تحميل HLS
              const PlyrModule = await import("plyr");
              const newPlayer = new PlyrModule.default(video, {
                controls: [
                  "play-large",
                  "play",
                  "progress",
                  "current-time",
                  "mute",
                  "volume",
                  "settings",
                  "fullscreen",
                ],
                download: false, // منع التحميل - zaki alkholy
                hideControls: true,
                keyboard: { focused: true, global: true },
                tooltips: { controls: true, seek: true },
                quality: {
                  default: 720,
                  options: [1080, 720, 480, 360],
                },
                // إعدادات إضافية للحماية - zaki alkholy
                clickToPlay: true,
                disableContextMenu: true,
              });

              setPlayer(newPlayer);

              // إضافة تتبع التقدم للمشغل - zaki alkholy
              if (selectedLesson) {
                setupProgressTracking(newPlayer, selectedLesson);
              }
            });

            hls.on(Hls.Events.ERROR, (_, data) => {
              console.error("خطأ في HLS - zaki alkholy:", data);
              if (data.fatal) {
                switch (data.type) {
                  case Hls.ErrorTypes.NETWORK_ERROR:
                    setPreviewError("خطأ في الشبكة أثناء تشغيل الفيديو");
                    break;
                  case Hls.ErrorTypes.MEDIA_ERROR:
                    setPreviewError("خطأ في ملف الفيديو");
                    break;
                  default:
                    setPreviewError("حدث خطأ أثناء تشغيل الفيديو");
                    break;
                }
              }
            });
          } else if (video.canPlayType("application/vnd.apple.mpegurl")) {
            // Safari native HLS support
            console.log("استخدام HLS الأصلي في Safari - zaki alkholy");
            video.src = previewVideoUrl;

            video.addEventListener("loadedmetadata", async () => {
              const PlyrModule = await import("plyr");
              const newPlayer = new PlyrModule.default(video, {
                controls: [
                  "play-large",
                  "play",
                  "progress",
                  "current-time",
                  "mute",
                  "volume",
                  "settings",
                  "fullscreen",
                ],
                download: false,
                hideControls: true,
                keyboard: { focused: true, global: true },
                tooltips: { controls: true, seek: true },
                disableContextMenu: true,
              });

              setPlayer(newPlayer);

              // إضافة تتبع التقدم للمشغل - zaki alkholy
              if (selectedLesson) {
                setupProgressTracking(newPlayer, selectedLesson);
              }
            });
          } else {
            setPreviewError("المتصفح لا يدعم تشغيل فيديوهات HLS المحمية");
          }

          // معالجة أخطاء الفيديو
          video.addEventListener("error", () => {
            setPreviewError("حدث خطأ أثناء تشغيل الفيديو");
          });
        } catch (error) {
          console.error("خطأ في تهيئة المشغل - zaki alkholy:", error);
          setPreviewError("فشل في تهيئة مشغل الفيديو");
        }
      };

      initSecurePlayer();
    };

    // بدء التهيئة
    initializeVideo();

    return () => {
      if (player) {
        player.destroy();
      }
      // تنظيف interval تتبع التقدم - zaki alkholy
      if (progressSaveInterval) {
        clearInterval(progressSaveInterval);
        setProgressSaveInterval(null);
      }
    };
  }, [previewVideoUrl, isClient]);

  // جلب التعليقات عند تحميل الصفحة أو تغيير الكورس
  useEffect(() => {
    if (!id) return;
    setReviewLoading(true);
    fetchCourseReviews(id)
      .then(async (reviewsData) => {
        setReviews(reviewsData);

        // جلب التعليقات لكل تقييم - zaki alkholy
        for (const review of reviewsData) {
          try {
            const comments = await fetchReviewComments(review.id);
            setReviewComments((prev) => ({ ...prev, [review.id]: comments }));
          } catch (err) {
            console.error(
              `Failed to load comments for review ${review.id}:`,
              err
            );
            setReviewComments((prev) => ({ ...prev, [review.id]: [] }));
          }
        }
      })
      .catch(() => setReviews([]))
      .finally(() => setReviewLoading(false));
  }, [id]);

  // إرسال تعليق جديد
  const onSubmitReview = async (data) => {
    setReviewError(null);
    setReviewSuccess(null);
    try {
      const token = Cookies.get("authToken");
      await submitCourseReview(id, data, token);
      setReviewSuccess("تم إرسال التقييم بنجاح وسيظهر بعد المراجعة.");
      reset();
      const res = await fetchCourseReviews(id);
      setReviews(res);
    } catch (err) {
      setReviewError("حدث خطأ أثناء إرسال التقييم");
    }
  };

  // دوال التعامل مع التعليقات الشجرية - zaki alkholy
  const handleAddComment = async (reviewId, parentId = null) => {
    const text = parentId
      ? replyText[`${reviewId}-${parentId}`]
      : commentText[reviewId];
    if (!text?.trim()) return;

    setCommentLoading((prev) => ({ ...prev, [reviewId]: true }));
    try {
      const token = Cookies.get("authToken");
      await addReviewReply(reviewId, parentId, text, token);

      // إعادة جلب التعليقات
      const comments = await fetchReviewComments(reviewId);
      setReviewComments((prev) => ({ ...prev, [reviewId]: comments }));

      // مسح النص
      if (parentId) {
        setReplyText((prev) => ({ ...prev, [`${reviewId}-${parentId}`]: "" }));
        setShowReplyForm((prev) => ({
          ...prev,
          [`${reviewId}-${parentId}`]: false,
        }));
      } else {
        setCommentText((prev) => ({ ...prev, [reviewId]: "" }));
      }
    } catch (err) {
      console.error("Error adding comment:", err);
    } finally {
      setCommentLoading((prev) => ({ ...prev, [reviewId]: false }));
    }
  };

  const toggleReplyForm = (reviewId, parentId = null) => {
    const key = parentId ? `${reviewId}-${parentId}` : reviewId;
    setShowReplyForm((prev) => ({ ...prev, [key]: !prev[key] }));
  };

  const toggleExpandComments = (reviewId) => {
    setExpandedComments((prev) => ({ ...prev, [reviewId]: !prev[reviewId] }));
  };

  // دالة تبديل الإعجاب
  const handleToggleLike = async () => {
    if (!user) {
      router.push("/login");
      return;
    }
    try {
      setLikeLoading(true);
      const token = Cookies.get("authToken");
      const res = await toggleCourseLike(id, token);
      setIsLiked(res.is_liked ?? res.liked ?? false);
      setLikesCount(res.likes_count ?? res.likesCount ?? 0);
    } catch (err) {
      console.error("toggle_like error:", err);
    } finally {
      setLikeLoading(false);
    }
  };

  // دالة جلب التعليقات الشجرية (مستخدمة في useEffect أو عند تحديث التعليقات)
  const fetchReviewCommentsHandler = async (reviewId) => {
    try {
      return await fetchReviewComments(reviewId);
    } catch {
      return [];
    }
  };

  // دالة إعجاب/إلغاء إعجاب للتعليق أو الرد
  const handleToggleCommentLike = async (commentId) => {
    try {
      const token = Cookies.get("authToken");
      const res = await toggleCommentLike(commentId, token);
      setReviews((prev) =>
        prev.map((review) => ({
          ...review,
          comments:
            review.comments?.map((c) =>
              updateCommentLikeRecursive(c, commentId, res)
            ) || [],
        }))
      );
    } catch (err) {
      // يمكن عرض رسالة خطأ
    }
  };

  // دالة إضافة رد
  const handleAddReply = async (parentId, replyText, reviewId) => {
    try {
      const token = Cookies.get("authToken");
      await addReviewReply(reviewId, parentId, replyText, token);
      const comments = await fetchReviewCommentsHandler(reviewId);
      setReviews((prev) =>
        prev.map((review) =>
          review.id === reviewId ? { ...review, comments } : review
        )
      );
    } catch (err) {
      // يمكن عرض رسالة خطأ
    }
  };

  // دالة تحميل الملف مع إخفاء رابط Cloudinary - zaki alkholy
  const handleFileDownload = async (fileUrl, fileName = "lesson-file") => {
    try {
      // إظهار مؤشر التحميل
      const downloadButton = document.activeElement;
      const originalText = downloadButton.textContent;
      downloadButton.textContent = "جاري التحميل...";
      downloadButton.disabled = true;

      // جلب الملف من Cloudinary
      const response = await fetch(fileUrl, {
        method: "GET",
        headers: {
          Accept: "*/*",
        },
      });

      if (!response.ok) {
        throw new Error("فشل في تحميل الملف");
      }

      // تحويل الاستجابة إلى blob
      const blob = await response.blob();

      // استخراج اسم الملف من URL أو استخدام اسم افتراضي
      const urlParts = fileUrl.split("/");
      const fileNameFromUrl = urlParts[urlParts.length - 1];
      const finalFileName = fileNameFromUrl.includes(".")
        ? fileNameFromUrl
        : `${fileName}.pdf`;

      // إنشاء رابط تحميل مؤقت
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = finalFileName;

      // إضافة الرابط للصفحة وتفعيله ثم حذفه
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // تنظيف الذاكرة
      window.URL.revokeObjectURL(downloadUrl);

      // إعادة تعيين النص الأصلي للزر
      downloadButton.textContent = originalText;
      downloadButton.disabled = false;
    } catch (error) {
      console.error("خطأ في تحميل الملف - zaki alkholy:", error);
      alert("حدث خطأ أثناء تحميل الملف. يرجى المحاولة مرة أخرى.");

      // إعادة تعيين النص الأصلي للزر في حالة الخطأ
      const downloadButton = document.activeElement;
      if (downloadButton) {
        downloadButton.textContent = "تحميل ملف الدرس";
        downloadButton.disabled = false;
      }
    }
  };

  // مكون عرض التعليقات الشجرية
  function CommentTree({ comments, reviewId }) {
    const [replyingTo, setReplyingTo] = useState(null);
    const [replyText, setReplyText] = useState("");
    return (
      <div>
        {comments?.map((comment) => (
          <div key={comment.id} className="mt-2 p-2 bg-gray-100 rounded ml-2">
            <div className="flex justify-between items-center">
              <span className="text-gray-800">
                {comment.user?.username || "مستخدم"}: {comment.text}
              </span>
              <div className="flex items-center gap-2">
                <button
                  className={`text-sm ${
                    comment.is_liked ? "text-blue-600" : "text-gray-600"
                  }`}
                  onClick={() => handleToggleCommentLike(comment.id)}
                >
                  👍 {comment.likes_count || 0}
                </button>
                <button
                  className="text-sm text-green-600"
                  onClick={() => setReplyingTo(comment.id)}
                >
                  رد
                </button>
              </div>
            </div>
            {replyingTo === comment.id && (
              <form
                className="flex gap-2 mt-2"
                onSubmit={(e) => {
                  e.preventDefault();
                  if (replyText.trim()) {
                    handleAddReply(comment.id, replyText, reviewId);
                    setReplyText("");
                    setReplyingTo(null);
                  }
                }}
              >
                <input
                  className="border rounded p-1 flex-1"
                  value={replyText}
                  onChange={(e) => setReplyText(e.target.value)}
                  placeholder="اكتب ردك..."
                />
                <button
                  type="submit"
                  className="bg-primary text-white px-2 rounded"
                >
                  إرسال
                </button>
                <button
                  type="button"
                  className="text-gray-500"
                  onClick={() => setReplyingTo(null)}
                >
                  إلغاء
                </button>
              </form>
            )}
            {/* عرض الردود بشكل شجري */}
            {comment.replies && comment.replies.length > 0 && (
              <CommentTree comments={comment.replies} reviewId={reviewId} />
            )}
          </div>
        ))}
      </div>
    );
  }

  // إضافة state للمودال (يجب أن تكون داخل دالة CoursePage)
  const [modalOpen, setModalOpen] = useState(false);
  const [modalType, setModalType] = useState(null); // 'exam' | 'assignment'
  const [modalId, setModalId] = useState(null);
  const [modalDuration, setModalDuration] = useState(0);
  const [modalQuestions, setModalQuestions] = useState([]); // state جديد للأسئلة الممررة للمودال

  // تعديل فتح مودال الامتحان لتنفيذ المتطلبات - zaki alkholy
  const handleOpenExamModal = async (examIdParam) => {
    console.log("1. زر حل الامتحان تم الضغط عليه - zaki alkholy");
    try {
      const token = Cookies.get("authToken");
      const usedExamId = examIdParam || examId;
      console.log("examId:", usedExamId);
      console.log("token:", token);
      if (token && usedExamId) {
        // 1. تحقق من حالة الامتحان أولاً - zaki alkholy
        const statusRes = await fetchExamStatus(usedExamId, token);
        console.log("zaki alkholy - fetchExamStatus response:", statusRes);

        if (statusRes.status === "submitted") {
          // 7. الامتحان اتسلم خلاص، اعرض النتيجة مع إجابات الطالب - zaki alkholy
          console.log("7. الامتحان مُسلم، سيتم عرض النتيجة - zaki alkholy");
          setModalType("exam");
          setModalId(usedExamId);
          setModalDuration(0); // مافيش وقت لأن الامتحان خلص
          setModalQuestions(statusRes.questions || []);
          setModalOpen(true);
        } else {
          // الامتحان لسه مش اتسلم، ابدأ الامتحان أو استكمل - zaki alkholy
          try {
            // 1. استدعاء start endpoint لتسجيل دخول الطالب في وقت كذا - zaki alkholy
            const startRes = await startExam(usedExamId, token);
            console.log(
              "1. تم تسجيل دخول الطالب للامتحان - zaki alkholy:",
              startRes
            );
          } catch (startErr) {
            if (startErr?.response?.status === 403) {
              // الامتحان تم تسليمه بالفعل
              console.log("zaki alkholy - الامتحان تم تسليمه بالفعل");
              const updatedStatus = await fetchExamStatus(usedExamId, token);
              setModalType("exam");
              setModalId(usedExamId);
              setModalDuration(0);
              setModalQuestions(updatedStatus.questions || []);
              setModalOpen(true);
              return;
            }
          }

          // جلب الوقت المتبقي - zaki alkholy
          const res = await fetchExamTimeLeft(usedExamId, token);
          console.log("zaki alkholy - fetchExamTimeLeft response:", res);
          console.log("zaki alkholy - res.questions:", res.questions);

          if (res.status === "auto_submitted") {
            // 5. تم التسليم التلقائي في الباك إند، اعرض النتيجة - zaki alkholy
            alert(res.message);
            // إعادة جلب حالة الامتحان لعرض النتيجة
            const updatedStatus = await fetchExamStatus(usedExamId, token);
            setModalType("exam");
            setModalId(usedExamId);
            setModalDuration(0);
            setModalQuestions(updatedStatus.questions || []);
            setModalOpen(true);
          } else if (res.time_left === -1) {
            // واجب بوقت غير محدود - zaki alkholy
            console.log("2. فتح الواجب بوقت غير محدود - zaki alkholy");
            setModalType("assignment");
            setModalId(usedExamId);
            setModalDuration(-1); // وقت غير محدود
            setModalQuestions(res.questions || []);
            setModalOpen(true);
          } else if (res.time_left > 0) {
            console.log(
              "2. فتح الامتحان مع الوقت المتبقي - zaki alkholy:",
              res.time_left
            );
            // 2. إظهار المودال للطالب لبدء الامتحان - zaki alkholy
            setModalType("exam");
            setModalId(usedExamId);
            setModalDuration(res.time_left); // الوقت المتبقي الصحيح - zaki alkholy
            setModalQuestions(res.questions || []);
            setModalOpen(true);
          } else {
            alert("انتهى وقت الامتحان أو لا يمكنك الدخول.");
          }
        }
      }
    } catch (e) {
      alert(
        e?.response?.data?.error || "لا يمكنك دخول الامتحان أو انتهى الوقت."
      );
    }
  };

  // إضافة حالة الامتحان examStatus في CoursePage
  const [examStatus, setExamStatus] = useState(null);

  // جلب حالة الامتحان عند تحميل الصفحة أو تغيير الكورس
  const examId = course?.exam_id;
  const assignmentId = course?.assignment_id;
  useEffect(() => {
    if (!examId) return;
    const fetchStatus = async () => {
      try {
        const token = Cookies.get("authToken");
        if (!token) return;
        const status = await fetchExamStatus(examId, token);
        setExamStatus(status);
      } catch (e) {
        console.error("تعذر جلب حالة الامتحان - zaki alkholy:", e);
      }
    };
    fetchStatus();
  }, [examId]);

  // حالة كل كويز لكل درس
  const [quizStatuses, setQuizStatuses] = useState({});

  // جلب حالة كل كويز عند تحميل الدروس
  useEffect(() => {
    if (!lessons || !user) return;
    const token = Cookies.get("authToken");
    const fetchStatuses = async () => {
      const statuses = {};
      for (const lesson of lessons) {
        if (lesson.quizzes && lesson.quizzes.length > 0) {
          for (const quiz of lesson.quizzes) {
            try {
              const res = await fetchExamStatus(quiz.id, token);
              statuses[quiz.id] = res;
            } catch (e) {
              statuses[quiz.id] = null;
            }
          }
        }
      }
      setQuizStatuses(statuses);
    };
    fetchStatuses();
  }, [lessons, user]);

  // 6. دالة لتحديث حالة الامتحان بعد التسليم - zaki alkholy
  const handleExamSubmitted = async (quizId) => {
    try {
      const token = Cookies.get("authToken");
      if (!token) return;

      console.log("6. تحديث حالة الامتحان بعد التسليم - zaki alkholy:", quizId);
      // 6. تحديث حالة الكويز المحدد لتغيير الزر من "حل الامتحان" إلى "إظهار النتيجة" - zaki alkholy
      const updatedStatus = await fetchExamStatus(quizId, token);
      setQuizStatuses((prev) => ({
        ...prev,
        [quizId]: updatedStatus,
      }));

      // إذا كان هذا هو الامتحان الرئيسي للكورس، حدث examStatus
      if (quizId === examId) {
        setExamStatus(updatedStatus);
      }
      console.log("6. تم تحديث حالة الامتحان بنجاح - zaki alkholy");
    } catch (e) {
      console.error("6. خطأ في تحديث حالة الامتحان - zaki alkholy:", e);
    }
  };

  if (loading) {
    return <PageLoader text="جاري تحميل الكورس..." />;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-white via-gray-50 to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 transition-colors duration-300 flex items-center justify-center">
        <div className="text-center bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 max-w-md mx-4">
          <h1 className="text-2xl font-bold text-red-600 dark:text-red-400 mb-4">
            خطأ
          </h1>
          <p className="text-gray-600 dark:text-gray-300">{error}</p>
        </div>
      </div>
    );
  }

  if (!course) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-white via-gray-50 to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 transition-colors duration-300 flex items-center justify-center">
        <div className="text-center bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 max-w-md mx-4">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">
            الكورس غير موجود
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            لم يتم العثور على الكورس المطلوب
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white via-gray-50 to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 transition-colors duration-300 pt-20">
      {/* إضافة styles الحماية في head - zaki alkholy */}
      <style jsx global>{`
        .video-protected {
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: transparent;
          pointer-events: auto;
        }

        .video-protected video {
          pointer-events: auto;
        }

        /* منع النقر بالزر الأيمن */
        .video-protected video::-webkit-media-controls {
          display: none !important;
        }

        .video-protected video::-webkit-media-controls-enclosure {
          display: none !important;
        }

        /* منع حفظ الفيديو */
        .video-protected video::-webkit-media-controls-download-button {
          display: none !important;
        }

        .video-protected video::-webkit-media-controls-fullscreen-button {
          display: none !important;
        }
      `}</style>

      <div className="container mx-auto px-4 py-8">
        {/* Hero Section - معلومات الكورس الرئيسية */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden mb-8 animate-fade-in">
          {/* Course Header with Gradient Background */}
          <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 p-8 text-white relative overflow-hidden">
            <div className="absolute inset-0 bg-black/10"></div>
            <div className="relative z-10">
              <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
                {/* Course Title and Info */}
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h1 className="text-3xl lg:text-4xl font-bold text-white">
                      {course.title}
                    </h1>
                    <div
                      className={`px-3 py-1 rounded-full text-sm font-medium ${
                        isEnrolled
                          ? "bg-green-500/20 text-green-100 border border-green-400/30"
                          : "bg-yellow-500/20 text-yellow-100 border border-yellow-400/30"
                      }`}
                    >
                      {isEnrolled ? "مشترك" : "غير مشترك"}
                    </div>
                  </div>
                  <p className="text-white/80 text-lg max-w-2xl">
                    {course.short_description}
                  </p>
                  <div className="flex items-center gap-4 mt-4">
                    <div className="flex items-center gap-2">
                      <svg
                        className="w-5 h-5 text-white/70"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                      <span className="text-white/80">
                        {course.instructor?.name || "غير محدد"}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <svg
                        className="w-5 h-5 text-white/70"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                        />
                      </svg>
                      <span className="text-white/80">
                        {lessons?.length || 0} درس
                      </span>
                    </div>
                  </div>
                </div>

                {/* Course Image */}
                <div className="w-full lg:w-80 h-48 lg:h-56 rounded-xl overflow-hidden shadow-2xl">
                  {course.thumbnail ? (
                    <Image
                      src={`${theUrl}${course.thumbnail}`}
                      alt={course.title}
                      width={320}
                      height={224}
                      className="w-full h-full object-cover"
                      priority
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-white/10 backdrop-blur-sm">
                      <span className="text-white/70 text-lg">
                        لا توجد صورة للكورس
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
            {/* Decorative Elements */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
            <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
          </div>

          {/* Course Information */}
          <div className="p-8">
            <div className="flex flex-col justify-between items-start mb-6">
              {/* Like Button */}
              <div className="my-6 flex justify-center   ms-auto">
                <button
                  onClick={handleToggleLike}
                  disabled={likeLoading}
                  className={`flex items-center gap-2 px-6 py-3 rounded-xl transition-all duration-300 ${
                    isLiked
                      ? "bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg shadow-red-200 dark:shadow-red-900/30"
                      : "bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 text-gray-700 dark:text-gray-300 hover:from-red-50 hover:to-pink-50 dark:hover:from-red-900/20 dark:hover:to-pink-900/20"
                  } ${
                    likeLoading
                      ? "opacity-50 cursor-not-allowed"
                      : "hover:scale-105"
                  }`}
                >
                  <svg
                    className={`w-5 h-5 ${
                      isLiked
                        ? "fill-white"
                        : "fill-gray-400 dark:fill-gray-500"
                    }`}
                    viewBox="0 0 20 20"
                  >
                    <path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 18.657l-6.828-6.829a4 4 0 010-5.656z" />
                  </svg>
                  <span className="font-medium">
                    {isLiked ? "إلغاء الإعجاب" : "إعجاب"}
                  </span>
                  {likeLoading && (
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  )}
                </button>
              </div>
              <div className="grid grid-cols-3 gap-4">
                {/* Course Stats */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                      <svg
                        className="w-5 h-5 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        التقييم
                      </p>
                      <p className="text-lg font-bold text-gray-800 dark:text-white">
                        {course.rating || 0}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                      <svg
                        className="w-5 h-5 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path d="M18 8a2 2 0 11-4 0 2 2 0 014 0z" />
                        <path d="M14 15a4 4 0 00-8 0v3h8v-3z" />
                        <path d="M6 8a2 2 0 11-4 0 2 2 0 014 0z" />
                        <path d="M16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3z" />
                        <path d="M4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        الطلاب
                      </p>
                      <p className="text-lg font-bold text-gray-800 dark:text-white">
                        {course.students_count || 0}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                      <svg
                        className="w-5 h-5 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                        />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        الإعجابات
                      </p>
                      <p className="text-lg font-bold text-gray-800 dark:text-white">
                        {likesCount}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* معلومات المدرس */}
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700/50 dark:to-gray-600/50 rounded-xl p-6 mb-8">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center overflow-hidden shadow-lg">
                  {course.instructor?.profile_image ? (
                    <Image
                      src={getProfileImageUrl(course.instructor.profile_image)}
                      alt={course.instructor?.username || "صورة المدرس"}
                      width={64}
                      height={64}
                      className="w-full object-cover h-full"
                      sizes="64px"
                    />
                  ) : (
                    <svg
                      className="w-8 h-8 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                      />
                    </svg>
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-1">
                    {course.instructor?.first_name || "غير محدد"}{" "}
                    {course.instructor?.last_name || ""}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    {course.instructor?.bio || "مدرب معتمد"}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    المدرب
                  </p>
                  <div className="flex items-center gap-1 mt-1">
                    <svg
                      className="w-4 h-4 text-yellow-400"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      خبير
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* وصف الكورس */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 mb-8 shadow-lg">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </div>
                <h2 className="text-xl font-bold text-gray-800 dark:text-white">
                  وصف الكورس
                </h2>
              </div>
              <div className="prose max-w-none">
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed text-lg">
                  {course.description}
                </p>
              </div>
            </div>

            {/* تفاصيل الكورس */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-xl border border-blue-200 dark:border-blue-700">
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-4 h-4 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 10V3L4 14h7v7l9-11h-7z"
                      />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    المستوى
                  </h3>
                </div>
                <p className="text-gray-600 dark:text-gray-300 font-medium">
                  {course.level}
                </p>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-4 rounded-xl border border-green-200 dark:border-green-700">
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-4 h-4 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"
                      />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    اللغة
                  </h3>
                </div>
                <p className="text-gray-600 dark:text-gray-300 font-medium">
                  {course.language}
                </p>
              </div>

              <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-4 rounded-xl border border-purple-200 dark:border-purple-700">
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-4 h-4 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                      />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    السعر
                  </h3>
                </div>
                <div className="text-gray-600 dark:text-gray-300 font-medium">
                  {course.discount_price ? (
                    <div className="flex items-center gap-2">
                      <span className="line-through text-gray-400 dark:text-gray-500 text-sm">
                        {course.price}
                      </span>
                      <span className="text-purple-600 dark:text-purple-400 font-bold">
                        {course.discount_price}
                      </span>
                      <span className="text-xs">{course.currency}</span>
                    </div>
                  ) : (
                    <span>
                      {course.price} {course.currency}
                    </span>
                  )}
                </div>
              </div>

              <div className="bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 p-4 rounded-xl border border-orange-200 dark:border-orange-700">
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-4 h-4 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                      />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    عدد الدروس
                  </h3>
                </div>
                <p className="text-gray-600 dark:text-gray-300 font-medium">
                  {lessons.length} درس
                </p>
              </div>
            </div>

            {/* متطلبات الكورس */}
            {course.prerequisites && (
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  متطلبات الكورس
                </h2>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-gray-600">{course.prerequisites}</p>
                </div>
              </div>
            )}

            {/* مخرجات التعلم */}
            {course.learning_outcomes && (
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  مخرجات التعلم
                </h2>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-gray-600">{course.learning_outcomes}</p>
                </div>
              </div>
            )}

            {/* محتوى الكورس */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-8 mb-8 shadow-lg">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <svg
                    className="w-5 h-5 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                    />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
                  محتوى الكورس
                </h2>
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium">
                  {lessons?.length || 0} درس
                </span>
              </div>

              <div className="space-y-4">
                {Array.isArray(lessons) && lessons.length > 0 ? (
                  lessons.map((lesson, index) => (
                    <div
                      key={lesson.id}
                      className="group bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 rounded-xl p-6 hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 transition-all duration-300 border border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-lg "
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 justify-between">
                          <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-lg">
                            {index + 1}
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-gray-800 dark:text-white group-hover:text-indigo-700 dark:group-hover:text-indigo-300 transition-colors">
                              {lesson.title}
                            </h3>
                            <div className="flex items-center gap-3 mt-1">
                              {lesson.is_preview && (
                                <span className="bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-medium px-3 py-1 rounded-full shadow-sm">
                                  معاينة مجانية
                                </span>
                              )}
                              <span className="text-gray-500 dark:text-gray-400 text-sm">
                                {lesson.duration} دقيقة
                              </span>
                              <span className="text-gray-500 dark:text-gray-400 text-sm">
                                {lesson.lesson_type}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="flex  items-center gap-3">
                          {/* Progress indicator if enrolled */}
                          {isEnrolled && watchProgress[lesson.id] && (
                            <div className="flex items-center gap-2">
                              <div className="w-16 h-2 bg-gray-200 dark:bg-gray-600 rounded-full overflow-hidden">
                                <div
                                  className="h-full bg-gradient-to-r from-green-500 to-emerald-500 transition-all duration-300"
                                  style={{
                                    width: `${
                                      watchProgress[lesson.id]
                                        .progressPercentage || 0
                                    }%`,
                                  }}
                                ></div>
                              </div>
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                {watchProgress[lesson.id].progressPercentage ||
                                  0}
                                %
                              </span>
                            </div>
                          )}

                          {(lesson.is_preview || isEnrolled) && (
                            <button
                              onClick={() => handleVideo(lesson)}
                              className="group/btn bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-4 py-3 whitespace-nowrap rounded-xl font-light md:font-normal transition-all duration-300 hover:scale-105 shadow-lg flex items-center gap-2 scale-75 md:scale-100"
                            >
                              <svg
                                className="w-4 h-4 group-hover/btn:scale-110 transition-transform"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z"
                                />
                              </svg>
                              {lesson.is_preview
                                ? "معاينة مجانية"
                                : "مشاهدة الدرس"}
                            </button>
                          )}

                          {!lesson.is_preview && !isEnrolled && (
                            <div className="bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400 px-6 py-3 rounded-xl font-medium flex items-center gap-2">
                              <svg
                                className="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                                />
                              </svg>
                              مقفل
                            </div>
                          )}
                        </div>
                      </div>

                      {/* الكويزات والملفات */}
                      {(lesson.quizzes?.length > 0 || lesson.resources) &&
                        isEnrolled && (
                          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
                            <div className="flex flex-wrap gap-3">
                              {/* أزرار الامتحان/الواجب */}
                              {lesson.quizzes &&
                                lesson.quizzes.length > 0 &&
                                lesson.quizzes.map((quiz) => {
                                  if (!quiz.is_published) return null;
                                  const quizStatus = quizStatuses[quiz.id];
                                  const isSubmitted =
                                    quizStatus?.status === "submitted";

                                  return (
                                    <button
                                      key={quiz.id}
                                      onClick={() =>
                                        handleOpenExamModal(quiz.id)
                                      }
                                      className={`flex items-center gap-2 px-4 py-2 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-sm ${
                                        isSubmitted
                                          ? "bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"
                                          : quiz.quiz_type === "exam"
                                          ? "bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white"
                                          : "bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
                                      }`}
                                    >
                                      <svg
                                        className="w-4 h-4"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                      >
                                        {isSubmitted ? (
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                          />
                                        ) : quiz.quiz_type === "exam" ? (
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
                                          />
                                        ) : (
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                                          />
                                        )}
                                      </svg>
                                      {isSubmitted
                                        ? "إظهار النتيجة"
                                        : quiz.quiz_type === "exam"
                                        ? "حل الامتحان"
                                        : "حل الواجب"}
                                    </button>
                                  );
                                })}

                              {/* زر تحميل ملف الدرس */}
                              {lesson.resources && (
                                <button
                                  onClick={() =>
                                    handleFileDownload(
                                      lesson.resources,
                                      `درس-${lesson.title}`
                                    )
                                  }
                                  className="flex items-center gap-2 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white px-4 py-2 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-sm"
                                >
                                  <svg
                                    className="w-4 h-4"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                    />
                                  </svg>
                                  تحميل ملف الدرس
                                </button>
                              )}
                            </div>
                          </div>
                        )}
                    </div>
                  ))
                ) : (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg
                        className="w-8 h-8 text-gray-400 dark:text-gray-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                        />
                      </svg>
                    </div>
                    <p className="text-gray-500 dark:text-gray-400 text-lg">
                      لا توجد دروس متاحة حالياً
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* التقييمات والتعليقات */}
            {reviews?.length > 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 mb-8 shadow-lg">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-10 h-10 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center">
                    <svg
                      className="w-5 h-5 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                      />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
                    تقييمات الطلاب
                  </h2>
                  <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-full text-sm font-medium">
                    {reviews.length} تقييم
                  </span>
                </div>

                {reviewLoading ? (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 border-4 border-yellow-200 border-t-yellow-500 rounded-full animate-spin mx-auto mb-4"></div>
                    <p className="text-gray-600 dark:text-gray-300">
                      جاري تحميل التقييمات...
                    </p>
                  </div>
                ) : reviews.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg
                        className="w-8 h-8 text-gray-400 dark:text-gray-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"
                        />
                      </svg>
                    </div>
                    <p className="text-gray-500 dark:text-gray-400 text-lg">
                      لا توجد تقييمات بعد
                    </p>
                    <p className="text-gray-400 dark:text-gray-500 text-sm mt-2">
                      كن أول من يقيم هذا الكورس
                    </p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {reviews.map((review) => (
                      <div
                        key={review.id}
                        className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 rounded-xl p-6 border border-gray-200 dark:border-gray-600"
                      >
                        <div className="flex items-start gap-4 mb-4">
                          <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                            {(review.user?.username || "مستخدم")
                              .charAt(0)
                              .toUpperCase()}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <span className="font-bold text-gray-800 dark:text-white">
                                {review.user?.username || "مستخدم"}
                              </span>
                              <div className="flex items-center gap-1">
                                {[...Array(5)].map((_, i) => (
                                  <svg
                                    key={i}
                                    className={`w-4 h-4 ${
                                      i < review.rating
                                        ? "text-yellow-400"
                                        : "text-gray-300 dark:text-gray-600"
                                    }`}
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                  >
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                ))}
                              </div>
                              <span className="text-xs text-gray-400 dark:text-gray-500">
                                {new Date(review.created_at).toLocaleDateString(
                                  "ar-EG"
                                )}
                              </span>
                            </div>
                            <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                              {review.comment}
                            </p>

                            {review.reply && (
                              <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                                <div className="flex items-center gap-2 mb-2">
                                  <svg
                                    className="w-4 h-4 text-blue-600 dark:text-blue-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"
                                    />
                                  </svg>
                                  <span className="font-bold text-blue-800 dark:text-blue-200">
                                    رد المدرب:
                                  </span>
                                </div>
                                <p className="text-blue-700 dark:text-blue-300">
                                  {review.reply}
                                </p>
                              </div>
                            )}

                            {/* عرض التعليقات الشجرية - zaki alkholy */}
                            <ReviewComments
                              review={review}
                              comments={reviewComments[review.id] || []}
                              commentText={commentText}
                              setCommentText={setCommentText}
                              replyText={replyText}
                              setReplyText={setReplyText}
                              showReplyForm={showReplyForm}
                              onAddComment={handleAddComment}
                              onToggleReplyForm={toggleReplyForm}
                              commentLoading={commentLoading}
                              expandedComments={expandedComments}
                              onToggleExpand={toggleExpandComments}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* فورم إضافة تقييم */}
            {user && isEnrolled && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 mb-8 shadow-lg">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                    <svg
                      className="w-5 h-5 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 4v16m8-8H4"
                      />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 dark:text-white">
                    أضف تقييمك للكورس
                  </h3>
                </div>

                <form
                  onSubmit={handleSubmit(onSubmitReview)}
                  className="space-y-6"
                >
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      تقييمك بالنجوم:
                    </label>
                    <select
                      {...register("rating", { required: true })}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors"
                    >
                      <option value="">اختر تقييمك</option>
                      {[1, 2, 3, 4, 5].map((n) => (
                        <option key={n} value={n}>
                          {n} نجمة{n > 1 ? "" : ""}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      تعليقك على الكورس:
                    </label>
                    <textarea
                      {...register("comment", { required: true })}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors resize-none"
                      rows={4}
                      placeholder="شاركنا رأيك في الكورس..."
                    />
                  </div>

                  <div className="flex gap-4">
                    <button
                      type="submit"
                      className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg flex items-center gap-2"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                        />
                      </svg>
                      إرسال التقييم
                    </button>
                  </div>

                  {reviewError && (
                    <div className="bg-gradient-to-r from-red-50 to-rose-50 dark:from-red-900/20 dark:to-rose-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 px-4 py-3 rounded-xl flex items-center gap-2">
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                      {reviewError}
                    </div>
                  )}

                  {reviewSuccess && (
                    <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-700 text-green-700 dark:text-green-300 px-4 py-3 rounded-xl flex items-center gap-2">
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                      {reviewSuccess}
                    </div>
                  )}
                </form>
              </div>
            )}

            {/* زر التسجيل والإجراءات */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-8 mb-8 shadow-lg">
              {user ? (
                isEnrolled ? (
                  <div className="text-center">
                    <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-700 rounded-xl p-6 mb-6">
                      <div className="flex items-center justify-center gap-3 mb-4">
                        <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                          <svg
                            className="w-6 h-6 text-white"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                        </div>
                        <div>
                          <h3 className="text-lg font-bold text-green-800 dark:text-green-200">
                            مبروك!
                          </h3>
                          <p className="text-green-600 dark:text-green-300">
                            أنت مسجل في هذا الكورس
                          </p>
                        </div>
                      </div>
                      <Link
                        href={`/student/course/${course?.slug || id}/learn`}
                        className="inline-flex items-center gap-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white py-4 px-8 rounded-xl font-medium text-lg transition-all duration-300 hover:scale-105 shadow-lg"
                      >
                        <svg
                          className="w-5 h-5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z"
                          />
                        </svg>
                        متابعة التعلم
                      </Link>
                    </div>
                  </div>
                ) : (
                  <div>
                    <div className="text-center mb-6">
                      <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-2">
                        ابدأ رحلتك التعليمية الآن
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300">
                        انضم إلى آلاف الطلاب واحصل على شهادة معتمدة
                      </p>
                    </div>
                    {course ? (
                      <PaymentForm
                        course={course}
                        coursePrice={course?.discount_price || course?.price}
                      />
                    ) : (
                      <div className="bg-gradient-to-r from-red-50 to-rose-50 dark:from-red-900/20 dark:to-rose-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 px-6 py-4 rounded-xl text-center">
                        <svg
                          className="w-8 h-8 mx-auto mb-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        خطأ: بيانات الكورس غير متوفرة
                      </div>
                    )}
                  </div>
                )
              ) : (
                <div className="text-center">
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-700 rounded-xl p-6 mb-6">
                    <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg
                        className="w-8 h-8 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                    </div>
                    <h3 className="text-lg font-bold text-blue-800 dark:text-blue-200 mb-2">
                      مطلوب تسجيل الدخول
                    </h3>
                    <p className="text-blue-600 dark:text-blue-300 mb-4">
                      يجب تسجيل الدخول للتسجيل في الكورس
                    </p>
                    <Link
                      href="/login"
                      className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white py-4 px-8 rounded-xl font-medium text-lg transition-all duration-300 hover:scale-105 shadow-lg"
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"
                        />
                      </svg>
                      تسجيل الدخول
                    </Link>
                  </div>
                </div>
              )}
            </div>

            {/* أزرار حل الامتحان/الواجب */}
            {(examId || assignmentId) && isEnrolled && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 mb-8 shadow-lg">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-4 h-4 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
                      />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold text-gray-800 dark:text-white">
                    الاختبارات والواجبات
                  </h3>
                </div>

                <div className="flex flex-wrap gap-4">
                  {examId &&
                    (examStatus?.submitted || examStatus?.time_left === 0 ? (
                      <button
                        type="button"
                        onClick={() => handleOpenExamModal(examId)}
                        className="flex items-center gap-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        عرض النتيجة - {course?.exam_name}
                      </button>
                    ) : (
                      <button
                        type="button"
                        onClick={() => handleOpenExamModal(examId)}
                        className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
                          />
                        </svg>
                        حل الامتحان - {course?.exam_name}
                      </button>
                    ))}
                  {assignmentId && (
                    <button
                      type="button"
                      onClick={() => {
                        setModalType("assignment");
                        setModalId(assignmentId);
                        setModalDuration(0);
                        setModalOpen(true);
                      }}
                      className="flex items-center gap-2 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                        />
                      </svg>
                      حل الواجب
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* مودال الامتحان/الواجب */}
      <ExamAssignmentModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        type={modalType}
        id={modalId}
        duration={modalType === "exam" ? modalDuration : undefined}
        questions={modalQuestions}
        examStatus={modalId === examId ? examStatus : quizStatuses[modalId]} // تمرير حالة الامتحان الصحيحة
        onExamSubmitted={handleExamSubmitted} // تمرير دالة callback
      />

      {previewVideoUrl && isClient && (
        <div className="fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl shadow-2xl border border-gray-200 dark:border-gray-700">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex justify-between items-center mb-3">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                  {selectedLesson?.title} -{" "}
                  {selectedLesson?.is_preview ? "معاينة" : "مشاهدة"}
                </h3>
                <button
                  onClick={() => {
                    setPreviewVideoUrl(null);
                    setSelectedLesson(null);
                    if (player) {
                      player.destroy();
                      setPlayer(null);
                    }
                    // تنظيف interval تتبع التقدم - zaki alkholy
                    if (progressSaveInterval) {
                      clearInterval(progressSaveInterval);
                      setProgressSaveInterval(null);
                    }
                  }}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors duration-200 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            </div>
            <div className="p-4 bg-gray-50 dark:bg-gray-900/50">
              {previewLoading ? (
                <div className="flex justify-center items-center h-64">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
                </div>
              ) : previewError ? (
                <div className="text-red-500 dark:text-red-400 text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                  {previewError}
                </div>
              ) : (
                <div
                  className="plyr__video-embed relative video-protected rounded-lg overflow-hidden shadow-lg"
                  onContextMenu={(e) => e.preventDefault()} // منع النقر بالزر الأيمن - zaki alkholy
                  onDragStart={(e) => e.preventDefault()} // منع السحب - zaki alkholy
                  style={{ userSelect: "none" }} // منع التحديد - zaki alkholy
                >
                  <video
                    ref={videoRef}
                    className="w-full rounded-lg"
                    style={{ maxHeight: "70vh" }}
                    playsInline
                    controls={false} // إخفاء controls الافتراضية لاستخدام Plyr - zaki alkholy
                    controlsList="nodownload nofullscreen noremoteplayback" // منع التحميل - zaki alkholy
                    disablePictureInPicture // منع picture-in-picture - zaki alkholy
                    onContextMenu={(e) => e.preventDefault()}
                    crossOrigin="anonymous" // للأمان - zaki alkholy
                    onLoadStart={() =>
                      console.log("بدء تحميل الفيديو المحمي - zaki alkholy")
                    }
                    onError={() =>
                      console.error(
                        "خطأ في تحميل الفيديو المحمي - zaki alkholy"
                      )
                    }
                    // منع التحديد والسحب - zaki alkholy
                    draggable={false}
                    onDragStart={(e) => e.preventDefault()}
                  />
                  {/* طبقة حماية شفافة - zaki alkholy */}
                  <div
                    className="absolute inset-0 pointer-events-none rounded-lg"
                    style={{
                      background: "transparent",
                      zIndex: 1,
                    }}
                  />

                  {/* Dynamic Canvas Watermark للفيديوهات المحمية - زكي الخولي */}
                  {(() => {
                    const shouldShow =
                      selectedLesson && !selectedLesson.is_preview && user;
                    console.log("Watermark Debug - زكي الخولي:", {
                      selectedLesson: !!selectedLesson,
                      isPreview: selectedLesson?.is_preview,
                      userValid: isUserDataValid(),
                      isWatermarkVisible,
                      forceWatermark,
                      shouldShow,
                      user: !!user,
                    });

                    return (
                      shouldShow && (
                        <DynamicWatermark
                          user={user}
                          videoElement={videoRef}
                          isVisible={forceWatermark || isWatermarkVisible}
                          className="rounded-lg"
                        />
                      )
                    );
                  })()}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
