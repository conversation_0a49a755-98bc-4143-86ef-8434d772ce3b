// صفحة تحليلات وتقارير المعلم الشاملة - zaki alkholy
"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useSelector } from "react-redux";
import Cookies from "js-cookie";
import { toast } from "react-hot-toast";
import {
  BarChart3,
  Users,
  DollarSign,
  TrendingUp,
  BookOpen,
  Award,
  Calendar,
  Eye,
  Target,
  Clock,
  Star,
  Download,
} from "lucide-react";

// استيراد خدمات API الجديدة - zaki alkholy
import {
  fetchInstructorAnalytics,
  fetchSalesAnalytics,
  fetchTopPerformingContent,
  fetchAssignmentAnalytics,
  exportAnalyticsReport,
} from "@/services/instructorAnalytics";

import { selectCurrentUser, selectIsAuthenticated } from "@/store/authSlice";

// مكون لعرض بطاقة إحصائية - zaki alkholy
const StatCard = ({
  icon: Icon,
  title,
  value,
  change,
  changeType,
  color = "blue",
}) => {
  const colorClasses = {
    blue: "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700 text-blue-600 dark:text-blue-400",
    green:
      "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700 text-green-600 dark:text-green-400",
    purple:
      "bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-700 text-purple-600 dark:text-purple-400",
    orange:
      "bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-700 text-orange-600 dark:text-orange-400",
    red: "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-700 text-red-600 dark:text-red-400",
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`p-6 rounded-xl border-2 ${colorClasses[color]} hover:shadow-lg dark:hover:shadow-gray-900/20 transition-all duration-300 bg-white dark:bg-gray-800`}
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
            {title}
          </p>
          <p className="text-2xl font-bold mt-1 text-gray-900 dark:text-gray-100">
            {value}
          </p>
          {change && (
            <div
              className={`flex items-center mt-2 text-sm ${
                changeType === "positive"
                  ? "text-green-600 dark:text-green-400"
                  : "text-red-600 dark:text-red-400"
              }`}
            >
              <TrendingUp
                className={`w-4 h-4 mr-1 ${
                  changeType === "negative" ? "rotate-180" : ""
                }`}
              />
              <span>{change}</span>
            </div>
          )}
        </div>
        <Icon className="w-8 h-8" />
      </div>
    </motion.div>
  );
};

// مكون لعرض الرسم البياني البسيط - zaki alkholy
const SimpleChart = ({ data, title, color = "blue" }) => {
  const maxValue = Math.max(...data.map((item) => item.value));

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">
        {title}
      </h3>
      <div className="space-y-3">
        {data.map((item, index) => (
          <div key={index} className="flex items-center">
            <div className="w-20 text-sm text-gray-600 dark:text-gray-400 truncate">
              {item.label}
            </div>
            <div className="flex-1 mx-3">
              <div className="bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${(item.value / maxValue) * 100}%` }}
                  transition={{ duration: 1, delay: index * 0.1 }}
                  className={`h-2 rounded-full ${
                    color === "blue"
                      ? "bg-blue-500 dark:bg-blue-400"
                      : color === "green"
                      ? "bg-green-500 dark:bg-green-400"
                      : color === "purple"
                      ? "bg-purple-500 dark:bg-purple-400"
                      : "bg-orange-500 dark:bg-orange-400"
                  }`}
                />
              </div>
            </div>
            <div className="w-12 text-sm font-medium text-right text-gray-900 dark:text-gray-100">
              {item.value}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// مكون لعرض جدول أداء الطلاب - zaki alkholy
const StudentPerformanceTable = ({ students }) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          أداء الطلاب
        </h3>
        <button className="flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm">
          <Download className="w-4 h-4 mr-1" />
          تصدير
        </button>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full text-sm">
          <thead>
            <tr className="border-b border-gray-200 dark:border-gray-700">
              <th className="text-right py-3 px-2 font-medium text-gray-600 dark:text-gray-400">
                الطالب
              </th>
              <th className="text-right py-3 px-2 font-medium text-gray-600 dark:text-gray-400">
                التقدم
              </th>
              <th className="text-right py-3 px-2 font-medium text-gray-600 dark:text-gray-400">
                الاختبارات
              </th>
              <th className="text-right py-3 px-2 font-medium text-gray-600 dark:text-gray-400">
                آخر نشاط
              </th>
              <th className="text-right py-3 px-2 font-medium text-gray-600 dark:text-gray-400">
                الحالة
              </th>
            </tr>
          </thead>
          <tbody>
            {students.map((student, index) => (
              <tr
                key={index}
                className="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50"
              >
                <td className="py-3 px-2">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mr-3">
                      <span className="text-blue-600 dark:text-blue-400 font-medium text-xs">
                        {student.name.charAt(0)}
                      </span>
                    </div>
                    <span className="font-medium text-gray-900 dark:text-gray-100">
                      {student.name}
                    </span>
                  </div>
                </td>
                <td className="py-3 px-2">
                  <div className="flex items-center">
                    <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                      <div
                        className="bg-blue-500 dark:bg-blue-400 h-2 rounded-full"
                        style={{ width: `${student.progress}%` }}
                      />
                    </div>
                    <span className="text-sm text-gray-900 dark:text-gray-100">
                      {student.progress}%
                    </span>
                  </div>
                </td>
                <td className="py-3 px-2">
                  <span className="text-sm text-gray-900 dark:text-gray-100">
                    {student.quizzes}/5
                  </span>
                </td>
                <td className="py-3 px-2">
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {student.lastActivity}
                  </span>
                </td>
                <td className="py-3 px-2">
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${
                      student.status === "نشط"
                        ? "bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400"
                        : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400"
                    }`}
                  >
                    {student.status}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

// الصفحة الرئيسية لتحليلات المعلم - zaki alkholy
export default function InstructorAnalyticsPage() {
  const [analyticsData, setAnalyticsData] = useState(null);
  const [salesData, setSalesData] = useState(null);
  const [topContentData, setTopContentData] = useState(null);
  const [assignmentData, setAssignmentData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedPeriod, setSelectedPeriod] = useState("30");
  const [activeTab, setActiveTab] = useState("overview");

  // الحصول على بيانات المستخدم من Redux - zaki alkholy
  const user = useSelector(selectCurrentUser);
  const isAuthenticated = useSelector(selectIsAuthenticated);

  // جلب بيانات التحليلات من API - zaki alkholy
  useEffect(() => {
    const fetchAllAnalytics = async () => {
      if (!isAuthenticated || !user) {
        setLoading(false);
        return;
      }

      try {
        const token =
          Cookies.get("authToken") || localStorage.getItem("access_token");
        if (!token) {
          throw new Error("لا يوجد رمز مصادقة");
        }

        // جلب جميع البيانات المطلوبة بشكل متوازي مع معالجة الأخطاء - zaki alkholy
        const [analyticsData, salesData, topContentData, assignmentData] =
          await Promise.allSettled([
            fetchInstructorAnalytics(token, selectedPeriod),
            fetchSalesAnalytics(token, selectedPeriod),
            fetchTopPerformingContent(token, selectedPeriod),
            fetchAssignmentAnalytics(token),
          ]).then((results) =>
            results.map((result) =>
              result.status === "fulfilled" ? result.value : null
            )
          );

        setAnalyticsData(analyticsData);
        setSalesData(salesData);
        setTopContentData(topContentData);
        setAssignmentData(assignmentData);

        // التحقق من وجود بيانات فاشلة وإظهار تحذير - zaki alkholy
        const failedData = [
          analyticsData,
          salesData,
          topContentData,
          assignmentData,
        ].filter((data) => data === null);

        if (failedData.length > 0) {
          toast.warning(
            `تم تحميل البيانات جزئياً. ${failedData.length} من أصل 4 خدمات فشلت.`
          );
        }
      } catch (error) {
        console.error("خطأ في جلب بيانات التحليلات:", error);
        setError(error.message);
        toast.error("حدث خطأ في جلب بيانات التحليلات");
      } finally {
        setLoading(false);
      }
    };

    fetchAllAnalytics();
  }, [selectedPeriod, isAuthenticated, user]);

  // عرض شاشة التحميل - zaki alkholy
  if (loading) {
    return (
      <div className="min-h-screen bg-background dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-foreground mb-2">
            جاري تحميل التحليلات...
          </h2>
          <p className="text-secondary">يرجى الانتظار قليلاً</p>
        </div>
      </div>
    );
  }

  // عرض رسالة خطأ إذا لم يكن المستخدم مسجل دخول - zaki alkholy
  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            يجب تسجيل الدخول أولاً
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            يرجى تسجيل الدخول لعرض التحليلات
          </p>
        </div>
      </div>
    );
  }

  // عرض رسالة خطأ إذا فشل تحميل البيانات - zaki alkholy
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 dark:text-red-400 mb-4">
            حدث خطأ
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 dark:bg-blue-600 text-white rounded hover:bg-blue-600 dark:hover:bg-blue-700 transition-colors"
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    );
  }

  // دمج البيانات الحقيقية مع البيانات الافتراضية - zaki alkholy
  const displayData = {
    overview: analyticsData || {
      total_courses: 0,
      total_students: 0,
      total_revenue: 0,
      average_rating: 0,
      active_students: 0,
      quiz_pass_rate: 0,
    },
    sales: salesData || [],
    topContent: topContentData || [],
    assignments: assignmentData || [],
  };

  // إعداد بيانات أداء الدورات - zaki alkholy
  const coursePerformanceData =
    displayData.topContent.length > 0
      ? displayData.topContent.map((course) => ({
          label: course.title || course.name || "دورة غير محددة",
          value: course.completion_rate || course.students_count || 0,
        }))
      : [{ label: "لا توجد بيانات", value: 0 }];

  // إعداد بيانات الطلاب - zaki alkholy
  const studentsData =
    displayData.assignments.length > 0
      ? displayData.assignments.slice(0, 10) // أول 10 طلاب
      : [];

  // دالة تصدير التقارير - zaki alkholy
  const handleExportReport = async (reportType) => {
    try {
      const token =
        Cookies.get("authToken") || localStorage.getItem("access_token");
      if (!token) {
        toast.error("لا يوجد رمز مصادقة");
        return;
      }

      toast.loading("جاري تصدير التقرير...");

      const reportData = await exportAnalyticsReport(token, reportType, {
        period: selectedPeriod,
      });

      // إنشاء رابط تحميل الملف
      const url = window.URL.createObjectURL(new Blob([reportData]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute(
        "download",
        `${reportType}_report_${selectedPeriod}days.pdf`
      );
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      toast.dismiss();
      toast.success("تم تصدير التقرير بنجاح");
    } catch (error) {
      toast.dismiss();
      toast.error("حدث خطأ في تصدير التقرير");
      console.error("Export error:", error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* العنوان والفلاتر - zaki alkholy */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex justify-between items-center mb-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2 flex items-center">
                <BarChart3 className="w-8 h-8 mr-3 text-blue-600 dark:text-blue-400" />
                تحليلات الأداء
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                تابع أداء دوراتك وطلابك ومبيعاتك
              </p>
            </div>

            <div className="flex items-center gap-4">
              {/* فلتر الفترة الزمنية - zaki alkholy */}
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
              >
                <option value="7">آخر 7 أيام</option>
                <option value="30">آخر 30 يوم</option>
                <option value="90">آخر 3 شهور</option>
                <option value="365">آخر سنة</option>
              </select>

              {/* زر تصدير التقرير - zaki alkholy */}
              <button
                onClick={() => handleExportReport("analytics")}
                className="flex items-center md:px-4 px-2 py-2 whitespace-nowrap bg-blue-600 dark:bg-blue-500 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors"
              >
                <Download className="w-4 h-4 ml-1" />
                تصدير التقرير
              </button>
            </div>
          </div>
        </motion.div>

        {/* الإحصائيات السريعة - zaki alkholy */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            icon={BookOpen}
            title="إجمالي الدورات"
            value={displayData.overview.total_courses || 0}
            change={displayData.overview.courses_change || ""}
            changeType={displayData.overview.courses_change_type || "positive"}
            color="blue"
          />
          <StatCard
            icon={Users}
            title="إجمالي الطلاب"
            value={(displayData.overview.total_students || 0).toLocaleString()}
            change={displayData.overview.students_change || ""}
            changeType={displayData.overview.students_change_type || "positive"}
            color="green"
          />
          <StatCard
            icon={DollarSign}
            title="إجمالي الإيرادات"
            value={`${(
              displayData.overview.total_revenue || 0
            ).toLocaleString()} ج.م`}
            change={displayData.overview.revenue_change || ""}
            changeType={displayData.overview.revenue_change_type || "positive"}
            color="purple"
          />
          <StatCard
            icon={Star}
            title="متوسط التقييم"
            value={displayData.overview.average_rating || 0}
            change="ممتاز"
            changeType="positive"
            color="orange"
          />
        </div>

        {/* التبويبات - zaki alkholy */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex justify-between px-6">
              {[
                { id: "overview", label: "نظرة عامة", icon: BarChart3 },
                { id: "courses", label: "أداء الدورات", icon: BookOpen },
                { id: "students", label: "الطلاب", icon: Users },
                { id: "sales", label: "المبيعات", icon: DollarSign },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? "border-blue-500 dark:border-blue-400 text-blue-600 dark:text-blue-400"
                      : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                  }`}
                >
                  <tab.icon className="w-4 h-4 ml-1" />
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {/* تبويب النظرة العامة - zaki alkholy */}
            {activeTab === "overview" && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-6">
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                    <h3 className="text-lg font-semibold mb-4 flex items-center text-gray-900 dark:text-gray-100">
                      <Users className="w-5 h-5 mr-2 text-blue-500 dark:text-blue-400" />
                      نشاط الطلاب
                    </h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                          {displayData.overview.active_students || 0}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          طالب نشط
                        </p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                          {displayData.overview.quiz_pass_rate || 0}%
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          معدل النجاح
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <SimpleChart
                  data={coursePerformanceData}
                  title="أداء الدورات"
                  color="blue"
                />
              </div>
            )}

            {/* تبويب أداء الدورات - zaki alkholy */}
            {activeTab === "courses" && (
              <SimpleChart
                data={coursePerformanceData}
                title="معدل إكمال الدورات"
                color="green"
              />
            )}

            {/* تبويب الطلاب - zaki alkholy */}
            {activeTab === "students" && (
              <StudentPerformanceTable students={studentsData} />
            )}

            {/* تبويب المبيعات - zaki alkholy */}
            {activeTab === "sales" && (
              <SimpleChart
                data={
                  displayData.sales.length > 0
                    ? displayData.sales.map((sale) => ({
                        label: sale.month || sale.date || "غير محدد",
                        value: sale.revenue || sale.amount || 0,
                      }))
                    : [{ label: "لا توجد بيانات", value: 0 }]
                }
                title="المبيعات الشهرية (ج.م)"
                color="purple"
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
