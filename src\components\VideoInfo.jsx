/**
 * مكون عرض معلومات الفيديو - نظام مختلط - زكي الخولي
 * يعرض معلومات الفيديو حسب المنصة (Cloudinary أو Bunny Stream)
 */

import React from 'react';

const VideoInfo = ({ lesson, userType = "student", className = "" }) => {
  if (!lesson || lesson.lesson_type !== "video") {
    return null;
  }

  const isPreview = lesson.is_preview;
  const platform = isPreview ? "cloudinary" : "bunny_stream";
  
  // تحديد الألوان حسب نوع الفيديو - زكي الخولي
  const colorClasses = isPreview 
    ? "bg-orange-100 text-orange-800 border-orange-200" 
    : "bg-green-100 text-green-800 border-green-200";

  return (
    <div className={`p-4 border rounded-lg ${colorClasses} ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h4 className="font-medium text-sm">
          معلومات الفيديو - زكي الخولي
        </h4>
        
        {/* أيقونة نوع الفيديو */}
        <div className="flex items-center gap-2">
          {isPreview ? (
            <svg className="w-5 h-5 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
            </svg>
          ) : (
            <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
            </svg>
          )}
        </div>
      </div>

      <div className="space-y-2 text-sm">
        {/* نوع الفيديو */}
        <div className="flex justify-between">
          <span className="font-medium">النوع:</span>
          <span>{isPreview ? "ترويجي مجاني" : "محمي مدفوع"}</span>
        </div>

        {/* المنصة */}
        <div className="flex justify-between">
          <span className="font-medium">المنصة:</span>
          <span>{isPreview ? "Cloudinary" : "Bunny Stream"}</span>
        </div>

        {/* مستوى الحماية */}
        <div className="flex justify-between">
          <span className="font-medium">مستوى الحماية:</span>
          <span>{isPreview ? "أساسي" : "عالي"}</span>
        </div>

        {/* مميزات الحماية للفيديوهات المحمية */}
        {!isPreview && (
          <div className="mt-3 pt-3 border-t border-green-300">
            <div className="font-medium mb-2">مميزات الحماية:</div>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center gap-1">
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>تشفير DRM</span>
              </div>
              <div className="flex items-center gap-1">
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>HLS مشفر</span>
              </div>
              <div className="flex items-center gap-1">
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>Watermark شخصي</span>
              </div>
              <div className="flex items-center gap-1">
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>منع التحميل</span>
              </div>
            </div>
          </div>
        )}

        {/* معلومات إضافية للمعلم */}
        {userType === "instructor" && (
          <div className="mt-3 pt-3 border-t border-current border-opacity-30">
            <div className="text-xs opacity-75">
              {isPreview 
                ? "الفيديوهات الترويجية متاحة للجميع ومناسبة للمعاينة"
                : "الفيديوهات المحمية متاحة للطلاب المشتركين فقط مع حماية كاملة من السرقة"
              }
            </div>
          </div>
        )}

        {/* معلومات إضافية للطالب */}
        {userType === "student" && !isPreview && (
          <div className="mt-3 pt-3 border-t border-green-300">
            <div className="text-xs text-green-700">
              ⚠️ هذا الفيديو محمي ويحتوي على اسمك كعلامة مائية لمنع المشاركة غير المصرح بها
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoInfo;
