/**
 * Protected Video Player مع Dynamic Canvas Watermark - زكي الخولي
 * مشغل فيديو محمي مع watermark ديناميكي متحرك كل 10 ثواني
 */

import React, { useRef, useEffect, useState } from 'react';
import DynamicWatermark from './DynamicWatermark';
import { useDynamicWatermark } from '../hooks/useDynamicWatermark';

const ProtectedVideoPlayer = ({ 
  user, 
  lesson, 
  videoUrl, 
  onVideoRef,
  className = "",
  ...videoProps 
}) => {
  const videoRef = useRef(null);
  const containerRef = useRef(null);
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  
  // Dynamic Watermark Hook - زكي الخولي
  const {
    isWatermarkVisible,
    watermarkConfig,
    toggleWatermark,
    getWatermarkText,
    isUserDataValid
  } = useDynamicWatermark(user, lesson && !lesson.is_preview);

  // تمرير ref للمكون الأب - زكي الخولي
  useEffect(() => {
    if (onVideoRef && videoRef.current) {
      onVideoRef(videoRef);
    }
  }, [onVideoRef]);

  // مراقبة تحميل الفيديو - زكي الخولي
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadedMetadata = () => {
      setIsVideoLoaded(true);
    };

    const handleLoadStart = () => {
      // بدء تحميل الفيديو المحمي
    };

    const handleError = (e) => {
      setIsVideoLoaded(false);
    };

    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('loadstart', handleLoadStart);
    video.addEventListener('error', handleError);

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('loadstart', handleLoadStart);
      video.removeEventListener('error', handleError);
    };
  }, [videoUrl]);

  // منع التلاعب بالفيديو - زكي الخولي
  const handleContextMenu = (e) => {
    e.preventDefault();
    return false;
  };

  const handleDragStart = (e) => {
    e.preventDefault();
    return false;
  };

  // حماية إضافية ضد التحديد - زكي الخولي
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const preventSelect = (e) => {
      e.preventDefault();
      return false;
    };

    // إضافة حماية ضد التحديد
    container.addEventListener('selectstart', preventSelect);
    container.addEventListener('mousedown', preventSelect);

    return () => {
      container.removeEventListener('selectstart', preventSelect);
      container.removeEventListener('mousedown', preventSelect);
    };
  }, []);

  // تحديد ما إذا كان الفيديو محمي - زكي الخولي
  const isProtectedVideo = lesson && !lesson.is_preview;

  return (
    <div
      ref={containerRef}
      className={`relative video-protected ${className}`}
      onContextMenu={handleContextMenu}
      onDragStart={handleDragStart}
      style={{ userSelect: 'none' }}
    >
      {/* عنصر الفيديو */}
      <video
        ref={videoRef}
        className="w-full rounded-lg"
        style={{ maxHeight: "70vh" }}
        playsInline
        controls={false} // إخفاء controls الافتراضية لاستخدام Plyr - zaki alkholy
        controlsList="nodownload nofullscreen noremoteplayback" // منع التحميل - zaki alkholy
        disablePictureInPicture // منع picture-in-picture - zaki alkholy
        onContextMenu={handleContextMenu}
        crossOrigin="anonymous" // للأمان - zaki alkholy
        onLoadStart={() => {
          // بدء تحميل الفيديو المحمي
        }}
        onError={() => {
          // خطأ في تحميل الفيديو المحمي
        }}
        // منع التحديد والسحب - zaki alkholy
        draggable={false}
        onDragStart={handleDragStart}
        {...videoProps}
      />

      {/* طبقة حماية شفافة - zaki alkholy */}
      <div
        className="absolute inset-0 pointer-events-none rounded-lg"
        style={{
          background: "transparent",
          zIndex: 1,
        }}
      />

      {/* Dynamic Canvas Watermark للفيديوهات المحمية - زكي الخولي */}
      {isProtectedVideo && isUserDataValid() && isVideoLoaded && (
        <DynamicWatermark
          user={user}
          videoElement={videoRef}
          isVisible={isWatermarkVisible}
          className="rounded-lg"
        />
      )}

      {/* رسالة تحذيرية للحماية - zaki alkholy */}
      <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded flex items-center gap-1">
        <svg
          className="w-3 h-3"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fillRule="evenodd"
            d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
            clipRule="evenodd"
          />
        </svg>
        <span>محمي</span>
      </div>

      {/* معلومات إضافية للفيديو المحمي - زكي الخولي */}
      {isProtectedVideo && (
        <div className="absolute top-2 left-2 bg-green-600 bg-opacity-80 text-white text-xs px-2 py-1 rounded">
          <span>🛡️ محمي بـ Canvas Watermark</span>
        </div>
      )}

      {/* رسالة تحذيرية للطالب - زكي الخولي */}
      {isProtectedVideo && (
        <div className="absolute bottom-2 left-2 bg-red-600 bg-opacity-80 text-white text-xs px-2 py-1 rounded max-w-xs">
          <span>⚠️ يحتوي على معلوماتك الشخصية</span>
        </div>
      )}
    </div>
  );
};

export default ProtectedVideoPlayer;
