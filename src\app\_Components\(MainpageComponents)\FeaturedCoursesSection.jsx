"use client";
import React, { useEffect, useRef, useState } from "react";
import Link from "next/link";
import {
  Star,
  Clock,
  Users,
  Play,
  BookOpen,
  ChevronLeft,
  AlertCircle,
  RefreshCw,
} from "lucide-react";
import { useSelector } from "react-redux";
import { selectCurrentUser } from "../../../store/authSlice";
import Cookies from "js-cookie";
import {
  fetchFeaturedCourses,
  fetchCategories,
} from "../../../services/courses";
import { API_BASE_URL } from "../../../config/api";
import { useRouter } from "next/navigation";

// مكون زر بدء الكورس مع التحقق من تسجيل الدخول - zaki alkholy
function CourseStartButton({ course }) {
  const router = useRouter();
  const user = useSelector(selectCurrentUser);

  const handleStartCourse = () => {
    // التحقق من تسجيل الدخول
    if (!user) {
      // توجيه لصفحة تسجيل الدخول
      router.push("/login");
      return;
    }

    // توجيه لصفحة الكورس باستخدام slug
    router.push(`/student/course/${course.slug}`);
  };

  return (
    <button
      onClick={handleStartCourse}
      className="group/btn inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-medium hover:shadow-lg transform hover:scale-105 transition-all duration-300"
    >
      <span>ابدأ الآن</span>
      <ChevronLeft className="w-4 h-4 mr-2 transform group-hover/btn:-translate-x-1 transition-transform duration-300" />
    </button>
  );
}

export default function FeaturedCoursesSection() {
  const theUrl = "https://res.cloudinary.com/djwhgnwp5/";
  const [visibleCards, setVisibleCards] = useState([]);
  const sectionRef = useRef(null);
  const user = useSelector(selectCurrentUser);

  // حالات البيانات الديناميكية - zaki alkholy
  const [featuredCourses, setFeaturedCourses] = useState([]);
  const [categories, setCategories] = useState([]);
  const [filteredCategories, setFilteredCategories] = useState([]); // الفئات المفلترة التي تحتوي على كورسات
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState("الكل");
  const [categoryLoading, setCategoryLoading] = useState(false);

  // جلب الكورسات والفئات عند تحميل المكون - zaki alkholy
  useEffect(() => {
    loadInitialData();
  }, []);

  // جلب الكورسات عند تغيير الفئة - zaki alkholy
  useEffect(() => {
    if (categories.length > 0) {
      loadCoursesByCategory(selectedCategory);
    }
  }, [selectedCategory, categories]);

  // دالة فلترة الفئات لإظهار فقط التي تحتوي على كورسات - zaki alkholy
  const filterCategoriesWithCourses = async (
    categoriesData,
    coursesData = null
  ) => {
    try {
      console.log("🔍 بدء فلترة الفئات...");
      console.log("📚 الفئات المتاحة:", categoriesData);
      console.log("🎓 الكورسات المتاحة:", coursesData);

      // إذا لم يتم تمرير كورسات، جلب جميع الكورسات المنشورة
      let allCourses = coursesData;
      if (!allCourses) {
        console.log("🔄 جلب جميع الكورسات...");
        allCourses = await fetchFeaturedCourses({ limit: 1000 }); // جلب عدد كبير للحصول على جميع الكورسات
        console.log("📖 جميع الكورسات:", allCourses);
      }

      // الحصول على IDs الفئات التي تحتوي على كورسات
      const categoriesWithCourses = new Set();
      allCourses.forEach((course) => {
        if (course.category) {
          // إذا كانت الفئة object، استخدم الـ id، وإلا استخدم القيمة مباشرة (UUID)
          const categoryId =
            typeof course.category === "object"
              ? course.category.id
              : course.category;

          console.log(
            `📂 كورس "${course.title}" ينتمي لفئة ID: "${categoryId}"`
          );
          console.log(`   - نوع الفئة: ${typeof course.category}`);
          console.log(`   - بيانات الفئة الكاملة:`, course.category);

          if (categoryId) {
            categoriesWithCourses.add(categoryId);
          }
        } else {
          console.log(`⚠️ كورس "${course.title}" ليس له فئة`);
        }
      });

      console.log(
        "🏷️ IDs الفئات التي تحتوي على كورسات:",
        Array.from(categoriesWithCourses)
      );

      // فلترة الفئات لإظهار فقط التي تحتوي على كورسات
      const filtered = categoriesData.filter((category) => {
        // مقارنة بـ ID الفئة
        const categoryId = category.id;
        const hasMatch = categoriesWithCourses.has(categoryId);

        console.log(
          `🔍 فحص فئة "${category.name}" (ID: ${categoryId}): ${
            hasMatch ? "✅ تحتوي على كورسات" : "❌ لا تحتوي على كورسات"
          }`
        );

        return hasMatch;
      });

      console.log("✅ الفئات المفلترة النهائية:", filtered);

      // إذا لم توجد فئات مفلترة، عرض جميع الفئات كخيار احتياطي
      if (filtered.length === 0) {
        console.log("⚠️ لم توجد فئات مفلترة، سيتم عرض جميع الفئات");
        setFilteredCategories(categoriesData);
      } else {
        setFilteredCategories(filtered);
      }
    } catch (error) {
      console.error("خطأ في فلترة الفئات:", error);
      // في حالة الخطأ، إظهار جميع الفئات
      setFilteredCategories(categoriesData);
    }
  };

  // دالة جلب البيانات الأولية - zaki alkholy
  const loadInitialData = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log("🚀 بدء جلب البيانات الأولية...");

      // جلب الفئات والكورسات بشكل متوازي - بدون token
      const [categoriesData, coursesData] = await Promise.all([
        fetchCategories(),
        fetchFeaturedCourses({ limit: 6, randomize: true }),
      ]);

      console.log("📊 البيانات المجلبة:");
      console.log("📚 الفئات:", categoriesData);
      console.log("🎓 الكورسات:", coursesData);

      setCategories(categoriesData);
      setFeaturedCourses(coursesData);

      // فلترة الفئات لإظهار فقط التي تحتوي على كورسات
      filterCategoriesWithCourses(categoriesData, coursesData);
    } catch (err) {
      console.error("خطأ في جلب البيانات الأولية:", err);
      setError(err.message || "حدث خطأ أثناء تحميل الكورسات");
    } finally {
      setLoading(false);
    }
  };

  // دالة جلب الكورسات حسب الفئة - بدون token - zaki alkholy
  const loadCoursesByCategory = async (category) => {
    try {
      setCategoryLoading(true);

      const coursesData = await fetchFeaturedCourses({
        category: category === "الكل" ? null : category,
        limit: 6,
        randomize: true,
      });
      console.log("Courses data:", coursesData);
      setFeaturedCourses(coursesData);

      // إعادة فلترة الفئات بناءً على الكورسات الجديدة إذا كانت الفئة "الكل"
      if (category === "الكل" && categories.length > 0) {
        filterCategoriesWithCourses(categories, coursesData);
      }
    } catch (err) {
      console.error("خطأ في جلب الكورسات حسب الفئة:", err);
      setError(err.message || "حدث خطأ أثناء تحميل الكورسات");
    } finally {
      setCategoryLoading(false);
    }
  };

  // دالة إعادة المحاولة - zaki alkholy
  const handleRetry = () => {
    loadInitialData();
  };

  // دالة تغيير الفئة - zaki alkholy
  const handleCategoryChange = (category) => {
    setSelectedCategory(category);
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.dataset.index);
            setVisibleCards((prev) => [...new Set([...prev, index])]);
          }
        });
      },
      { threshold: 0.1 }
    );

    const cardElements = sectionRef.current?.querySelectorAll("[data-index]");
    cardElements?.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, [featuredCourses]);

  // دوال مساعدة لمعالجة البيانات - zaki alkholy
  const getImageUrl = (thumbnailPath) => {
    if (!thumbnailPath) return "/api/placeholder/300/200";

    if (
      thumbnailPath.startsWith("http://") ||
      thumbnailPath.startsWith("https://")
    ) {
      return thumbnailPath;
    }

    return `${API_BASE_URL}${
      thumbnailPath.startsWith("/") ? thumbnailPath : `/${thumbnailPath}`
    }`;
  };

  const formatPrice = (price, currency = "ر.س") => {
    if (!price) return "مجاني";
    return `${price} ${currency}`;
  };

  const formatDuration = (duration) => {
    if (!duration) return "غير محدد";
    if (typeof duration === "string") return duration;

    const hours = Math.floor(duration / 60);
    const minutes = duration % 60;

    if (hours > 0) {
      return `${hours} ساعة${minutes > 0 ? ` و ${minutes} دقيقة` : ""}`;
    }
    return `${minutes} دقيقة`;
  };

  const renderStars = (rating) => {
    const numRating = parseFloat(rating) || 0;
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < Math.floor(numRating)
            ? "text-yellow-400 fill-current"
            : i < numRating
            ? "text-yellow-400 fill-current opacity-50"
            : "text-gray-300 dark:text-gray-600"
        }`}
      />
    ));
  };

  return (
    <section ref={sectionRef} className="py-20 bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              الكورسات
            </span>
            <br />
            المقترحة
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            اكتشف أفضل الكورسات التعليمية المختارة بعناية من قبل خبرائنا
          </p>

          {/* Category filters - ديناميكي من API */}
          <div className="flex flex-wrap justify-center gap-3">
            <button
              key="الكل"
              onClick={() => handleCategoryChange("الكل")}
              disabled={categoryLoading}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 hover:scale-105 shadow-sm ${
                selectedCategory === "الكل"
                  ? "bg-blue-600 text-white"
                  : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-600 dark:hover:text-blue-400"
              } ${categoryLoading ? "opacity-50 cursor-not-allowed" : ""}`}
            >
              {categoryLoading && selectedCategory === "الكل" && (
                <RefreshCw className="w-4 h-4 animate-spin inline ml-2" />
              )}
              الكل
            </button>
            {filteredCategories.map((category) => (
              <button
                key={category.slug}
                onClick={() => handleCategoryChange(category.name)}
                disabled={categoryLoading}
                className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 hover:scale-105 shadow-sm ${
                  selectedCategory === category.name
                    ? "bg-blue-600 text-white"
                    : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-600 dark:hover:text-blue-400"
                } ${categoryLoading ? "opacity-50 cursor-not-allowed" : ""}`}
              >
                {categoryLoading && selectedCategory === category.name && (
                  <RefreshCw className="w-4 h-4 animate-spin inline ml-2" />
                )}
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-20">
            <div className="text-center">
              <RefreshCw className="w-12 h-12 animate-spin text-blue-600 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-300">
                جاري تحميل الكورسات...
              </p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && !loading && (
          <div className="flex justify-center items-center py-20">
            <div className="text-center">
              <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
              <button
                onClick={handleRetry}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-300"
              >
                إعادة المحاولة
              </button>
            </div>
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && featuredCourses.length === 0 && (
          <div className="flex justify-center items-center py-20">
            <div className="text-center">
              <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-300">
                لا توجد كورسات متاحة حالياً
              </p>
            </div>
          </div>
        )}

        {/* Courses Grid */}
        {!loading && !error && featuredCourses.length > 0 && (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {featuredCourses.map((course, index) => {
              const isVisible = visibleCards.includes(index);

              return (
                <div
                  key={course.slug}
                  data-index={index}
                  className={`group bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden ${
                    isVisible ? "animate-slide-up opacity-100" : "opacity-0"
                  }`}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  {/* Course Image */}
                  <div className="relative overflow-hidden">
                    <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 relative">
                      {course.thumbnail ? (
                        <img
                          src={`${theUrl}${course.thumbnail}`}
                          alt={course.title}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.target.style.display = "none";
                            e.target.nextSibling.style.display = "flex";
                          }}
                        />
                      ) : null}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <Play className="w-16 h-16 text-blue-500 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300" />
                      </div>
                    </div>

                    {/* Overlay */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <Link
                          href={`/student/course/${course.slug}`}
                          className="bg-white text-blue-600 px-4 py-2 rounded-full font-medium shadow-lg transform scale-90 group-hover:scale-100 transition-transform duration-300"
                        >
                          معاينة الكورس
                        </Link>
                      </div>
                    </div>

                    {/* Category Badge */}
                    {/* {course.category && (
                      <div className="absolute top-4 right-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium">
                        {typeof course.category === "object"
                          ? course.category.slug
                          : course.category}
                      </div>
                    )} */}

                    {/* Discount Badge */}
                    {course.discount_price &&
                      course.price &&
                      course.discount_price < course.price && (
                        <div className="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                          خصم{" "}
                          {Math.round(
                            ((course.price - course.discount_price) /
                              course.price) *
                              100
                          )}
                          %
                        </div>
                      )}
                  </div>

                  {/* Course Content */}
                  <div className="p-6">
                    {/* Title */}
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                      {course.title}
                    </h3>

                    {/* Description */}
                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">
                      {course.short_description || course.description}
                    </p>

                    {/* Instructor */}
                    <div className="flex items-center mb-4">
                      <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center ml-3">
                        <span className="text-white text-sm font-bold">
                          {course.instructor?.first_name?.charAt(0) ||
                            course.instructor?.charAt(0) ||
                            "م"}
                        </span>
                      </div>
                      <span className="text-gray-700 dark:text-gray-300 text-sm">
                        {course.instructor?.first_name +
                          " " +
                          course.instructor?.last_name ||
                          course.instructor ||
                          "معلم"}
                      </span>
                    </div>

                    {/* Rating and Stats */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <div className="flex items-center">
                          {renderStars(course.rating)}
                        </div>
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          ({course.rating || "0.0"})
                        </span>
                      </div>
                      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <Users className="w-4 h-4 ml-1" />
                        {course.students_count || 0}
                      </div>
                    </div>

                    {/* Duration and Level */}
                    <div className="flex items-center justify-between mb-4 text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 ml-1" />
                        {formatDuration(course.duration)}
                      </div>
                      <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs">
                        {course.level === "beginner"
                          ? "مبتدئ"
                          : course.level === "intermediate"
                          ? "متوسط"
                          : course.level === "advanced"
                          ? "متقدم"
                          : course.level || "غير محدد"}
                      </span>
                    </div>

                    {/* Price and CTA */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                          {course.discount_price
                            ? formatPrice(course.discount_price)
                            : formatPrice(course.price)}
                        </span>
                        {course.discount_price &&
                          course.price &&
                          course.discount_price < course.price && (
                            <span className="text-sm text-gray-500 dark:text-gray-400 line-through">
                              {formatPrice(course.price)}
                            </span>
                          )}
                      </div>
                      <CourseStartButton course={course} />
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* View All Button - يظهر فقط إذا كانت هناك كورسات */}
        {!loading && !error && featuredCourses.length > 0 && (
          <div className="text-center">
            <Link
              href="/courses"
              className="inline-flex items-center px-8 py-4 text-lg font-bold text-blue-600 dark:text-blue-400 bg-white dark:bg-gray-800 border-2 border-blue-600 dark:border-blue-400 rounded-full shadow-lg hover:shadow-xl hover:bg-blue-50 dark:hover:bg-gray-700 transform hover:scale-105 transition-all duration-300"
            >
              <BookOpen className="w-6 h-6 ml-3" />
              <span>عرض جميع الكورسات</span>
              <ChevronLeft className="w-5 h-5 mr-3 transform group-hover:-translate-x-1 transition-transform duration-300" />
            </Link>
          </div>
        )}
      </div>
    </section>
  );
}
