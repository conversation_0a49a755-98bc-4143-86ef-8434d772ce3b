// صفحة متجر النقاط والمكافآت للطلاب - zaki alkholy
'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useSelector } from 'react-redux';
import Cookies from 'js-cookie';
import { toast } from 'react-hot-toast';
import {
  ShoppingBag,
  Star,
  Gift,
  Ticket,
  Award,
  MessageCircle,
  BookOpen,
  Settings,
  Check,
  X
} from 'lucide-react';

// استيراد خدمات API الجديدة - zaki alkholy
import {
  fetchPointsStore,
  redeemReward,
  fetchStudentPoints,
  fetchWeeklyLeaderboard
} from '@/services/student';

import { selectCurrentUser, selectIsAuthenticated } from '@/store/authSlice';

// مكون لعرض بطاقة المكافأة - zaki alkholy
const RewardCard = ({ reward, userPoints, onRedeem }) => {
  const canAfford = userPoints >= reward.points_cost;
  
  // أيقونات المكافآت - zaki alkholy
  const getIcon = (id) => {
    const icons = {
      discount_10: Ticket,
      discount_20: Ticket,
      certificate: Award,
      consultation: MessageCircle,
      course_unlock: BookOpen,
      priority_support: Settings,
    };
    return icons[id] || Gift;
  };

  const Icon = getIcon(reward.id);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -5 }}
      className={`bg-white dark:bg-gray-800 rounded-xl shadow-lg border-2 p-6 transition-all duration-300 ${
        canAfford
          ? 'border-blue-200 dark:border-blue-700 hover:border-blue-400 dark:hover:border-blue-500 hover:shadow-xl'
          : 'border-gray-200 dark:border-gray-700 opacity-60'
      }`}
    >
      <div className="text-center">
        {/* أيقونة المكافأة - zaki alkholy */}
        <div className={`w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center ${
          canAfford ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'bg-gray-100 dark:bg-gray-700 text-gray-400'
        }`}>
          <span className="text-2xl">{reward.icon}</span>
        </div>

        {/* عنوان المكافأة - zaki alkholy */}
        <h3 className="text-lg font-bold mb-2 text-foreground">{reward.title}</h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">{reward.description}</p>

        {/* تكلفة النقاط - zaki alkholy */}
        <div className="flex items-center justify-center mb-4">
          <Star className="w-5 h-5 text-yellow-500 dark:text-yellow-400 mr-1" />
          <span className="text-xl font-bold text-gray-900 dark:text-white">
            {reward.points_cost.toLocaleString()}
          </span>
          <span className="text-sm text-gray-500 dark:text-gray-400 mr-1">نقطة</span>
        </div>

        {/* زر الاستبدال - zaki alkholy */}
        <button
          onClick={() => canAfford && onRedeem(reward)}
          disabled={!canAfford}
          className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-300 ${
            canAfford
              ? 'bg-blue-600 hover:bg-blue-700 text-white active:scale-95'
              : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
          }`}
        >
          {canAfford ? 'استبدال' : 'نقاط غير كافية'}
        </button>
      </div>
    </motion.div>
  );
};

// مكون لعرض فئات المكافآت - zaki alkholy
const CategoryFilter = ({ categories, activeCategory, onCategoryChange }) => {
  const categoryLabels = {
    all: 'الكل',
    discounts: 'خصومات',
    certificates: 'شهادات',
    services: 'خدمات',
    courses: 'دورات',
  };

  return (
    <div className="flex flex-wrap gap-2 mb-6">
      {Object.keys(categoryLabels).map((category) => (
        <button
          key={category}
          onClick={() => onCategoryChange(category)}
          className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
            activeCategory === category
              ? 'bg-blue-600 text-white shadow-md'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600'
          }`}
        >
          {categoryLabels[category]}
        </button>
      ))}
    </div>
  );
};

// مكون لعرض رصيد النقاط - zaki alkholy
const PointsBalance = ({ points, level }) => {
  const levelColors = {
    bronze: 'text-orange-600 bg-orange-100',
    silver: 'text-gray-600 bg-gray-100',
    gold: 'text-yellow-600 bg-yellow-100',
    platinum: 'text-purple-600 bg-purple-100',
    diamond: 'text-blue-600 bg-blue-100',
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl p-6 mb-8"
    >
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold mb-2">رصيد النقاط</h2>
          <div className="flex items-center">
            <Star className="w-6 h-6 text-yellow-400 mr-2" />
            <span className="text-3xl font-bold">{points.toLocaleString()}</span>
            <span className="text-lg mr-2">نقطة</span>
          </div>
        </div>
        <div className="text-right">
          <p className="text-sm opacity-90 mb-1">المستوى الحالي</p>
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${levelColors[level] || levelColors.bronze}`}>
            {level === 'bronze' && 'البرونزي'}
            {level === 'silver' && 'الفضي'}
            {level === 'gold' && 'الذهبي'}
            {level === 'platinum' && 'البلاتيني'}
            {level === 'diamond' && 'الماسي'}
          </span>
        </div>
      </div>
    </motion.div>
  );
};

// الصفحة الرئيسية لمتجر النقاط - zaki alkholy
export default function PointsStorePage() {
  const [storeData, setStoreData] = useState(null);
  const [pointsData, setPointsData] = useState(null);
  const [leaderboardData, setLeaderboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeCategory, setActiveCategory] = useState('all');
  const [showRedeemModal, setShowRedeemModal] = useState(false);
  const [selectedReward, setSelectedReward] = useState(null);
  const [redeeming, setRedeeming] = useState(false);

  // الحصول على بيانات المستخدم من Redux - zaki alkholy
  const user = useSelector(selectCurrentUser);
  const isAuthenticated = useSelector(selectIsAuthenticated);

  // جلب بيانات المتجر من API - zaki alkholy
  useEffect(() => {
    const fetchAllStoreData = async () => {
      if (!isAuthenticated || !user) {
        setLoading(false);
        return;
      }

      try {
        const token = Cookies.get('authToken') || localStorage.getItem('access_token');
        if (!token) {
          throw new Error('لا يوجد رمز مصادقة');
        }

        // جلب جميع البيانات المطلوبة بشكل متوازي - zaki alkholy
        const [
          storeData,
          pointsData,
          leaderboardData
        ] = await Promise.all([
          fetchPointsStore(token),
          fetchStudentPoints(token),
          fetchWeeklyLeaderboard(token)
        ]);

        setStoreData(storeData);
        setPointsData(pointsData);
        setLeaderboardData(leaderboardData);

      } catch (error) {
        console.error('خطأ في جلب بيانات المتجر:', error);
        setError(error.message);
        toast.error('حدث خطأ في جلب بيانات المتجر');
      } finally {
        setLoading(false);
      }
    };

    fetchAllStoreData();
  }, [isAuthenticated, user]);

  // معالجة استبدال المكافأة - zaki alkholy
  const handleRedeem = async (reward) => {
    setSelectedReward(reward);
    setShowRedeemModal(true);
  };

  // تأكيد الاستبدال - zaki alkholy
  const confirmRedeem = async () => {
    if (!selectedReward) return;

    setRedeeming(true);
    try {
      const token = Cookies.get('authToken') || localStorage.getItem('access_token');
      if (!token) {
        throw new Error('لا يوجد رمز مصادقة');
      }

      // استخدام الخدمة الجديدة للاستبدال - zaki alkholy
      const result = await redeemReward(token, selectedReward.id);

      toast.success('تم استبدال المكافأة بنجاح! 🎉');

      // تحديث البيانات المحلية - zaki alkholy
      if (pointsData) {
        setPointsData({
          ...pointsData,
          total_points: pointsData.total_points - selectedReward.points_cost
        });
      }

      // إعادة جلب بيانات المتجر المحدثة
      const token2 = Cookies.get('authToken') || localStorage.getItem('access_token');
      const updatedStoreData = await fetchPointsStore(token2);
      setStoreData(updatedStoreData);

    } catch (error) {
      console.error('خطأ في استبدال المكافأة:', error);
      toast.error(error.message || 'حدث خطأ في الاستبدال');
    } finally {
      setRedeeming(false);
      setShowRedeemModal(false);
      setSelectedReward(null);
    }
  };

  // فلترة المكافآت حسب الفئة - zaki alkholy
  const getFilteredRewards = () => {
    const rewards = storeData?.rewards || [];

    if (activeCategory === 'all') {
      return rewards;
    }

    return rewards.filter(reward => reward.category === activeCategory);
  };

  // عرض شاشة التحميل - zaki alkholy
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // عرض رسالة خطأ إذا لم يكن المستخدم مسجل دخول - zaki alkholy
  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">يجب تسجيل الدخول أولاً</h2>
          <p className="text-gray-600">يرجى تسجيل الدخول لعرض متجر النقاط</p>
        </div>
      </div>
    );
  }

  // عرض رسالة خطأ إذا فشل تحميل البيانات - zaki alkholy
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 dark:text-red-400 mb-4">حدث خطأ</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors"
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    );
  }

  // دمج البيانات الحقيقية مع البيانات الافتراضية - zaki alkholy
  const displayData = {
    student_points: pointsData?.total_points || 0,
    rewards: storeData?.rewards || [],
    leaderboard: leaderboardData || null
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* العنوان الرئيسي - zaki alkholy */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center">
            <ShoppingBag className="w-8 h-8 mr-3 text-blue-600 dark:text-blue-400" />
            متجر النقاط
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            استبدل نقاطك بمكافآت رائعة ومفيدة
          </p>
        </motion.div>

        {/* رصيد النقاط - zaki alkholy */}
        <PointsBalance
          points={displayData.student_points}
          level={pointsData?.current_level || "bronze"}
        />

        {/* فلتر الفئات - zaki alkholy */}
        <CategoryFilter
          categories={['all', 'discounts', 'certificates', 'services', 'courses']}
          activeCategory={activeCategory}
          onCategoryChange={setActiveCategory}
        />

        {/* شبكة المكافآت - zaki alkholy */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {getFilteredRewards().length > 0 ? (
            getFilteredRewards().map((reward) => (
              <RewardCard
                key={reward.id}
                reward={reward}
                userPoints={displayData.student_points}
                onRedeem={handleRedeem}
              />
            ))
          ) : (
            <div className="col-span-full text-center text-gray-500 dark:text-gray-400 py-12">
              <Gift className="w-16 h-16 mx-auto mb-4 text-gray-400" />
              <p className="text-lg">لا توجد مكافآت متاحة في هذه الفئة</p>
              <p className="text-sm">جرب فئة أخرى أو عد لاحقاً</p>
            </div>
          )}
        </div>

        {/* مودال تأكيد الاستبدال - zaki alkholy */}
        {showRedeemModal && selectedReward && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md w-full mx-4 border border-gray-200 dark:border-gray-700"
            >
              <h3 className="text-xl font-bold mb-4 text-center text-foreground">
                تأكيد الاستبدال
              </h3>

              <div className="text-center mb-6">
                <div className="text-4xl mb-3">{selectedReward.icon}</div>
                <h4 className="font-semibold text-lg mb-2 text-foreground">{selectedReward.title}</h4>
                <p className="text-gray-600 dark:text-gray-400 mb-4">{selectedReward.description}</p>

                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">سيتم خصم:</p>
                  <div className="flex items-center justify-center">
                    <Star className="w-5 h-5 text-yellow-500 dark:text-yellow-400 mr-1" />
                    <span className="text-xl font-bold text-foreground">
                      {selectedReward.points_cost.toLocaleString()}
                    </span>
                    <span className="text-sm text-gray-500 dark:text-gray-400 mr-1">نقطة</span>
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => setShowRedeemModal(false)}
                  disabled={redeeming}
                  className="flex-1 py-3 px-4 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  إلغاء
                </button>
                <button
                  onClick={confirmRedeem}
                  disabled={redeeming}
                  className="flex-1 py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {redeeming ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      جاري الاستبدال...
                    </>
                  ) : (
                    'تأكيد الاستبدال'
                  )}
                </button>
              </div>
            </motion.div>
          </div>
        )}
      </div>
    </div>
  );
}
