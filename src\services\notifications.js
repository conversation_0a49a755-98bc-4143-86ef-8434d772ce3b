import axios from "axios";
import { API_BASE_URL } from '../config/api';
// ====================================get Notification
export async function fetchNotifications(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
  };

  const response = await axios.get(`${API_BASE_URL}/api/notifications/`, {
    headers,
  });

  return response.data;
}

// =========================================Get Notification Data
export async function markNotificationAsRead(id, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
  };

  const data = {
    is_read: true,
  };

  const response = await axios.patch(
    `${API_BASE_URL}/api/notifications/${id}/`,
    data,
    { headers }
  );

  return response.data;
}