"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON><PERSON>, Users, ChevronLeft, Sparkles, Zap, Target } from "lucide-react";

export default function FinalCTASection() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
          }
        });
      },
      { threshold: 0.3 }
    );

    const section = document.getElementById('final-cta');
    if (section) observer.observe(section);

    return () => observer.disconnect();
  }, []);

  return (
    <section 
      id="final-cta"
      className="relative py-20 overflow-hidden bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/5 rounded-full blur-3xl animate-pulse-slow"></div>
        
        {/* Floating Icons */}
        <div className="absolute top-20 right-20 text-white/20 animate-float">
          <BookOpen className="w-12 h-12" />
        </div>
        <div className="absolute bottom-20 left-20 text-white/20 animate-float" style={{ animationDelay: '1s' }}>
          <Users className="w-10 h-10" />
        </div>
        <div className="absolute top-1/3 left-1/4 text-white/20 animate-float" style={{ animationDelay: '3s' }}>
          <Sparkles className="w-8 h-8" />
        </div>
        <div className="absolute bottom-1/3 right-1/4 text-white/20 animate-float" style={{ animationDelay: '4s' }}>
          <Zap className="w-8 h-8" />
        </div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className={`text-center ${isVisible ? 'animate-slide-up opacity-100' : 'opacity-0'}`}>
          {/* Main Heading */}
          <div className="mb-8">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
              سواء كنت مدرّس
              <br />
              <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                أو متعلّم
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-blue-100 mb-4">
              مكانك معنا
            </p>
            <p className="text-lg text-blue-200 max-w-3xl mx-auto">
              ابدأ رحلتك التعليمية اليوم وانضم إلى آلاف الطلاب والمدرسين الذين يحققون أهدافهم معنا
            </p>
          </div>

          {/* Features Grid */}
          <div className={`grid md:grid-cols-3 gap-8 mb-12 ${isVisible ? 'animate-slide-up opacity-100' : 'opacity-0'}`} style={{ animationDelay: '0.3s' }}>
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <Target className="w-12 h-12 text-yellow-300 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-white mb-2">تعلم بهدف</h3>
              <p className="text-blue-100 text-sm">
                كورسات مصممة لتحقيق أهدافك المهنية والشخصية
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <Users className="w-12 h-12 text-green-300 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-white mb-2">مجتمع داعم</h3>
              <p className="text-blue-100 text-sm">
                انضم لمجتمع من المتعلمين والخبراء المتفاعلين
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <Zap className="w-12 h-12 text-orange-300 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-white mb-2">نتائج سريعة</h3>
              <p className="text-blue-100 text-sm">
                طرق تعليم حديثة تضمن تحقيق النتائج بأسرع وقت
              </p>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className={`flex flex-col sm:flex-row gap-6 justify-center items-center mb-12 ${isVisible ? 'animate-slide-up opacity-100' : 'opacity-0'}`} style={{ animationDelay: '0.6s' }}>
            <Link
              href="/courses"
              className="group relative inline-flex items-center px-8 py-4 text-lg font-bold text-blue-600 bg-white rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-white/50"
            >
              <BookOpen className="w-6 h-6 ml-3" />
              <span>ابدأ التعلّم الآن</span>
              <ChevronLeft className="w-5 h-5 mr-3 transform group-hover:-translate-x-1 transition-transform duration-300" />
              
              {/* Glow effect */}
              <div className="absolute inset-0 rounded-full bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
            </Link>
            
            <Link
              href="/areYouInstructor"
              className="group relative inline-flex items-center px-8 py-4 text-lg font-bold text-white bg-transparent border-2 border-white rounded-full shadow-lg hover:shadow-xl hover:bg-white hover:text-blue-600 transform hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-white/50"
            >
              <Users className="w-6 h-6 ml-3" />
              <span>انضم كمدرّس</span>
              <ChevronLeft className="w-5 h-5 mr-3 transform group-hover:-translate-x-1 transition-transform duration-300" />
            </Link>
          </div>

          {/* Trust Indicators */}
          <div className={`${isVisible ? 'animate-fade-in opacity-100' : 'opacity-0'}`} style={{ animationDelay: '0.9s' }}>
            <p className="text-blue-200 mb-6">انضم إلى أكثر من</p>
            <div className="flex justify-center items-center space-x-8 space-x-reverse">
              <div className="text-center">
                <div className="text-3xl font-bold text-white">15,000+</div>
                <div className="text-blue-200 text-sm">طالب نشط</div>
              </div>
              <div className="w-px h-12 bg-white/30"></div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white">200+</div>
                <div className="text-blue-200 text-sm">كورس متاح</div>
              </div>
              <div className="w-px h-12 bg-white/30"></div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white">50+</div>
                <div className="text-blue-200 text-sm">مدرس خبير</div>
              </div>
            </div>
          </div>

          {/* Bottom Message */}
          <div className={`mt-12 ${isVisible ? 'animate-fade-in opacity-100' : 'opacity-0'}`} style={{ animationDelay: '1.2s' }}>
            <p className="text-blue-100 text-lg">
              🚀 ابدأ مجاناً اليوم - لا توجد رسوم خفية
            </p>
          </div>
        </div>
      </div>

      {/* Bottom Wave */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" className="w-full h-12 fill-white dark:fill-gray-800">
          <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25"></path>
          <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5"></path>
          <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"></path>
        </svg>
      </div>
    </section>
  );
}
