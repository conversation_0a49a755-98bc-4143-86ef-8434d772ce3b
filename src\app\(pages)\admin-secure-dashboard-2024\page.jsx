"use client";
import React, { useState, useEffect } from "react";
import axios from "axios";
import {
  DollarSign,
  Users,
  BookOpen,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Eye
} from "lucide-react";

export default function AdminDashboard() {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      const token = localStorage.getItem("admin_token");
      const response = await axios.get("http://127.0.0.1:8000/api/admin-dashboard/stats/", {
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      setStats(response.data);
    } catch (error) {
      console.error("Error fetching stats:", error);
      setError("حدث خطأ في تحميل البيانات");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2 space-x-reverse">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="text-gray-600 dark:text-gray-400">جاري تحميل الإحصائيات...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div className="flex items-center">
          <AlertCircle className="w-5 h-5 text-red-500 ml-2" />
          <span className="text-red-700 dark:text-red-400">{error}</span>
        </div>
      </div>
    );
  }

  const StatCard = ({ title, value, icon: Icon, color, subtitle, trend }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">{value}</p>
          {subtitle && (
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{subtitle}</p>
          )}
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
      </div>
      {trend && (
        <div className="mt-4 flex items-center">
          <TrendingUp className="w-4 h-4 text-green-500 ml-1" />
          <span className="text-sm text-green-600 dark:text-green-400">{trend}</span>
        </div>
      )}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          مرحباً بك في لوحة تحكم المدير
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          نظرة عامة على أداء المنصة والمبيعات والتحويلات
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* إجمالي الإيرادات */}
        <StatCard
          title="إجمالي الإيرادات"
          value={`${stats?.revenue?.total_revenue?.toFixed(2) || 0} جنيه`}
          subtitle={`عمولة المنصة: ${stats?.revenue?.platform_fee?.toFixed(2) || 0} جنيه`}
          icon={DollarSign}
          color="bg-green-500"
        />

        {/* إجمالي الطلبات */}
        <StatCard
          title="إجمالي الطلبات"
          value={stats?.orders?.total_orders || 0}
          subtitle={`معلقة: ${stats?.orders?.pending_orders || 0}`}
          icon={BookOpen}
          color="bg-blue-500"
        />

        {/* المعلمين النشطين */}
        <StatCard
          title="المعلمين"
          value={stats?.users?.total_instructors || 0}
          subtitle={`نشط: ${stats?.users?.active_instructors || 0}`}
          icon={Users}
          color="bg-purple-500"
        />

        {/* التحويلات المعلقة */}
        <StatCard
          title="التحويلات المعلقة"
          value={stats?.payouts?.pending_payouts || 0}
          subtitle={`مكتملة: ${stats?.payouts?.completed_payouts || 0}`}
          icon={Clock}
          color="bg-orange-500"
        />
      </div>

      {/* Detailed Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Breakdown */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            تفصيل الإيرادات
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">إجمالي المبيعات</span>
              <span className="font-semibold text-gray-900 dark:text-white">
                {stats?.revenue?.total_revenue?.toFixed(2) || 0} جنيه
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">عمولة المنصة (5%)</span>
              <span className="font-semibold text-green-600">
                {stats?.revenue?.platform_fee?.toFixed(2) || 0} جنيه
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">أرباح المعلمين</span>
              <span className="font-semibold text-blue-600">
                {stats?.revenue?.instructor_earnings?.toFixed(2) || 0} جنيه
              </span>
            </div>
            <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600 dark:text-gray-400">المحول للمعلمين</span>
                <span className="font-semibold text-purple-600">
                  {stats?.payouts?.total_payouts_amount?.toFixed(2) || 0} جنيه
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Users Overview */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            نظرة عامة على المستخدمين
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">إجمالي المعلمين</span>
              <span className="font-semibold text-gray-900 dark:text-white">
                {stats?.users?.total_instructors || 0}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">المعلمين النشطين</span>
              <span className="font-semibold text-green-600">
                {stats?.users?.active_instructors || 0}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">إجمالي الطلاب</span>
              <span className="font-semibold text-gray-900 dark:text-white">
                {stats?.users?.total_students || 0}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">الطلاب النشطين</span>
              <span className="font-semibold text-blue-600">
                {stats?.users?.active_students || 0}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Courses Overview */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          إحصائيات الكورسات
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {stats?.courses?.total_courses || 0}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">إجمالي الكورسات</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {stats?.courses?.published_courses || 0}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">كورسات منشورة</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {(stats?.courses?.total_courses || 0) - (stats?.courses?.published_courses || 0)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">كورسات غير منشورة</div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          إجراءات سريعة
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <a
            href="/admin-secure-dashboard-2024/pending-payouts"
            className="flex items-center p-4 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg hover:bg-orange-100 dark:hover:bg-orange-900/30 transition-colors"
          >
            <Clock className="w-5 h-5 text-orange-600 ml-3" />
            <span className="text-orange-700 dark:text-orange-400 font-medium">
              معالجة التحويلات المعلقة
            </span>
          </a>
          
          <a
            href="/admin-secure-dashboard-2024/completed-payouts"
            className="flex items-center p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
          >
            <CheckCircle className="w-5 h-5 text-green-600 ml-3" />
            <span className="text-green-700 dark:text-green-400 font-medium">
              عرض التحويلات المكتملة
            </span>
          </a>
          
          <a
            href="/admin-secure-dashboard-2024/all-orders"
            className="flex items-center p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
          >
            <Eye className="w-5 h-5 text-blue-600 ml-3" />
            <span className="text-blue-700 dark:text-blue-400 font-medium">
              مراجعة جميع الطلبات
            </span>
          </a>
        </div>
      </div>
    </div>
  );
}
