"use client";
import React, { useEffect, useRef, useState } from "react";
import Link from "next/link";
import { Star, Users, BookOpen, Award, ChevronLeft } from "lucide-react";

export default function InstructorsSection() {
  const [visibleCards, setVisibleCards] = useState([]);
  const sectionRef = useRef(null);

  // Mock data for featured instructors
  const featuredInstructors = [
    {
      id: 1,
      name: "د. أحمد محمد",
      title: "خبير تطوير الويب",
      image: "/api/placeholder/150/150",
      rating: 4.9,
      students: 5200,
      courses: 12,
      specialties: ["React", "Node.js", "JavaScript"],
      experience: "10+ سنوات",
      description: "مطور ويب محترف مع خبرة واسعة في تطوير التطبيقات الحديثة",
      achievements: ["أفضل مدرس 2023", "شهادة AWS", "خبير React معتمد"]
    },
    {
      id: 2,
      name: "أ. فاطمة أحمد",
      title: "استشارية التسويق الرقمي",
      image: "/api/placeholder/150/150",
      rating: 4.8,
      students: 3800,
      courses: 8,
      specialties: ["SEO", "Social Media", "Google Ads"],
      experience: "8+ سنوات",
      description: "خبيرة في التسويق الرقمي مع سجل حافل في زيادة المبيعات",
      achievements: ["شهادة Google", "خبيرة Facebook", "مستشارة معتمدة"]
    },
    {
      id: 3,
      name: "أ. محمد علي",
      title: "مصمم جرافيك محترف",
      image: "/api/placeholder/150/150",
      rating: 4.7,
      students: 4500,
      courses: 15,
      specialties: ["Photoshop", "Illustrator", "UI/UX"],
      experience: "12+ سنة",
      description: "مصمم إبداعي مع خبرة في تصميم الهويات البصرية والواجهات",
      achievements: ["جائزة التصميم", "Adobe Expert", "مصمم معتمد"]
    },
    {
      id: 4,
      name: "د. سارة خالد",
      title: "خبيرة إدارة المشاريع",
      image: "/api/placeholder/150/150",
      rating: 4.9,
      students: 2900,
      courses: 6,
      specialties: ["PMP", "Agile", "Scrum"],
      experience: "15+ سنة",
      description: "مديرة مشاريع معتمدة مع خبرة في إدارة المشاريع الكبيرة",
      achievements: ["شهادة PMP", "Scrum Master", "خبيرة Agile"]
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.dataset.index);
            setVisibleCards(prev => [...new Set([...prev, index])]);
          }
        });
      },
      { threshold: 0.1 }
    );

    const cardElements = sectionRef.current?.querySelectorAll('[data-index]');
    cardElements?.forEach(el => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < Math.floor(rating)
            ? 'text-yellow-400 fill-current'
            : i < rating
            ? 'text-yellow-400 fill-current opacity-50'
            : 'text-gray-300 dark:text-gray-600'
        }`}
      />
    ));
  };

  return (
    <section ref={sectionRef} className="py-20 bg-white dark:bg-gray-800">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              قابل
            </span>
            <br />
            المدرسين
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            تعرف على نخبة من أفضل المدرسين والخبراء في مختلف المجالات
          </p>
        </div>

        {/* Instructors Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {featuredInstructors.map((instructor, index) => {
            const isVisible = visibleCards.includes(index);
            
            return (
              <div
                key={instructor.id}
                data-index={index}
                className={`group relative bg-white dark:bg-gray-700 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden ${
                  isVisible ? 'animate-slide-up opacity-100' : 'opacity-0'
                }`}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {/* Background Pattern */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 opacity-50"></div>
                
                <div className="relative p-6 text-center">
                  {/* Instructor Image */}
                  <div className="relative mx-auto mb-4">
                    <div className="w-24 h-24 mx-auto rounded-full bg-gradient-to-br from-blue-500 to-purple-500 p-1">
                      <div className="w-full h-full rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                        <span className="text-2xl font-bold text-gray-600 dark:text-gray-300">
                          {instructor.name.split(' ')[1]?.charAt(0) || instructor.name.charAt(0)}
                        </span>
                      </div>
                    </div>
                    
                    {/* Online indicator */}
                    <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white dark:border-gray-700 flex items-center justify-center">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                  </div>

                  {/* Instructor Info */}
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-1 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                    {instructor.name}
                  </h3>
                  <p className="text-sm text-blue-600 dark:text-blue-400 mb-3 font-medium">
                    {instructor.title}
                  </p>

                  {/* Rating */}
                  <div className="flex items-center justify-center mb-3">
                    <div className="flex items-center ml-2">
                      {renderStars(instructor.rating)}
                    </div>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      ({instructor.rating})
                    </span>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Users className="w-4 h-4 text-blue-500 ml-1" />
                        <span className="text-sm font-bold text-gray-900 dark:text-white">
                          {instructor.students.toLocaleString()}
                        </span>
                      </div>
                      <span className="text-xs text-gray-500 dark:text-gray-400">طالب</span>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <BookOpen className="w-4 h-4 text-purple-500 ml-1" />
                        <span className="text-sm font-bold text-gray-900 dark:text-white">
                          {instructor.courses}
                        </span>
                      </div>
                      <span className="text-xs text-gray-500 dark:text-gray-400">كورس</span>
                    </div>
                  </div>

                  {/* Specialties */}
                  <div className="mb-4">
                    <div className="flex flex-wrap justify-center gap-1">
                      {instructor.specialties.slice(0, 2).map((specialty, idx) => (
                        <span
                          key={idx}
                          className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 text-xs rounded-full"
                        >
                          {specialty}
                        </span>
                      ))}
                      {instructor.specialties.length > 2 && (
                        <span className="px-2 py-1 bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-400 text-xs rounded-full">
                          +{instructor.specialties.length - 2}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Hover Content */}
                  <div className="absolute inset-0 bg-white dark:bg-gray-700 p-6 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-full group-hover:translate-y-0">
                    <div className="h-full flex flex-col justify-center">
                      <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                        {instructor.name}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                        {instructor.description}
                      </p>
                      
                      {/* Experience */}
                      <div className="flex items-center justify-center mb-3">
                        <Award className="w-4 h-4 text-yellow-500 ml-1" />
                        <span className="text-sm text-gray-600 dark:text-gray-300">
                          {instructor.experience}
                        </span>
                      </div>

                      {/* Achievements */}
                      <div className="mb-4">
                        {instructor.achievements.slice(0, 2).map((achievement, idx) => (
                          <div key={idx} className="text-xs text-blue-600 dark:text-blue-400 mb-1">
                            • {achievement}
                          </div>
                        ))}
                      </div>

                      {/* View Profile Button */}
                      <Link
                        href={`/instructor/${instructor.id}`}
                        className="inline-flex items-center justify-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg text-sm font-medium hover:shadow-lg transform hover:scale-105 transition-all duration-300"
                      >
                        <span>عرض الملف الشخصي</span>
                        <ChevronLeft className="w-4 h-4 mr-2" />
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Bottom Stats */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-2xl p-8">
          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">50+</div>
              <div className="text-gray-600 dark:text-gray-300">مدرس خبير</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">15,000+</div>
              <div className="text-gray-600 dark:text-gray-300">طالب راضي</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-indigo-600 dark:text-indigo-400 mb-2">4.8</div>
              <div className="text-gray-600 dark:text-gray-300">متوسط التقييم</div>
            </div>
          </div>
        </div>

        {/* CTA */}
        <div className="text-center mt-12">
          <Link
            href="/instructors"
            className="inline-flex items-center px-8 py-4 text-lg font-bold text-blue-600 dark:text-blue-400 bg-white dark:bg-gray-700 border-2 border-blue-600 dark:border-blue-400 rounded-full shadow-lg hover:shadow-xl hover:bg-blue-50 dark:hover:bg-gray-600 transform hover:scale-105 transition-all duration-300"
          >
            <Users className="w-6 h-6 ml-3" />
            <span>تصفح جميع المدرسين</span>
            <ChevronLeft className="w-5 h-5 mr-3" />
          </Link>
        </div>
      </div>
    </section>
  );
}
