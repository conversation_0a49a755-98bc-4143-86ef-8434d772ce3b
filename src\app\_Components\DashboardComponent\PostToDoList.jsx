import { useState } from "react";
import Cookies from "js-cookie";
import { createInstructorTask } from "../../../services/newInstructorApis";

const AddTaskForm = ({ onTaskAdded }) => {
  const [title, setTitle] = useState("");

  const handleSubmit = async (e) => {
    e.preventDefault();
    const token = Cookies.get("authToken");
    if (!token || !title.trim()) return;

    try {
      const newTask = await createInstructorTask({ title }, token);
      setTitle("");
      onTaskAdded(newTask); // تحدث الواجهة أو القائمة
    } catch (e) {
      alert("فشل في إضافة المهمة");
    }
  };

  return (
    <form onSubmit={handleSubmit} className="flex gap-2 mt-4">
      <input
        value={title}
        onChange={(e) => setTitle(e.target.value)}
        placeholder="أضف مهمة جديدة"
        className="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300  bg-white dark:bg-gray-700 text-foreground p-2 rounded w-full focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      />
      <button
        type="submit"
        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
      >
        إضافة
      </button>
    </form>
  );
};

export default AddTaskForm;
