// صفحة تتبع تقدم الطالب مع النقاط والإنجازات - zaki alkholy
'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useSelector } from 'react-redux';
import Cookies from 'js-cookie';
import { toast } from 'react-hot-toast';
import {
  Trophy,
  Star,
  Target,
  TrendingUp,
  Award,
  Calendar,
  BookOpen,
  CheckCircle,
  Clock,
  Zap,
  RefreshCw
} from 'lucide-react';

// استيراد خدمات API الجديدة - zaki alkholy
import {
  fetchStudentDashboard,
  fetchStudentPoints,
  fetchStudentAchievements,
  fetchStudentLearningStreak,
  fetchStudentComparison
} from '@/services/student';

import { selectCurrentUser, selectIsAuthenticated } from '@/store/authSlice';
import { PageLoader } from '@/components/common/UniversalLoader';

// مكون لعرض إحصائيات سريعة - zaki alkholy
const StatCard = ({ icon: Icon, title, value, subtitle, color = "blue" }) => {
  const colorClasses = {
    blue: "bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 border-blue-200 dark:border-blue-700",
    green: "bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 border-green-200 dark:border-green-700",
    purple: "bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 border-purple-200 dark:border-purple-700",
    orange: "bg-orange-50 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 border-orange-200 dark:border-orange-700",
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`p-6 rounded-xl border-2 ${colorClasses[color]} hover:shadow-lg transition-all duration-300`}
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
          <p className="text-2xl font-bold mt-1 text-foreground">{value}</p>
          {subtitle && <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{subtitle}</p>}
        </div>
        <Icon className="w-8 h-8" />
      </div>
    </motion.div>
  );
};

// مكون لعرض شريط التقدم - zaki alkholy
const ProgressBar = ({ percentage, label, color = "blue" }) => {
  const colorClasses = {
    blue: "bg-blue-500 dark:bg-blue-400",
    green: "bg-green-500 dark:bg-green-400",
    purple: "bg-purple-500 dark:bg-purple-400",
    orange: "bg-orange-500 dark:bg-orange-400",
  };

  return (
    <div className="mb-4">
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{label}</span>
        <span className="text-sm text-gray-500 dark:text-gray-400">{percentage}%</span>
      </div>
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <motion.div
          initial={{ width: 0 }}
          animate={{ width: `${percentage}%` }}
          transition={{ duration: 1, ease: "easeOut" }}
          className={`h-2 rounded-full ${colorClasses[color]}`}
        />
      </div>
    </div>
  );
};

// مكون لعرض الإنجازات - zaki alkholy
const AchievementCard = ({ achievement, isEarned }) => {
  const progressPercentage = isEarned ? 100 :
    achievement.progress && achievement.required_count ?
    (achievement.progress / achievement.required_count) * 100 : 0;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`p-4 rounded-lg border-2 transition-all duration-300 ${
        isEarned
          ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-300 dark:border-yellow-700 shadow-md hover:shadow-lg'
          : 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
      }`}
    >
      <div className="text-center">
        <div className="text-3xl mb-2">{achievement.icon || '🏆'}</div>
        <h4 className="font-semibold text-sm mb-1 text-foreground">{achievement.name}</h4>
        <p className="text-xs text-gray-600 dark:text-gray-400 mb-3">{achievement.description}</p>

        {isEarned ? (
          <div className="flex items-center justify-center text-yellow-600 dark:text-yellow-400">
            <Trophy className="w-4 h-4 mr-1" />
            <span className="text-xs font-medium">مُحقق</span>
          </div>
        ) : (
          <div className="space-y-2">
            {achievement.required_count && (
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {achievement.progress || 0}/{achievement.required_count}
              </div>
            )}
            {achievement.points_reward && (
              <div className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                {achievement.points_reward} نقطة
              </div>
            )}
            {progressPercentage > 0 && (
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                <div
                  className="bg-blue-500 dark:bg-blue-400 h-1.5 rounded-full transition-all duration-300"
                  style={{ width: `${progressPercentage}%` }}
                />
              </div>
            )}
          </div>
        )}
      </div>
    </motion.div>
  );
};

// الصفحة الرئيسية لتتبع التقدم - zaki alkholy
export default function StudentProgressPage() {
  const [studentData, setStudentData] = useState(null);
  const [pointsData, setPointsData] = useState(null);
  const [achievementsData, setAchievementsData] = useState(null);
  const [streakData, setStreakData] = useState(null);
  const [comparisonData, setComparisonData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');

  // الحصول على بيانات المستخدم من Redux - zaki alkholy
  const user = useSelector(selectCurrentUser);
  const isAuthenticated = useSelector(selectIsAuthenticated);

  // جلب بيانات الطالب من API - zaki alkholy
  useEffect(() => {
    const fetchAllStudentData = async () => {
      if (!isAuthenticated || !user) {
        setLoading(false);
        return;
      }

      try {
        const token = Cookies.get('authToken') || localStorage.getItem('access_token');
        if (!token) {
          throw new Error('لا يوجد رمز مصادقة');
        }

        // جلب جميع البيانات المطلوبة بشكل متوازي - zaki alkholy
        const [
          dashboardData,
          pointsData,
          achievementsData,
          streakData,
          comparisonData
        ] = await Promise.all([
          fetchStudentDashboard(token),
          fetchStudentPoints(token),
          fetchStudentAchievements(token),
          fetchStudentLearningStreak(token),
          fetchStudentComparison(token)
        ]);

        setStudentData(dashboardData);
        setPointsData(pointsData);
        setAchievementsData(achievementsData);
        setStreakData(streakData);
        setComparisonData(comparisonData);

      } catch (error) {
        console.error('خطأ في جلب بيانات الطالب:', error);

        // تحديد نوع الخطأ وعرض رسالة مناسبة - zaki alkholy
        if (error.response?.status === 401) {
          setError('انتهت صلاحية جلسة تسجيل الدخول. يرجى تسجيل الدخول مرة أخرى.');
          toast.error('يرجى تسجيل الدخول مرة أخرى');
        } else if (error.response?.status === 403) {
          setError('ليس لديك صلاحية للوصول إلى هذه البيانات.');
          toast.error('ليس لديك صلاحية للوصول');
        } else if (error.response?.status >= 500) {
          setError('خطأ في الخادم. يرجى المحاولة لاحقاً.');
          toast.error('خطأ في الخادم');
        } else if (error.code === 'NETWORK_ERROR') {
          setError('خطأ في الاتصال بالشبكة. تحقق من اتصالك بالإنترنت.');
          toast.error('خطأ في الاتصال بالشبكة');
        } else {
          setError(error.message || 'حدث خطأ غير متوقع');
          toast.error('حدث خطأ في جلب البيانات');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchAllStudentData();
  }, [isAuthenticated, user]);

  // عرض شاشة التحميل - zaki alkholy
  if (loading) {
    return <PageLoader text="جاري تحميل بياناتك..." />;
  }

  // عرض رسالة خطأ إذا لم يكن المستخدم مسجل دخول - zaki alkholy
  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">يجب تسجيل الدخول أولاً</h2>
          <p className="text-gray-600 dark:text-gray-400">يرجى تسجيل الدخول لعرض تقدمك</p>
        </div>
      </div>
    );
  }

  // عرض رسالة خطأ إذا فشل تحميل البيانات - zaki alkholy
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 dark:text-red-400 mb-4">حدث خطأ</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors"
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    );
  }

  // دمج البيانات الحقيقية مع البيانات الافتراضية - zaki alkholy
  const displayData = {
    points_profile: pointsData || {
      total_points: 0,
      current_level: 'bronze',
      lessons_completed: 0,
      quizzes_passed: 0,
      login_streak: streakData?.streak_info?.current_streak || 0,
    },
    recent_achievements: achievementsData?.earned_achievements || [],
    current_courses: studentData?.current_courses || [],
    progress_summary: {
      total_lessons: studentData?.progress_summary?.total_lessons || 0,
      completed_lessons: studentData?.progress_summary?.completed_lessons || 0,
      completion_rate: studentData?.progress_summary?.completion_rate || 0,
      total_watch_time_hours: Math.round((studentData?.progress_summary?.total_watch_time_minutes || 0) / 60 * 10) / 10,
    },
    comparison: comparisonData || null,
    streak: streakData || null
  };

  // طباعة البيانات للتطوير - zaki alkholy
  console.log('Student Dashboard Data:', studentData);
  console.log('Points Data:', pointsData);
  console.log('Achievements Data:', achievementsData);
  console.log('Streak Data:', streakData);
  console.log('Comparison Data:', comparisonData);

  // إضافة بيانات افتراضية للتطوير إذا لم تكن البيانات متوفرة - zaki alkholy
  if (!studentData && !pointsData && !achievementsData) {
    console.log('استخدام البيانات الافتراضية للتطوير...');
    displayData.points_profile = {
      total_points: 1250,
      current_level: 'silver',
      lessons_completed: 15,
      quizzes_passed: 8,
      login_streak: 5,
    };
    displayData.current_courses = [
      {
        course_id: '1',
        title: 'أساسيات البرمجة',
        instructor: 'أحمد محمد',
        progress_percentage: 75,
        completed_lessons: 12,
        total_lessons: 16,
      },
      {
        course_id: '2',
        title: 'تطوير المواقع',
        instructor: 'فاطمة علي',
        progress_percentage: 40,
        completed_lessons: 6,
        total_lessons: 15,
      }
    ];
    displayData.recent_achievements = [
      {
        id: '1',
        achievement: {
          name: 'أول خطوة',
          description: 'أكمل أول درس',
          icon: '🎯'
        }
      },
      {
        id: '2',
        achievement: {
          name: 'متعلم نشط',
          description: 'سجل دخول لمدة 5 أيام متتالية',
          icon: '🔥'
        }
      }
    ];
    displayData.progress_summary = {
      total_lessons: 31,
      completed_lessons: 18,
      completion_rate: 58,
      total_watch_time_hours: 12.5,
    };
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* العنوان الرئيسي - zaki alkholy */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                لوحة تحكم التقدم
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                تابع تقدمك ونقاطك وإنجازاتك في رحلة التعلم
              </p>
            </div>

            {/* مؤشر المستوى الحالي وزر التحديث - zaki alkholy */}
            <div className="mt-4 md:mt-0 flex items-center space-x-4">
              <button
                onClick={() => window.location.reload()}
                className="flex items-center px-4 py-2 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                title="تحديث البيانات"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                تحديث
              </button>

              <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-full">
                <div className="flex items-center space-x-3">
                  <Star className="w-5 h-5" />
                  <span className="font-semibold">
                    المستوى: {displayData.points_profile?.current_level || 'مبتدئ'}
                  </span>
                  <span className="text-blue-100">
                    ({(displayData.points_profile?.total_points || 0).toLocaleString()} نقطة)
                  </span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* الإحصائيات السريعة - zaki alkholy */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            icon={Star}
            title="إجمالي النقاط"
            value={(displayData.points_profile?.total_points || 0).toLocaleString()}
            subtitle={`المستوى: ${displayData.points_profile?.current_level || 'مبتدئ'}`}
            color="blue"
          />
          <StatCard
            icon={BookOpen}
            title="الدروس المكتملة"
            value={displayData.points_profile?.lessons_completed || displayData.progress_summary.completed_lessons || 0}
            subtitle={`من أصل ${displayData.progress_summary.total_lessons || 0}`}
            color="green"
          />
          <StatCard
            icon={CheckCircle}
            title="الاختبارات الناجحة"
            value={displayData.points_profile?.quizzes_passed || 0}
            subtitle="اختبار مُجتاز"
            color="purple"
          />
          <StatCard
            icon={Zap}
            title="سلسلة التعلم"
            value={`${displayData.streak?.current_streak || displayData.points_profile?.login_streak || 0} أيام`}
            subtitle="متتالية"
            color="orange"
          />
        </div>

        {/* التبويبات - zaki alkholy */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'overview', label: 'نظرة عامة', icon: TrendingUp },
                { id: 'courses', label: 'الدورات', icon: BookOpen },
                { id: 'achievements', label: 'الإنجازات', icon: Trophy },
                { id: 'goals', label: 'الأهداف', icon: Target },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  }`}
                >
                  <tab.icon className="w-4 h-4 mr-2" />
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {/* تبويب النظرة العامة - zaki alkholy */}
            {activeTab === 'overview' && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* التقدم العام - zaki alkholy */}
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                    <h3 className="text-lg font-semibold mb-4 flex items-center text-foreground">
                      <TrendingUp className="w-5 h-5 mr-2 text-blue-500 dark:text-blue-400" />
                      التقدم العام
                    </h3>
                    <ProgressBar
                      percentage={Math.round(displayData.progress_summary.completion_rate || 0)}
                      label="معدل إكمال الدروس"
                      color="blue"
                    />
                    <div className="grid grid-cols-2 gap-4 mt-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                          {displayData.progress_summary.total_watch_time_hours || 0}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">ساعة مشاهدة</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                          {Math.round(displayData.progress_summary.completion_rate || 0)}%
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">معدل الإكمال</p>
                      </div>
                    </div>

                    {/* إحصائيات إضافية - zaki alkholy */}
                    <div className="grid grid-cols-2 gap-4 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                      <div className="text-center">
                        <p className="text-lg font-bold text-purple-600 dark:text-purple-400">
                          {displayData.current_courses?.length || 0}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">دورة مسجل بها</p>
                      </div>
                      <div className="text-center">
                        <p className="text-lg font-bold text-orange-600 dark:text-orange-400">
                          {displayData.recent_achievements?.length || 0}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">إنجاز محقق</p>
                      </div>
                    </div>
                  </div>

                  {/* الإنجازات الأخيرة - zaki alkholy */}
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                    <h3 className="text-lg font-semibold mb-4 flex items-center text-foreground">
                      <Award className="w-5 h-5 mr-2 text-yellow-500 dark:text-yellow-400" />
                      الإنجازات الأخيرة
                    </h3>
                    <div className="space-y-3">
                      {displayData.recent_achievements.length > 0 ? (
                        displayData.recent_achievements.slice(0, 3).map((achievement) => (
                          <div key={achievement.id} className="flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg">
                            <span className="text-2xl mr-3">{achievement.achievement?.icon || '🏆'}</span>
                            <div>
                              <p className="font-medium text-sm text-foreground">{achievement.achievement?.name || achievement.name}</p>
                              <p className="text-xs text-gray-600 dark:text-gray-400">{achievement.achievement?.description || achievement.description}</p>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center text-gray-500 dark:text-gray-400 py-4">
                          <p>لا توجد إنجازات بعد</p>
                          <p className="text-xs">ابدأ التعلم لكسب إنجازاتك الأولى!</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* النشاط الأسبوعي - zaki alkholy */}
                  {displayData.streak?.weekly_activity && (
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h3 className="text-lg font-semibold mb-4 flex items-center">
                        <Calendar className="w-5 h-5 mr-2 text-green-500" />
                        النشاط الأسبوعي
                      </h3>
                      <div className="grid grid-cols-7 gap-2">
                        {displayData.streak.weekly_activity.map((day, index) => (
                          <div key={index} className="text-center">
                            <div className="text-xs text-gray-500 mb-1">
                              {new Date(day.date).toLocaleDateString('ar-EG', { weekday: 'short' })}
                            </div>
                            <div
                              className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                                day.total_activity > 0
                                  ? 'bg-green-500 text-white'
                                  : 'bg-gray-200 text-gray-500'
                              }`}
                            >
                              {day.total_activity}
                            </div>
                          </div>
                        ))}
                      </div>
                      <p className="text-xs text-gray-600 mt-3 text-center">
                        إجمالي النشاط هذا الأسبوع: {displayData.streak.weekly_activity.reduce((total, day) => total + day.total_activity, 0)} نشاط
                      </p>
                    </div>
                  )}
                </div>

                {/* مقارنة الأداء - zaki alkholy */}
                {displayData.comparison && (
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
                    <h3 className="text-lg font-semibold mb-4 flex items-center">
                      <TrendingUp className="w-5 h-5 mr-2 text-blue-500" />
                      مقارنة أدائك
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-blue-600">
                          #{displayData.comparison.rank || 'N/A'}
                        </p>
                        <p className="text-sm text-gray-600">ترتيبك</p>
                        <p className="text-xs text-gray-500">
                          من أصل {displayData.comparison.total_students || 0} طالب
                        </p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-green-600">
                          {Math.round(displayData.comparison.percentile || 0)}%
                        </p>
                        <p className="text-sm text-gray-600">أفضل من</p>
                        <p className="text-xs text-gray-500">الطلاب الآخرين</p>
                      </div>
                      <div className="text-center">
                        <p className={`text-2xl font-bold ${
                          (displayData.comparison.points_vs_average?.improvement_percentage || 0) >= 0
                            ? 'text-green-600'
                            : 'text-red-600'
                        }`}>
                          {displayData.comparison.points_vs_average?.improvement_percentage >= 0 ? '+' : ''}
                          {Math.round(displayData.comparison.points_vs_average?.improvement_percentage || 0)}%
                        </p>
                        <p className="text-sm text-gray-600">مقارنة بالمتوسط</p>
                        <p className="text-xs text-gray-500">
                          {displayData.comparison.points_vs_average?.student || 0} نقطة
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* تبويب الدورات - zaki alkholy */}
            {activeTab === 'courses' && (
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold">الدورات الحالية</h3>
                  <span className="text-sm text-gray-500">
                    {displayData.current_courses.length} دورة مسجل بها
                  </span>
                </div>

                {displayData.current_courses.length > 0 ? (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {displayData.current_courses.map((course) => (
                      <motion.div
                        key={course.id || course.course_id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="bg-white rounded-lg p-6 border border-gray-200 hover:shadow-lg transition-all duration-300"
                      >
                        <div className="flex justify-between items-start mb-4">
                          <div className="flex-1">
                            <h4 className="font-semibold text-lg mb-1">
                              {course.title || course.course_title}
                            </h4>
                            <p className="text-sm text-gray-600 mb-2">
                              المعلم: {course.instructor || course.instructor_name || 'غير محدد'}
                            </p>
                            <div className="flex items-center space-x-4 text-xs text-gray-500">
                              <span>{course.completed_lessons || 0} / {course.total_lessons || 0} درس</span>
                              {course.last_accessed && (
                                <span>آخر وصول: {new Date(course.last_accessed).toLocaleDateString('ar-EG')}</span>
                              )}
                            </div>
                          </div>
                          <div className="text-right">
                            <span className="text-2xl font-bold text-blue-600">
                              {Math.round(course.progress_percentage || course.overall_progress || 0)}%
                            </span>
                            <p className="text-xs text-gray-500">مكتمل</p>
                          </div>
                        </div>

                        <ProgressBar
                          percentage={course.progress_percentage || course.overall_progress || 0}
                          label="التقدم في الدورة"
                          color="blue"
                        />

                        <div className="mt-4 flex justify-between items-center">
                          <div className="flex space-x-2">
                            {(course.progress_percentage || course.overall_progress || 0) === 100 ? (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <CheckCircle className="w-3 h-3 mr-1" />
                                مكتملة
                              </span>
                            ) : (course.progress_percentage || course.overall_progress || 0) > 0 ? (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <Clock className="w-3 h-3 mr-1" />
                                جاري التعلم
                              </span>
                            ) : (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                لم تبدأ
                              </span>
                            )}
                          </div>
                          <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            متابعة التعلم ←
                          </button>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-gray-500 py-12">
                    <BookOpen className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                    <p className="text-lg mb-2">لا توجد دورات مسجل بها حالياً</p>
                    <p className="text-sm mb-4">ابدأ رحلة التعلم بالتسجيل في دورة جديدة!</p>
                    <button className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors">
                      استكشف الدورات
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* تبويب الإنجازات - zaki alkholy */}
            {activeTab === 'achievements' && (
              <div className="space-y-8">
                {/* الإنجازات المحققة - zaki alkholy */}
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <Trophy className="w-5 h-5 mr-2 text-yellow-500" />
                    الإنجازات المحققة ({displayData.recent_achievements.length})
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {displayData.recent_achievements.length > 0 ? (
                      displayData.recent_achievements.map((achievement) => (
                        <AchievementCard
                          key={achievement.id}
                          achievement={achievement.achievement || achievement}
                          isEarned={true}
                        />
                      ))
                    ) : (
                      <div className="col-span-full text-center text-gray-500 py-8">
                        <Trophy className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                        <p className="text-lg">لا توجد إنجازات محققة بعد</p>
                        <p className="text-sm">ابدأ التعلم واكسب إنجازاتك الأولى!</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* الإنجازات المتاحة - zaki alkholy */}
                {achievementsData?.available_achievements && achievementsData.available_achievements.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold mb-4 flex items-center">
                      <Target className="w-5 h-5 mr-2 text-gray-500" />
                      إنجازات يمكن تحقيقها ({achievementsData.available_achievements.length})
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {achievementsData.available_achievements.map((achievement) => (
                        <AchievementCard
                          key={achievement.id}
                          achievement={achievement}
                          isEarned={false}
                        />
                      ))}
                    </div>
                  </div>
                )}

                {/* إحصائيات الإنجازات - zaki alkholy */}
                {achievementsData?.stats && (
                  <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-6 border border-yellow-200">
                    <h4 className="font-semibold mb-4 flex items-center">
                      <Award className="w-5 h-5 mr-2 text-yellow-600" />
                      إحصائيات الإنجازات
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-yellow-600">
                          {achievementsData.stats.total_earned || 0}
                        </p>
                        <p className="text-sm text-gray-600">إنجاز محقق</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-orange-600">
                          {achievementsData.stats.total_points_earned || 0}
                        </p>
                        <p className="text-sm text-gray-600">نقطة من الإنجازات</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-blue-600">
                          {achievementsData.stats.available_count || 0}
                        </p>
                        <p className="text-sm text-gray-600">إنجاز متاح</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* تبويب الأهداف - zaki alkholy */}
            {activeTab === 'goals' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold mb-4">أهداف التعلم</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* الهدف اليومي - zaki alkholy */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold mb-3 flex items-center">
                      <Calendar className="w-4 h-4 mr-2 text-green-500" />
                      الهدف اليومي
                    </h4>
                    <ProgressBar
                      percentage={displayData.streak?.learning_goals?.daily_lessons?.percentage || 0}
                      label={`${displayData.streak?.learning_goals?.daily_lessons?.current || 0}/${displayData.streak?.learning_goals?.daily_lessons?.goal || 2} درس يومياً`}
                      color="green"
                    />
                    <p className="text-xs text-gray-600 mt-2">
                      {(displayData.streak?.learning_goals?.daily_lessons?.current || 0) >= (displayData.streak?.learning_goals?.daily_lessons?.goal || 2) ? '🎉 تم تحقيق الهدف اليومي!' : 'استمر في التعلم لتحقيق هدفك اليومي'}
                    </p>
                  </div>

                  {/* الهدف الأسبوعي - zaki alkholy */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold mb-3 flex items-center">
                      <Target className="w-4 h-4 mr-2 text-purple-500" />
                      الهدف الأسبوعي
                    </h4>
                    <ProgressBar
                      percentage={displayData.streak?.learning_goals?.weekly_quizzes?.percentage || 0}
                      label={`${displayData.streak?.learning_goals?.weekly_quizzes?.current || 0}/${displayData.streak?.learning_goals?.weekly_quizzes?.goal || 5} اختبار أسبوعياً`}
                      color="purple"
                    />
                    <p className="text-xs text-gray-600 mt-2">
                      {(displayData.streak?.learning_goals?.weekly_quizzes?.current || 0) >= (displayData.streak?.learning_goals?.weekly_quizzes?.goal || 5) ? '🏆 ممتاز! تم تحقيق الهدف الأسبوعي' : 'اجتز المزيد من الاختبارات'}
                    </p>
                  </div>

                  {/* الهدف الشهري - zaki alkholy */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold mb-3 flex items-center">
                      <BookOpen className="w-4 h-4 mr-2 text-blue-500" />
                      الهدف الشهري
                    </h4>
                    <ProgressBar
                      percentage={displayData.streak?.learning_goals?.monthly_courses?.percentage || 0}
                      label={`${displayData.streak?.learning_goals?.monthly_courses?.current || 0}/${displayData.streak?.learning_goals?.monthly_courses?.goal || 1} دورة شهرياً`}
                      color="blue"
                    />
                    <p className="text-xs text-gray-600 mt-2">
                      {(displayData.streak?.learning_goals?.monthly_courses?.current || 0) >= (displayData.streak?.learning_goals?.monthly_courses?.goal || 1) ? '🌟 رائع! تم إكمال دورة هذا الشهر' : 'أكمل دورة واحدة على الأقل هذا الشهر'}
                    </p>
                  </div>
                </div>

                {/* إحصائيات سلسلة التعلم - zaki alkholy */}
                {displayData.streak && (
                  <div className="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg p-6 border border-orange-200">
                    <h4 className="font-semibold mb-4 flex items-center">
                      <Zap className="w-5 h-5 mr-2 text-orange-500" />
                      إحصائيات سلسلة التعلم
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-orange-600">
                          {displayData.streak?.streak_info?.current_streak || 0}
                        </p>
                        <p className="text-sm text-gray-600">أيام متتالية</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-yellow-600">
                          {displayData.streak?.streak_info?.longest_streak || 0}
                        </p>
                        <p className="text-sm text-gray-600">أطول سلسلة</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-green-600">
                          {displayData.streak?.weekly_activity?.reduce((total, day) => total + day.total_activity, 0) || 0}
                        </p>
                        <p className="text-sm text-gray-600">نشاط هذا الأسبوع</p>
                      </div>
                    </div>

                    {/* رسالة تحفيزية - zaki alkholy */}
                    {displayData.streak?.motivational_message && (
                      <div className="mt-4 p-3 bg-white rounded-lg border border-orange-300">
                        <p className="text-center text-orange-700 font-medium">
                          {displayData.streak.motivational_message}
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
