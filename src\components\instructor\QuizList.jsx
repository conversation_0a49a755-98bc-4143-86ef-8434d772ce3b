// import React from "react";

// const QuizList = ({
//   quizzes = [],
//   lessonId,
//   handleDeleteQuiz,
//   setQuizEditForm,
//   quizEditForm,
//   handleEditQuiz,
//   setQuizForm,
//   setShowQuizModal,
//   generateQuizPDF,
//   user,
//   showQuestionForm,
//   setShowQuestionForm,
//   QuestionFormModal,
//   questionForm,
//   setQuestionForm,
//   answersForm,
//   setAnswersForm,
//   questionLoading,
//   handleAddQuestion,
//   handleDeleteQuestion,
//   questionEditForm,
//   setQuestionEditForm,
//   handleEditQuestion,
// }) => {
//   return (
//     <div className="mt-4">
//       <h4 className="font-bold text-gray-700 mb-2">الامتحانات والواجبات</h4>
//       {quizzes && quizzes.length > 0 ? (
//         <ul className="space-y-2">
//           {quizzes.map((quiz) => (
//             <li
//               key={quiz.id}
//               className="bg-white border border-black p-4 border-3 rounded flex flex-col md:flex-row md:items-center md:justify-between"
//             >
//               <div className="w-full">
//                 {quiz.is_published ? (
//                   <button
//                     className="bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600"
//                     onClick={quiz.onUnpublish}
//                   >
//                     إلغاء النشر
//                   </button>
//                 ) : (
//                   <button
//                     className="bg-green-600 text-white h-[50px] px-3 py-1 rounded hover:bg-green-700"
//                     onClick={quiz.onPublish}
//                   >
//                     نشر
//                   </button>
//                 )}
//                 <button
//                   onClick={() => generateQuizPDF(quiz, user)}
//                   className="bg-green-600 mr-4 ml-4 h-[50px] text-white px-4 py-2 rounded hover:bg-green-700"
//                 >
//                   تحميل الامتحان PDF
//                 </button>
//                 <span className="font-semibold text-lg text-blue-700">
//                   {quiz.quiz_type === "exam" ? "امتحان" : "واجب"}:
//                 </span>{" "}
//                 {quiz.title}
//                 <div className="text-sm text-gray-600">{quiz.description}</div>
//                 <div className="text-xs text-gray-500">
//                   درجة النجاح: {quiz.passing_score} | الوقت: {quiz.time_limit} دقيقة
//                 </div>
//                 {quiz.questions && (
//                   <div className="text-xs text-blue-700 mt-1">
//                     مجموع الدرجات: <b>{quiz.questions.reduce((sum, q) => sum + Number(q.points || 0), 0)}</b> من <b>{quiz.passing_score}</b>
//                     {" — "}
//                     {(() => {
//                       const total = quiz.questions.reduce((sum, q) => sum + Number(q.points || 0), 0);
//                       const diff = total - quiz.passing_score;
//                       if (diff === 0) return "✅ متساوي";
//                       if (diff > 0) return `🔺 زيادة ${diff}`;
//                       return `🔻 ناقص ${Math.abs(diff)}`;
//                     })()}
//                   </div>
//                 )}
//                 <div className="mt-2 border-t pt-2">
//                   <div className="flex items-center justify-between mb-1">
//                     <span className="font-bold text-gray-700">الأسئلة</span>
//                     <button
//                       className="bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600"
//                       onClick={() => {
//                         setShowQuestionForm((prev) => ({ ...prev, [quiz.id]: !prev[quiz.id] }));
//                         if (!showQuestionForm[quiz.id]) {
//                           setAnswersForm((prev) => ({
//                             ...prev,
//                             [quiz.id]: [
//                               { text: "", is_correct: false },
//                               { text: "", is_correct: false },
//                             ],
//                           }));
//                           setQuestionForm((prev) => ({
//                             ...prev,
//                             [quiz.id]: {
//                               ...prev[quiz.id],
//                               question_type: "mcq",
//                             },
//                           }));
//                         }
//                       }}
//                     >
//                       {showQuestionForm[quiz.id] ? "إغلاق" : "إضافة سؤال"}
//                     </button>
//                   </div>
//                   <QuestionFormModal
//                     isOpen={!!showQuestionForm[quiz.id]}
//                     onClose={() => setShowQuestionForm((prev) => ({ ...prev, [quiz.id]: false }))}
//                     onSubmit={(quizId, { answers, image }) => handleAddQuestion(quizId, answers, image)}
//                     quizId={quiz.id}
//                     formState={questionForm[quiz.id] || {}}
//                     setFormState={updater =>
//                       setQuestionForm(prev => ({
//                         ...prev,
//                         [quiz.id]: typeof updater === "function"
//                           ? updater(prev[quiz.id] || {})
//                           : updater
//                       }))
//                     }
//                     answers={answersForm[quiz.id] || []}
//                     setAnswers={data => setAnswersForm(prev => ({ ...prev, [quiz.id]: data }))}
//                     loading={questionLoading[quiz.id]}
//                     quiz={quiz}
//                     existingOrders={quiz.questions?.map(q => q.order)}
//                   />
//                   {quiz.questions && quiz.questions.length > 0 ? (
//                     <ul className="space-y-1">
//                       {quiz.questions.map((question, index) => (
//                         <li
//                           key={question.id}
//                           className="bg-gray-50 border border-black p-4 border-3 rounded flex flex-col md:flex-row md:items-center md:justify-between"
//                         >
//                           <div>
//                             <span className="font-semibold text-indigo-700">س{index + 1}:</span> {question.text}
//                             <span className="text-xs text-gray-500 ml-2">
//                               {question.question_type === "mcq" || question.question_type === "multiple_choice"
//                                 ? "اختيار من متعدد"
//                                 : question.question_type === "true_false"
//                                 ? "صح أو خطأ"
//                                 : "غير محدد"}
//                             </span>
//                             <span className="text-xs text-gray-400 ml-2">درجة: {question.points}</span>
//                             {question.image_url && (
//                               <span className="text-xs text-blue-500 ml-2">📷 يحتوي على صورة</span>
//                             )}
//                             {question.image_url && (
//                               <div className="mt-2">
//                                 <img
//                                   src={question.image_url}
//                                   alt="صورة السؤال"
//                                   className="max-w-full h-24 object-contain border rounded"
//                                 />
//                               </div>
//                             )}
//                             {(question.question_type === "mcq" || question.question_type === "multiple_choice" || question.question_type === "true_false") &&
//                               question.answers &&
//                               question.answers.length > 0 && (
//                                 <ul className="mt-2 ml-4 list-disc text-sm">
//                                   {question.answers.map((ans) => (
//                                     <li key={ans.id} className={ans.is_correct ? "text-green-700 font-bold" : ""}>
//                                       {ans.text} {ans.is_correct && <span className="ml-1 text-green-600">✔</span>}
//                                     </li>
//                                   ))}
//                                 </ul>
//                               )}
//                           </div>
//                           <div className="flex gap-2 mt-2 md:mt-0">
//                             <button
//                               className="bg-yellow-400 text-white px-2 py-1 rounded text-xs hover:bg-yellow-500"
//                               onClick={() => {
//                                 setQuestionEditForm({
//                                   quizId: quiz.id,
//                                   question: question,
//                                   answers: question.answers || [],
//                                 });
//                               }}
//                             >
//                               تعديل
//                             </button>
//                             <button
//                               className="bg-red-500 text-white px-2 py-1 rounded text-xs hover:bg-red-600"
//                               onClick={() => handleDeleteQuestion(quiz.id, question.id)}
//                               disabled={questionLoading[quiz.id]}
//                             >
//                               حذف
//                             </button>
//                           </div>
//                         </li>
//                       ))}
//                     </ul>
//                   ) : (
//                     <div className="text-gray-400 text-xs">لا يوجد أسئلة بعد</div>
//                   )}
//                   <QuestionFormModal
//                     isOpen={!!(questionEditForm && questionEditForm.quizId === quiz.id)}
//                     onClose={() => {
//                       setQuestionEditForm(null);
//                       setAnswersForm({});
//                     }}
//                     onSubmit={(quizId, { answers, image }) => handleEditQuestion(quizId, questionEditForm.question.id, answers, image)}
//                     quizId={quiz.id}
//                     formState={questionEditForm?.question || {}}
//                     setFormState={updater =>
//                       setQuestionEditForm(prev => ({
//                         ...prev,
//                         question: typeof updater === "function"
//                           ? updater(prev?.question || {})
//                           : updater
//                       }))
//                     }
//                     answers={questionEditForm?.answers || []}
//                     setAnswers={data => setQuestionEditForm(prev => ({ ...prev, answers: data }))}
//                     loading={questionLoading[quiz.id]}
//                     quiz={quiz}
//                     existingOrders={quiz.questions?.map(q => q.order)}
//                   />
//                 </div>
//               </div>
//               <div className="flex gap-2 mt-2 md:mt-0">
//                 <button
//                   className="bg-yellow-400 text-white px-3 py-1 rounded hover:bg-yellow-500"
//                   onClick={() =>
//                     setQuizEditForm({
//                       lessonId: lessonId,
//                       quiz: {
//                         id: quiz.id,
//                         title: quiz.title || "",
//                         description: quiz.description || "",
//                         passing_score: quiz.passing_score || "",
//                         time_limit: quiz.time_limit || "",
//                         quiz_type: quiz.quiz_type || "",
//                       },
//                     })
//                   }
//                 >
//                   تعديل
//                 </button>
//                 <button
//                   className="bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600"
//                   onClick={() => handleDeleteQuiz(lessonId, quiz.id)}
//                 >
//                   حذف
//                 </button>
//               </div>
//             </li>
//           ))}
//         </ul>
//       ) : (
//         <div className="text-gray-400 text-sm">لا يوجد امتحانات أو واجبات لهذا الدرس</div>
//       )}
//     </div>
//   );
// };

// export default QuizList;
