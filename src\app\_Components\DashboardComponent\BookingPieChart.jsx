"use client";
import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import DonutSmallIcon from "@mui/icons-material/DonutSmall";

export default function BookingPieChart({
  data = [],
  title = "نسبة الحجوزات",
}) {
  const COLORS = ["#10B981", "#EF4444", "#F59E0B", "#3B82F6", "#8B5CF6"];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl dark:shadow-gray-900/20 p-6 transition-all duration-300 border border-gray-100 dark:border-gray-700 animate-slide-up">
      <header className="flex items-center gap-3 border-b border-gray-200 dark:border-gray-700 pb-4 mb-6 text-gray-700 dark:text-gray-300 font-bold text-lg">
        <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
          <DonutSmallIcon
            fontSize="medium"
            className="text-purple-600 dark:text-purple-400"
          />
        </div>
        <div className="flex flex-1 justify-between items-center">
          <h3>{title}</h3>
          <span className="text-xs bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 text-blue-700 dark:text-blue-300 px-3 py-1 rounded-full font-medium border border-blue-200 dark:border-blue-700">
            قريباً
          </span>
        </div>
      </header>
      <div className="relative grayscale blur-sm">
        <ResponsiveContainer width="100%" height={220}>
          <PieChart>
            <defs>
              <linearGradient id="greenGradient" x1="0" y1="0" x2="1" y2="1">
                <stop offset="0%" stopColor="#10B981" />
                <stop offset="100%" stopColor="#059669" />
              </linearGradient>
              <linearGradient id="redGradient" x1="0" y1="0" x2="1" y2="1">
                <stop offset="0%" stopColor="#EF4444" />
                <stop offset="100%" stopColor="#DC2626" />
              </linearGradient>
              <linearGradient id="yellowGradient" x1="0" y1="0" x2="1" y2="1">
                <stop offset="0%" stopColor="#F59E0B" />
                <stop offset="100%" stopColor="#D97706" />
              </linearGradient>
            </defs>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              outerRadius={80}
              innerRadius={40}
              fill="#8884d8"
              dataKey="value"
              label={({ name, percent }) =>
                `${name} ${(percent * 100).toFixed(0)}%`
              }
              labelLine={false}
              animationBegin={0}
              animationDuration={1000}
            >
              {data.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={
                    index === 0
                      ? "url(#greenGradient)"
                      : index === 1
                      ? "url(#redGradient)"
                      : "url(#yellowGradient)"
                  }
                  stroke="#ffffff"
                  strokeWidth={2}
                />
              ))}
            </Pie>
            <Tooltip
              contentStyle={{
                backgroundColor: "rgba(255, 255, 255, 0.95)",
                border: "none",
                borderRadius: "12px",
                boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
                fontSize: "14px",
              }}
            />
            <Legend
              wrapperStyle={{
                fontSize: "12px",
                fontWeight: "bold",
              }}
            />
          </PieChart>
        </ResponsiveContainer>

        {/* Center text */}
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-800 dark:text-gray-200">
              {data.reduce((sum, item) => sum + item.value, 0)}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 font-medium">
              إجمالي الحجوزات
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
