"use client";
import React, { useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import Link from "next/link";
import {
  LayoutDashboard,
  DollarSign,
  Users,
  FileText,
  CheckCircle,
  LogOut,
  Shield,
  Menu,
  X,
  Eye
} from "lucide-react";

export default function AdminDashboardLayout({ children }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [adminUser, setAdminUser] = useState(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  // التحقق من المصادقة
  useEffect(() => {
    const checkAuth = () => {
      const adminToken = localStorage.getItem("admin_token");
      const adminExpiry = localStorage.getItem("admin_token_expiry");
      const adminUserData = localStorage.getItem("admin_user");

      if (adminToken && adminExpiry && adminUserData) {
        const now = new Date().getTime();
        if (now < parseInt(adminExpiry)) {
          setIsAuthenticated(true);
          setAdminUser(JSON.parse(adminUserData));
        } else {
          // Token منتهي الصلاحية
          localStorage.removeItem("admin_token");
          localStorage.removeItem("admin_token_expiry");
          localStorage.removeItem("admin_user");
          router.push("/admin-secure-dashboard-2024/login");
        }
      } else if (pathname !== "/admin-secure-dashboard-2024/login") {
        router.push("/admin-secure-dashboard-2024/login");
      }
      setLoading(false);
    };

    checkAuth();
  }, [router, pathname]);

  const handleLogout = () => {
    localStorage.removeItem("admin_token");
    localStorage.removeItem("admin_token_expiry");
    localStorage.removeItem("admin_user");
    router.push("/admin-secure-dashboard-2024/login");
  };

  // Navigation items
  const navItems = [
    {
      name: "لوحة التحكم",
      href: "/admin-secure-dashboard-2024",
      icon: LayoutDashboard,
    },
    {
      name: "المبيعات المعلقة",
      href: "/admin-secure-dashboard-2024/pending-payouts",
      icon: DollarSign,
    },

    {
      name: "التحويلات المكتملة",
      href: "/admin-secure-dashboard-2024/completed-payouts",
      icon: CheckCircle,
    },
    {
      name: "جميع الطلبات",
      href: "/admin-secure-dashboard-2024/all-orders",
      icon: FileText,
    },
    {
      name: "المدفوعات",
      href: "/admin-secure-dashboard-2024/payments",
      icon: DollarSign,
    },
    {
      name: "سجلات المراجعة",
      href: "/admin-secure-dashboard-2024/audit-logs",
      icon: Eye,
    },
    {
      name: "المعلمين",
      href: "/admin-secure-dashboard-2024/instructors",
      icon: Users,
    },
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="flex items-center space-x-2 space-x-reverse">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="text-white">جاري التحميل...</span>
        </div>
      </div>
    );
  }

  // صفحة تسجيل الدخول
  if (pathname === "/admin-secure-dashboard-2024/login") {
    return children;
  }

  // التحقق من المصادقة
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 flex">
      {/* Sidebar */}
      <div className={`fixed inset-y-0 right-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform ${
        sidebarOpen ? 'translate-x-0' : 'translate-x-full'
      } transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 lg:flex lg:flex-col`}>
        
        {/* Sidebar Header */}
        <div className="flex items-center justify-between h-16 px-6 bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="flex items-center">
            <Shield className="w-8 h-8 text-white ml-2" />
            <span className="text-white font-bold text-lg">Admin Panel</span>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden text-white hover:text-gray-200"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Admin Info */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white font-semibold">
                {adminUser?.username?.charAt(0).toUpperCase()}
              </span>
            </div>
            <div className="mr-3">
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                {adminUser?.username}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                مدير النظام
              </p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 mt-4 px-4">
          <ul className="space-y-2">
            {navItems.map((item) => {
              const isActive = pathname === item.href;
              return (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className={`flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                      isActive
                        ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
                        : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
                    }`}
                    onClick={() => setSidebarOpen(false)}
                  >
                    <item.icon className="w-5 h-5 ml-3" />
                    {item.name}
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Logout Button */}
        <div className="absolute bottom-4 left-4 right-4">
          <button
            onClick={handleLogout}
            className="w-full flex items-center px-4 py-3 text-sm font-medium text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20 rounded-lg transition-colors"
          >
            <LogOut className="w-5 h-5 ml-3" />
            تسجيل الخروج
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 lg:mr-0 flex flex-col">
        {/* Top Bar */}
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between h-16 px-6">
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <Menu className="w-6 h-6" />
            </button>

            <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
              لوحة تحكم المدير
            </h1>

            <div className="flex items-center space-x-4 space-x-reverse">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {new Date().toLocaleDateString('ar-EG')}
              </span>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 p-6 overflow-auto">
          {children}
        </main>
      </div>

      {/* Sidebar Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        ></div>
      )}
    </div>
  );
}
