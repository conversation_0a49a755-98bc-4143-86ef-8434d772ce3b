// صفحة اختبار الاتصال بالباك اند - zaki alkholy
'use client';

import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_BASE_URL } from '@/config/api';

export default function TestConnectionPage() {
  const [connectionStatus, setConnectionStatus] = useState('جاري الاختبار...');
  const [backendData, setBackendData] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    testConnection();
  }, []);

  const testConnection = async () => {
    try {
      console.log('اختبار الاتصال بالباك اند - zaki alkholy:', API_BASE_URL);
      
      // اختبار اتصال بسيط
      const response = await axios.get(`${API_BASE_URL}/api/courses/`, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
        }
      });

      setConnectionStatus('✅ الاتصال ناجح!');
      setBackendData(response.data);
      console.log('نجح الاتصال - zaki alkholy:', response.data);
      
    } catch (err) {
      console.error('فشل الاتصال - zaki alkholy:', err);
      setConnectionStatus('❌ فشل الاتصال');
      setError(err.message);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">اختبار الاتصال بالباك اند</h1>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold mb-4 text-foreground">حالة الاتصال</h2>
          <p className="text-lg mb-4 text-foreground">{connectionStatus}</p>

          <div className="mb-4 text-foreground">
            <strong>رابط الباك اند:</strong> {API_BASE_URL}
          </div>

          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-4 mb-4">
              <h3 className="text-red-800 dark:text-red-400 font-semibold mb-2">خطأ:</h3>
              <p className="text-red-700 dark:text-red-300">{error}</p>
            </div>
          )}

          {backendData && (
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4">
              <h3 className="text-green-800 dark:text-green-400 font-semibold mb-2">البيانات المستلمة:</h3>
              <pre className="text-sm text-green-700 dark:text-green-300 overflow-auto max-h-96">
                {JSON.stringify(backendData, null, 2)}
              </pre>
            </div>
          )}

          <button
            onClick={testConnection}
            className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            إعادة الاختبار
          </button>
        </div>
      </div>
    </div>
  );
}
