"use client";
import React, { useState, useEffect, useRef } from "react";
import { useSelector } from "react-redux";
import { useRouter, useParams } from "next/navigation";
import Link from "next/link";
import Cookies from "js-cookie";
import {
  Star,
  Clock,
  Users,
  Play,
  BookOpen,
  ChevronLeft,
  User,
  Mail,
  Phone,
  Calendar,
  Settings,
  Award,
  Heart,
} from "lucide-react";
import {
  fetchStudentProfile,
  fetchStudentCourses,
} from "../../../../services/student";
import {
  selectCurrentUser,
  selectIsAuthenticated,
} from "../../../../store/authSlice";

// Helper functions
const formatPrice = (price, currency = "جنيه") => {
  if (!price) return "مجاني";
  return `${price} ${currency}`;
};

const formatDuration = (duration) => {
  if (!duration) return "غير محدد";
  if (typeof duration === "string") return duration;

  const hours = Math.floor(duration / 60);
  const minutes = duration % 60;
  if (hours > 0) {
    return `${hours} ساعة${minutes > 0 ? ` و ${minutes} دقيقة` : ""}`;
  }
  return `${minutes} دقيقة`;
};

const renderStars = (rating) => {
  const numRating = parseFloat(rating) || 0;
  return Array.from({ length: 5 }, (_, i) => (
    <Star
      key={i}
      className={`w-4 h-4 ${
        i < Math.floor(numRating)
          ? "text-yellow-400 fill-current"
          : i < numRating
          ? "text-yellow-400 fill-current opacity-50"
          : "text-gray-300 dark:text-gray-600"
      }`}
    />
  ));
};

const formatDate = (dateString) => {
  if (!dateString) return "غير محدد";
  const date = new Date(dateString);
  return date.toLocaleDateString("ar-EG", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

export default function StudentProfile() {
  const theUrl = "https://res.cloudinary.com/djwhgnwp5/";
  const { id } = useParams();
  const router = useRouter();
  const user = useSelector(selectCurrentUser);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const [studentProfile, setStudentProfile] = useState(null);
  const [studentCourses, setStudentCourses] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [errorMsg, setErrorMsg] = useState(null);
  const [visibleCards, setVisibleCards] = useState([]);
  const sectionRef = useRef(null);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/login");
      return;
    }
    if (user && user.id && user.id.toString() !== id) {
      // إذا كان المستخدم طالب لكن يحاول دخول صفحة طالب آخر
      if (user.is_student) {
        router.push(`/student/${user.id}`);
        return;
      } else if (user.is_instructor) {
        router.push(`/instructor/${user.id}`);
        return;
      }
    }

    const loadStudentData = async () => {
      try {
        setIsLoading(true);
        setErrorMsg(null);
        const token = Cookies.get("authToken");
        if (!token) {
          setErrorMsg("يرجى تسجيل الدخول لعرض الملف الشخصي");
          setIsLoading(false);
          return;
        }
        const profile = await fetchStudentProfile(id, token);
        console.log("Student Profile Data:", profile);
        if (!profile) {
          setErrorMsg("لم يتم العثور على الطالب");
          setIsLoading(false);
          return;
        }
        setStudentProfile(profile);
        const courses = await fetchStudentCourses(id, token);
        console.log("Student Courses Data:", courses);
        setStudentCourses(courses);
      } catch (err) {
        if (err.response?.status === 401) {
          setErrorMsg("انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى");
        } else {
          setErrorMsg("حدث خطأ أثناء جلب بيانات الطالب");
        }
      } finally {
        setIsLoading(false);
      }
    };
    loadStudentData();
  }, [id, isAuthenticated, user, router]);

  // Animation effect for course cards
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.dataset.index);
            setVisibleCards((prev) => [...new Set([...prev, index])]);
          }
        });
      },
      { threshold: 0.1 }
    );

    const cardElements = sectionRef.current?.querySelectorAll("[data-index]");
    cardElements?.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, [studentCourses]);

  if (isLoading) {
    return (
      <div className="min-h-screen pt-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto mb-6"></div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
              جاري تحميل الملف الشخصي...
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              يرجى الانتظار قليلاً
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (errorMsg) {
    return (
      <div className="min-h-screen pt-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-2xl p-8 text-center max-w-md mx-auto">
            <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <User className="w-8 h-8 text-red-600 dark:text-red-400" />
            </div>
            <h3 className="text-xl font-bold text-red-800 dark:text-red-300 mb-2">
              خطأ في تحميل البيانات
            </h3>
            <p className="text-red-600 dark:text-red-400 mb-6">{errorMsg}</p>
            <Link
              href="/login"
              className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-xl font-medium transition-colors duration-300"
            >
              <User className="w-5 h-5 ml-2" />
              تسجيل الدخول
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!studentProfile && user) {
    // عرض بيانات المستخدم من store إذا لم يتم جلبها من API
    return (
      <div className="min-h-screen pt-16 bg-gray-50 dark:bg-gray-900 flex flex-col items-center justify-center">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 w-full max-w-lg text-center">
          <div className="flex flex-col items-center mb-6">
            <div className="relative mb-4">
              <img
                src={user.profile_image || "/images/default-course.jpg"}
                alt="الصورة الشخصية"
                className="w-32 h-32 rounded-full object-cover border-4 border-blue-200 dark:border-blue-800"
              />
              <div className="absolute -bottom-2 -right-2 w-10 h-10 bg-green-500 rounded-full border-4 border-white dark:border-gray-800 flex items-center justify-center">
                <User className="w-5 h-5 text-white" />
              </div>
            </div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              مرحبًا بك يا {user.first_name || user.username}
            </h1>
            <div className="space-y-2 text-gray-600 dark:text-gray-400">
              <div className="flex items-center justify-center">
                <Mail className="w-4 h-4 ml-2" />
                <span>{user.email}</span>
              </div>
              {user.phone_number && (
                <div className="flex items-center justify-center">
                  <Phone className="w-4 h-4 ml-2" />
                  <span>{user.phone_number}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!studentProfile) {
    return (
      <div className="min-h-screen pt-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-2xl p-8 text-center max-w-md mx-auto">
            <div className="w-16 h-16 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <User className="w-8 h-8 text-yellow-600 dark:text-yellow-400" />
            </div>
            <h3 className="text-xl font-bold text-yellow-800 dark:text-yellow-300 mb-2">
              لم يتم العثور على الطالب
            </h3>
            <p className="text-yellow-600 dark:text-yellow-400">
              الملف الشخصي غير متاح حالياً
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen pt-16 bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/20 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 transition-all duration-500"
      ref={sectionRef}
      style={{ fontFamily: "Tajawal, Cairo, system-ui, sans-serif" }}
    >
      <div className="container mx-auto px-4 py-12">
        {/* Hero Section - معلومات الطالب */}
        <div className="bg-gradient-to-br from-blue-600 via-blue-700 to-purple-800 dark:from-blue-800 dark:via-purple-800 dark:to-indigo-900 rounded-3xl shadow-2xl hover:shadow-3xl p-10 mb-16 text-white relative overflow-hidden group transition-all duration-700 animate-fade-in">
          {/* Enhanced Background Pattern */}
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-0 left-0 w-48 h-48 bg-gradient-to-br from-white/30 to-transparent rounded-full -translate-x-24 -translate-y-24 animate-pulse"></div>
            <div className="absolute bottom-0 right-0 w-40 h-40 bg-gradient-to-tl from-white/20 to-transparent rounded-full translate-x-20 translate-y-20 animate-pulse delay-1000"></div>
            <div className="absolute top-1/2 left-1/2 w-32 h-32 bg-gradient-to-r from-purple-300/10 to-blue-300/10 rounded-full -translate-x-16 -translate-y-16 animate-bounce"></div>
          </div>

          {/* Floating particles effect */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-white/40 rounded-full animate-float"></div>
            <div className="absolute top-3/4 right-1/4 w-1 h-1 bg-white/30 rounded-full animate-float-delayed"></div>
            <div className="absolute bottom-1/4 left-3/4 w-1.5 h-1.5 bg-white/20 rounded-full animate-float-slow"></div>
          </div>

          <div className="relative z-10">
            <div className="flex flex-col lg:flex-row items-center lg:items-start space-y-8 lg:space-y-0 lg:space-x-12">
              {/* Enhanced Profile Image */}
              <div className="relative group/avatar">
                <div className="w-40 h-40 rounded-full overflow-hidden border-4 border-white/30 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:scale-105 bg-gradient-to-br from-white/20 to-white/5 backdrop-blur-sm">
                  {studentProfile.profile_image ? (
                    <img
                      src={studentProfile.profile_image}
                      alt="الصورة الشخصية"
                      className="w-full h-full object-cover transition-transform duration-500 group-hover/avatar:scale-110"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-white/20 to-white/5 flex items-center justify-center">
                      <User className="w-20 h-20 text-white/80 transition-all duration-300 group-hover/avatar:scale-110" />
                    </div>
                  )}
                </div>
                <div className="absolute -bottom-3 -right-3 w-12 h-12 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full border-4 border-white shadow-lg flex items-center justify-center animate-bounce hover:animate-none transition-all duration-300">
                  <Award className="w-6 h-6 text-white" />
                </div>
                {/* Online status indicator */}
                <div className="absolute top-2 right-2 w-4 h-4 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
              </div>

              {/* Enhanced Profile Info */}
              <div className="flex-1 text-center lg:text-right space-y-6">
                <div className="space-y-3">
                  <h1 className="text-5xl lg:text-6xl font-bold mb-3 bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent animate-fade-in-up">
                    {studentProfile.first_name && studentProfile.last_name
                      ? `${studentProfile.first_name} ${studentProfile.last_name}`
                      : studentProfile.username}
                  </h1>
                  <div className="flex items-center justify-center lg:justify-start space-x-3">
                    <div className="w-2 h-2 bg-blue-300 rounded-full animate-pulse"></div>
                    <p className="text-xl text-blue-100 font-medium pr-3">
                      طالب في منصة مُعَلِّمِيّ
                    </p>
                    <div className="w-2 h-2 bg-purple-300 rounded-full animate-pulse delay-600"></div>
                  </div>
                </div>

                {/* Enhanced Stats */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <div className="bg-white/15 hover:bg-white/20 rounded-2xl p-6 backdrop-blur-md border border-white/20 hover:border-white/30 transition-all duration-300 hover:scale-105 hover:shadow-xl group/stat">
                    <div className="flex items-center justify-center lg:justify-start space-x-4 gap-4">
                      <div className="p-3 bg-blue-500/30 rounded-xl group-hover/stat:bg-blue-400/40 transition-colors duration-300">
                        <BookOpen className="w-7 h-7 text-blue-100 group-hover/stat:scale-110 transition-transform duration-300" />
                      </div>
                      <div className="text-right">
                        <div className="text-3xl font-bold text-white">
                          {studentCourses.length}
                        </div>
                        <div className="text-sm text-blue-100 font-medium">
                          دورة مسجل فيها
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white/15 hover:bg-white/20 rounded-2xl p-6 backdrop-blur-md border border-white/20 hover:border-white/30 transition-all duration-300 hover:scale-105 hover:shadow-xl group/stat">
                    <div className="flex items-center justify-center lg:justify-start space-x-4 gap-4">
                      <div className="p-3 bg-purple-500/30 rounded-xl group-hover/stat:bg-purple-400/40 transition-colors duration-300">
                        <Calendar className="w-7 h-7 text-purple-100 group-hover/stat:scale-110 transition-transform duration-300" />
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-semibold text-white">
                          تاريخ الانضمام
                        </div>
                        <div className="text-xs text-purple-100 font-medium">
                          {formatDate(studentProfile.date_joined)}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white/15 hover:bg-white/20 rounded-2xl p-6 backdrop-blur-md border border-white/20 hover:border-white/30 transition-all duration-300 hover:scale-105 hover:shadow-xl group/stat">
                    <div className="flex items-center justify-center lg:justify-start space-x-4 gap-4">
                      <div className="p-3 bg-pink-500/30 rounded-xl group-hover/stat:bg-pink-400/40 transition-colors duration-300">
                        <Heart className="w-7 h-7 text-pink-100 group-hover/stat:scale-110 transition-transform duration-300" />
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-semibold text-white ">
                          الكورسات المفضلة
                        </div>
                        <div className="text-xs text-pink-100 font-medium">
                          {
                            studentCourses.filter((course) => course.is_liked)
                              .length
                          }{" "}
                          كورس
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Enhanced Contact Info */}
                <div className="flex flex-col lg:flex-row items-center justify-center lg:justify-start space-y-3 lg:space-y-0 lg:space-x-8 mb-8 gap-4">
                  <div className="flex items-center space-x-3 bg-white/10 rounded-xl px-4 py-3 backdrop-blur-sm border gap-2 border-white/20 hover:bg-white/15 transition-all duration-300 group/contact">
                    <div className="p-2 bg-blue-500/30 rounded-lg group-hover/contact:bg-blue-400/40 transition-colors duration-300">
                      <Mail className="w-5 h-5 text-blue-100" />
                    </div>
                    <span className="text-sm font-medium text-blue-50">
                      {studentProfile.email}
                    </span>
                  </div>
                  {studentProfile.phone_number && (
                    <div className="flex items-center space-x-3 bg-white/10 rounded-xl gap-2 px-4 py-3 backdrop-blur-sm border border-white/20 hover:bg-white/15 transition-all duration-300 group/contact">
                      <div className="p-2 bg-green-500/30 rounded-lg group-hover/contact:bg-green-400/40 transition-colors duration-300">
                        <Phone className="w-5 h-5 text-green-100" />
                      </div>
                      <span className="text-sm font-medium text-green-50">
                        {studentProfile.phone_number}
                      </span>
                    </div>
                  )}
                </div>

                {/* Enhanced Action Button */}
                <Link
                  href={`/student/${id}/settings`}
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white   hover:-translate-y-1 rounded-2xl font-bold text-lg shadow-xl hover:shadow-2xl transition-all duration-300 transform  hover:border-blue-200 group/button"
                >
                  <Settings className="w-6 h-6 ml-3 group-hover/button:rotate-90 transition-transform duration-300" />
                  إعدادات الحساب
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10 rounded-2xl opacity-0 group-hover/button:opacity-100 transition-opacity duration-300"></div>
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Courses Section Header */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            الكورسات المسجل فيها
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            {studentCourses.length > 0
              ? `${studentCourses.length} كورس متاح للدراسة`
              : "لا توجد كورسات مسجل فيها حالياً"}
          </p>
        </div>

        {/* قائمة الدورات المسجل فيها */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {studentCourses.map((course, index) => {
            const isVisible = visibleCards.includes(index);

            return (
              <div
                key={course.id}
                data-index={index}
                className={`group bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden ${
                  isVisible ? "animate-slide-up opacity-100" : "opacity-0"
                }`}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {/* Course Image */}
                <div className="relative overflow-hidden">
                  <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 relative">
                    {course.thumbnail ? (
                      <img
                        src={`${theUrl}${course.thumbnail}`}
                        alt={course.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.style.display = "none";
                          e.target.nextSibling.style.display = "flex";
                        }}
                      />
                    ) : null}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Play className="w-16 h-16 text-blue-500 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300" />
                    </div>
                  </div>

                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <Link
                        href={`/student/course/${course.slug}`}
                        className="bg-white text-blue-600 px-4 py-2 rounded-full font-medium shadow-lg transform scale-90 group-hover:scale-100 transition-transform duration-300"
                      >
                        متابعة الدراسة
                      </Link>
                    </div>
                  </div>

                  {/* Liked Badge */}
                  {course.is_liked && (
                    <div className="absolute top-4 right-4 bg-red-500 text-white p-2 rounded-full">
                      <Heart className="w-4 h-4 fill-current" />
                    </div>
                  )}

                  {/* Discount Badge */}
                  {course.discount_price &&
                    course.price &&
                    course.discount_price < course.price && (
                      <div className="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                        خصم{" "}
                        {Math.round(
                          ((course.price - course.discount_price) /
                            course.price) *
                            100
                        )}
                        %
                      </div>
                    )}
                </div>

                {/* Course Content */}
                <div className="p-6">
                  {/* Title */}
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                    {course.title}
                  </h3>

                  {/* Description */}
                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">
                    {course.short_description || course.description}
                  </p>

                  {/* Instructor */}
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 rounded-full overflow-hidden ml-3 border-2 border-gray-200 dark:border-gray-700">
                      {course.instructor?.profile_image ? (
                        <img
                          src={course.instructor.profile_image}
                          alt={`${course.instructor.first_name} ${course.instructor.last_name}`}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                          <span className="text-white text-sm font-bold">
                            {course.instructor?.first_name?.charAt(0) || "م"}
                          </span>
                        </div>
                      )}
                    </div>
                    <span className="text-gray-700 dark:text-gray-300 text-sm">
                      {course.instructor?.first_name &&
                      course.instructor?.last_name
                        ? `${course.instructor.first_name} ${course.instructor.last_name}`
                        : course.instructor?.username || "معلم"}
                    </span>
                  </div>

                  {/* Rating and Stats */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <div className="flex items-center">
                        {renderStars(course.rating)}
                      </div>
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        ({course.rating || "0.0"})
                      </span>
                    </div>
                    <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                      <Users className="w-4 h-4 ml-1" />
                      {course.students_count || 0}
                    </div>
                  </div>

                  {/* Level */}
                  <div className="flex items-center justify-between mb-4 text-sm text-gray-500 dark:text-gray-400">
                    <span className="bg-gray-100 dark:bg-gray-700 px-3 py-1 rounded-full text-xs font-medium">
                      {course.level === "beginner"
                        ? "مبتدئ"
                        : course.level === "intermediate"
                        ? "متوسط"
                        : course.level === "advanced"
                        ? "متقدم"
                        : course.level || "غير محدد"}
                    </span>
                    <span className="text-xs text-gray-400">
                      {course.language === "Arabic"
                        ? "العربية"
                        : course.language}
                    </span>
                  </div>

                  {/* Price and CTA */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {course.discount_price
                          ? formatPrice(course.discount_price, course.currency)
                          : formatPrice(course.price, course.currency)}
                      </span>
                      {course.discount_price &&
                        course.price &&
                        course.discount_price < course.price && (
                          <span className="text-sm text-gray-500 dark:text-gray-400 line-through">
                            {formatPrice(course.price, course.currency)}
                          </span>
                        )}
                    </div>
                    <Link
                      href={`/student/course/${course.slug}`}
                      className="group/btn inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-medium hover:shadow-lg transform hover:scale-105 transition-all duration-300"
                    >
                      <span>متابعة</span>
                      <ChevronLeft className="w-4 h-4 mr-2 transform group-hover/btn:-translate-x-1 transition-transform duration-300" />
                    </Link>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Empty State */}
        {studentCourses.length === 0 && (
          <div className="flex justify-center items-center py-20">
            <div className="text-center">
              <div className="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-6">
                <BookOpen className="w-12 h-12 text-gray-400 dark:text-gray-500" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                لا توجد كورسات مسجل فيها
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md">
                ابدأ رحلتك التعليمية الآن واستكشف مجموعة واسعة من الكورسات
                المتاحة
              </p>
              <Link
                href="/courses"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-medium hover:shadow-lg transform hover:scale-105 transition-all duration-300"
              >
                <BookOpen className="w-5 h-5 ml-2" />
                استكشف الكورسات
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
