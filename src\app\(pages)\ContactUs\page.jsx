"use client";
import React, { useState } from "react";
import { Mail, Phone, MapPin, Clock, Send, MessageCircle } from "lucide-react";

export default function ContactUs() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: ""
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // هنا يمكن إضافة منطق إرسال الرسالة
    console.log("Form submitted:", formData);
    alert("تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.");
    setFormData({ name: "", email: "", subject: "", message: "" });
  };

  return (
    <div className="min-h-screen pt-16 bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold text-center text-foreground mb-8">
            تواصل معنا
          </h1>
          <p className="text-xl text-secondary text-center mb-12 max-w-3xl mx-auto">
            نحن هنا لمساعدتك! لا تتردد في التواصل معنا لأي استفسار أو مساعدة تحتاجها
          </p>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* معلومات الاتصال */}
            <div className="space-y-8">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <h2 className="text-2xl font-bold text-foreground mb-6">
                  معلومات الاتصال
                </h2>
                
                <div className="space-y-6">
                  {/* البريد الإلكتروني */}
                  <div className="flex items-start space-x-4 space-x-reverse">
                    <div className="flex items-center justify-center w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg">
                      <Mail className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-foreground mb-1">
                        البريد الإلكتروني
                      </h3>
                      <p className="text-secondary">
                        <EMAIL>
                      </p>
                      <p className="text-sm text-secondary mt-1">
                        نرد على الرسائل خلال 24 ساعة
                      </p>
                    </div>
                  </div>

                  {/* رقم الهاتف */}
                  <div className="flex items-start space-x-4 space-x-reverse">
                    <div className="flex items-center justify-center w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg">
                      <Phone className="w-6 h-6 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-foreground mb-1">
                        رقم الهاتف
                      </h3>
                      <p className="text-secondary">
                        01002925291
                      </p>
                      <p className="text-sm text-secondary mt-1">
                        متاح للمكالمات والواتساب
                      </p>
                    </div>
                  </div>

                  {/* العنوان */}
                  <div className="flex items-start space-x-4 space-x-reverse">
                    <div className="flex items-center justify-center w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg">
                      <MapPin className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-foreground mb-1">
                        العنوان
                      </h3>
                      <p className="text-secondary">
                        ش سلامونى بالمنزلة دقهلية
                      </p>
                      <p className="text-sm text-secondary mt-1">
                        مصر
                      </p>
                    </div>
                  </div>

                  {/* أوقات العمل */}
                  <div className="flex items-start space-x-4 space-x-reverse">
                    <div className="flex items-center justify-center w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-lg">
                      <Clock className="w-6 h-6 text-orange-600 dark:text-orange-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-foreground mb-1">
                        أوقات العمل
                      </h3>
                      <p className="text-secondary">
                        الأحد - الخميس: 9:00 ص - 6:00 م
                      </p>
                      <p className="text-secondary">
                        الجمعة - السبت: 10:00 ص - 4:00 م
                      </p>
                      <p className="text-sm text-secondary mt-1">
                        الدعم الفني متاح 24/7 عبر البريد الإلكتروني
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* طرق التواصل السريع */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <h2 className="text-2xl font-bold text-foreground mb-6">
                  طرق التواصل السريع
                </h2>
                
                <div className="grid grid-cols-2 gap-4">
                  <a
                    href="mailto:<EMAIL>"
                    className="flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                  >
                    <Mail className="w-6 h-6 text-blue-600 dark:text-blue-400 ml-2" />
                    <span className="text-blue-600 dark:text-blue-400 font-medium">
                      إرسال إيميل
                    </span>
                  </a>
                  
                  <a
                    href="https://wa.me/201002925291"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
                  >
                    <MessageCircle className="w-6 h-6 text-green-600 dark:text-green-400 ml-2" />
                    <span className="text-green-600 dark:text-green-400 font-medium">
                      واتساب
                    </span>
                  </a>
                </div>
              </div>
            </div>

            {/* نموذج التواصل */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <h2 className="text-2xl font-bold text-foreground mb-6">
                أرسل لنا رسالة
              </h2>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-foreground mb-2">
                    الاسم الكامل *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-foreground"
                    placeholder="أدخل اسمك الكامل"
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-foreground mb-2">
                    البريد الإلكتروني *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-foreground"
                    placeholder="أدخل بريدك الإلكتروني"
                  />
                </div>

                <div>
                  <label htmlFor="subject" className="block text-sm font-medium text-foreground mb-2">
                    موضوع الرسالة *
                  </label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-foreground"
                    placeholder="موضوع رسالتك"
                  />
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-foreground mb-2">
                    الرسالة *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={6}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-foreground resize-none"
                    placeholder="اكتب رسالتك هنا..."
                  />
                </div>

                <button
                  type="submit"
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 flex items-center justify-center"
                >
                  <Send className="w-5 h-5 ml-2" />
                  إرسال الرسالة
                </button>
              </form>
            </div>
          </div>

          {/* معلومات إضافية */}
          <div className="mt-12 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-8">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-foreground mb-4">
                نحن هنا لمساعدتك
              </h2>
              <p className="text-secondary max-w-3xl mx-auto">
                فريق دعم منصة "معلمى" مستعد لمساعدتك في أي وقت. سواء كان لديك سؤال تقني، 
                أو تحتاج مساعدة في استخدام المنصة، أو لديك اقتراح لتحسين خدماتنا، 
                لا تتردد في التواصل معنا.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
