"use client";
import React from 'react';
import { BookOpen, Users, BarChart3, Shield, Headphones, Zap } from 'lucide-react';

export default function Services() {
  const services = [
    {
      icon: BookOpen,
      title: "إنشاء الدورات",
      description: "أنشئ دوراتك التعليمية بسهولة مع أدوات متقدمة لإدارة المحتوى",
      features: ["رفع الفيديوهات", "إضافة المواد التعليمية", "تنظيم الدروس"]
    },
    {
      icon: Users,
      title: "إدارة الطلاب",
      description: "تابع تقدم طلابك وتفاعل معهم بطريقة فعالة",
      features: ["متابعة التقدم", "التواصل المباشر", "تقارير مفصلة"]
    },
    {
      icon: BarChart3,
      title: "التحليلات",
      description: "احصل على تحليلات مفصلة عن أداء دوراتك وطلابك",
      features: ["إحصائيات شاملة", "تقارير الأداء", "رؤى تحليلية"]
    },
    {
      icon: Shield,
      title: "الأمان",
      description: "بيئة آمنة ومحمية لجميع المستخدمين",
      features: ["حماية البيانات", "تشفير متقدم", "خصوصية كاملة"]
    },
    {
      icon: Headphones,
      title: "الدعم الفني",
      description: "دعم فني متواصل لمساعدتك في أي وقت",
      features: ["دعم 24/7", "استجابة سريعة", "حلول فعالة"]
    },
    {
      icon: Zap,
      title: "الأداء",
      description: "منصة سريعة وموثوقة لتجربة تعليمية مميزة",
      features: ["سرعة عالية", "استقرار تام", "تحديثات مستمرة"]
    }
  ];

  return (
    <div className="min-h-screen pt-16 bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
              خدماتنا
            </h1>
            <p className="text-xl text-secondary max-w-3xl mx-auto">
              نقدم مجموعة شاملة من الخدمات التعليمية المتطورة لتلبية احتياجاتك
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            {services.map((service, index) => {
              const Icon = service.icon;
              return (
                <div
                  key={index}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300"
                >
                  <div className="flex items-center justify-center w-16 h-16 bg-primary/10 rounded-lg mb-4">
                    <Icon className="w-8 h-8 text-primary" />
                  </div>

                  <h3 className="text-xl font-semibold text-foreground mb-3">
                    {service.title}
                  </h3>

                  <p className="text-secondary mb-4 leading-relaxed">
                    {service.description}
                  </p>

                  <ul className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-foreground">
                        <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              );
            })}
          </div>

          <div className="mt-16 text-center">
            <div className="bg-primary/5 dark:bg-primary/10 rounded-lg p-8">
              <h2 className="text-2xl md:text-3xl font-bold text-foreground mb-4">
                هل أنت مستعد للبدء؟
              </h2>
              <p className="text-secondary mb-6 max-w-2xl mx-auto">
                انضم إلى آلاف المعلمين الذين يستخدمون منصتنا لتقديم تعليم عالي الجودة
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="/areYouInstructor"
                  className="bg-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors"
                >
                  ابدأ كمعلم
                </a>
                <a
                  href="/signup"
                  className="bg-white dark:bg-gray-800 text-primary border-2 border-primary px-6 py-3 rounded-lg font-semibold hover:bg-primary/5 transition-colors"
                >
                  سجل كطالب
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
