"use client";
import React, { useEffect, useRef, useState } from "react";
import {
  UserPlus,
  BookOpen,
  BarChart3,
  DollarSign,
  CheckCircle,
} from "lucide-react";

export default function HowItWorksSection() {
  const [activeStep, setActiveStep] = useState(0);
  const [visibleSteps, setVisibleSteps] = useState([]);
  const sectionRef = useRef(null);

  const steps = [
    {
      icon: UserPlus,
      title: "أنشئ حسابك",
      description: "سجل مجاناً في دقائق معدودة وابدأ رحلتك التعليمية",
      details: "عملية تسجيل بسيطة وسريعة مع تفعيل فوري للحساب",
      color: "from-blue-500 to-blue-600",
    },
    {
      icon: BookOpen,
      title: "اختر أو أنشئ كورسك",
      description: "استعرض الكورسات المتاحة أو أنشئ كورسك الخاص  ",
      details: "أدوات إنشاء متطورة مع قوالب جاهزة لتسهيل البداية",
      color: "from-green-500 to-green-600",
    },
    {
      icon: BarChart3,
      title: "راقب طلابك وتفاعل معهم",
      description: "تابع تقدم الطلاب وتفاعل معهم من خلال لوحة التحكم الذكية",
      details: "إحصائيات مفصلة وأدوات تفاعل متقدمة لضمان أفضل تجربة تعليمية",
      color: "from-purple-500 to-purple-600",
    },
    {
      icon: DollarSign,
      title: "اربح من خبرتك",
      description: "احصل على أرباحك بانتظام مع نظام دفع آمن وشفاف",
      details: "عمولة منخفضة 1% فقط مع دفعات شهرية منتظمة",
      color: "from-orange-500 to-orange-600",
    },
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.dataset.index);
            setVisibleSteps((prev) => [...new Set([...prev, index])]);
          }
        });
      },
      { threshold: 0.3 }
    );

    const stepElements = sectionRef.current?.querySelectorAll("[data-index]");
    stepElements?.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveStep((prev) => (prev + 1) % steps.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [steps.length]);

  return (
    <section ref={sectionRef} className="py-20 bg-white dark:bg-gray-800">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              كيف تعمل
            </span>
            <br />
            المنصة؟
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            أربع خطوات بسيطة للبدء في رحلتك التعليمية وتحقيق أهدافك
          </p>
        </div>

        {/* Steps */}
        <div className="max-w-6xl mx-auto">
          {/* Desktop View */}
          <div className="hidden lg:block">
            <div className="relative">
              {/* Progress Line */}
              <div className="absolute top-1/2 left-0  right-0 h-1 bg-gray-200 dark:bg-gray-700 transform -translate-y-1/2 z-0">
                <div
                  className="h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-1000 ease-out"
                  style={{
                    width: `${((activeStep + 1) / steps.length) * 100}%`,
                  }}
                ></div>
              </div>

              {/* Steps */}
              <div className="relative z-10 grid grid-cols-4 gap-8">
                {steps.map((step, index) => {
                  const Icon = step.icon;
                  const isActive = index <= activeStep;
                  const isVisible = visibleSteps.includes(index);

                  return (
                    <div
                      key={index}
                      data-index={index}
                      className={`text-center ${
                        isVisible ? "animate-slide-up opacity-100" : "opacity-0"
                      }`}
                      style={{ animationDelay: `${index * 0.2}s` }}
                    >
                      {/* Step Icon */}
                      <div
                        className={`relative mx-auto w-20 h-20 rounded-full flex items-center justify-center mb-6 transition-all duration-500 ${
                          isActive
                            ? `bg-gradient-to-r ${step.color} shadow-lg scale-110`
                            : "bg-gray-200 dark:bg-gray-700"
                        }`}
                      >
                        <Icon
                          className={`w-10 h-10 transition-colors duration-300 ${
                            isActive
                              ? "text-white"
                              : "text-gray-400 dark:text-gray-500"
                          }`}
                        />

                        {/* Check mark for completed steps */}
                        {index < activeStep && (
                          <div className="absolute -top-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <CheckCircle className="w-5 h-5 text-white" />
                          </div>
                        )}
                      </div>

                      {/* Step Content */}
                      <div className="space-y-3">
                        <h3
                          className={`text-xl font-bold transition-colors duration-300 ${
                            isActive
                              ? "text-blue-600 dark:text-blue-400"
                              : "text-gray-700 dark:text-gray-300"
                          }`}
                        >
                          {step.title}
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
                          {step.description}
                        </p>
                        {isActive && (
                          <p className="text-xs text-blue-600 dark:text-blue-400 animate-fade-in">
                            {step.details}
                          </p>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Mobile View */}
          <div className="lg:hidden space-y-8">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isVisible = visibleSteps.includes(index);

              return (
                <div
                  key={index}
                  data-index={index}
                  className={`flex items-start space-x-6 space-x-reverse p-6 rounded-2xl bg-gray-50 dark:bg-gray-700 ${
                    isVisible ? "animate-slide-up opacity-100" : "opacity-0"
                  }`}
                  style={{ animationDelay: `${index * 0.2}s` }}
                >
                  {/* Step Number & Icon */}
                  <div className="flex-shrink-0">
                    <div
                      className={`w-16 h-16 rounded-full bg-gradient-to-r ${step.color} flex items-center justify-center mb-2`}
                    >
                      <Icon className="w-8 h-8 text-white" />
                    </div>
                    <div className="text-center">
                      <span className="text-sm font-bold text-gray-500 dark:text-gray-400">
                        {index + 1}
                      </span>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                      {step.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-2">
                      {step.description}
                    </p>
                    <p className="text-sm text-blue-600 dark:text-blue-400">
                      {step.details}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
            جاهز للبدء؟ انضم إلينا اليوم!
          </p>
          <button className="inline-flex items-center px-8 py-4 text-lg font-bold text-white bg-gradient-to-r from-blue-600 to-purple-600 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
            ابدأ الآن مجاناً
          </button>
        </div>
      </div>
    </section>
  );
}
