"use client";

import React, { useState, useEffect } from "react";
import {
  MdInfoOutline,
  MdShare,
  MdDeleteOutline,
  MdExpandMore,
  MdExpandLess,
} from "react-icons/md";
import {
  FaWhatsapp,
  FaLinkedinIn,
  FaInstagram,
  FaTwitter,
  FaFacebookF,
} from "react-icons/fa";
import {
  fetchInstructorAvailabilities,
  deleteAvailabilityById,
  updateAvailability,
  createAvailability,
} from "../../../../../services/appointments";

const daysOfWeek = [
  { name: "السبت", key: "saturday" },
  { name: "الأحد", key: "sunday" },
  { name: "الإثنين", key: "monday" },
  { name: "الثلاثاء", key: "tuesday" },
  { name: "الأربعاء", key: "wednesday" },
  { name: "الخميس", key: "thursday" },
  { name: "الجمعة", key: "friday" },
];

const defaultFrom = "10:00";
const defaultTo = "12:00";

function formatToAmPm(time24) {
  const [hourStr, minute] = time24.split(":");
  let hour = parseInt(hourStr);
  const suffix = hour >= 12 ? "PM" : "AM";
  if (hour > 12) hour -= 12;
  if (hour === 0) hour = 12;
  return `${suffix} ${hour}:${minute}`;
}

function AppointmentsPage() {
  const [activeTab, setActiveTab] = useState("weekly");
  const [timezone, setTimezone] = useState("أفريقيا / القاهرة");
  const [availabilities, setAvailabilities] = useState({
    saturday: [],
    sunday: [],
    monday: [],
    tuesday: [],
    wednesday: [],
    thursday: [],
    friday: [],
  });

  const addSlot = (day) => {
    setAvailabilities((prev) => ({
      ...prev,
      [day]: [...prev[day], { from: defaultFrom, to: defaultTo, note: "" }],
    }));
  };

  const toTimeInput = (val) => {
    if (!val) return "";
    if (/^\d{2}:\d{2}$/.test(val)) return val;
    const match = val.match(/(AM|PM) (\d{1,2}):(\d{2})/);
    if (match) {
      let hour = parseInt(match[2], 10);
      const min = match[3];
      if (match[1] === "PM" && hour !== 12) hour += 12;
      if (match[1] === "AM" && hour === 12) hour = 0;
      return `${hour.toString().padStart(2, "0")}:${min}`;
    }
    return val;
  };

  useEffect(() => {
    const loadData = async () => {
      try {
        const data = await fetchInstructorAvailabilities();
        const grouped = {
          saturday: [],
          sunday: [],
          monday: [],
          tuesday: [],
          wednesday: [],
          thursday: [],
          friday: [],
        };
        data.forEach((item) => {
          grouped[item.day].push({
            id: item.id,
            from: toTimeInput(item.from_time),
            to: toTimeInput(item.to_time),
            note: item.note || "",
          });
        });
        setAvailabilities(grouped);
        if (data[0]?.timezone) setTimezone(data[0].timezone);
      } catch (e) {}
    };
    loadData();
  }, []);

  const removeSlot = async (day, idx) => {
    const slot = availabilities[day][idx];
    setAvailabilities((prev) => ({
      ...prev,
      [day]: prev[day].filter((_, i) => i !== idx),
    }));
    if (slot.id) {
      try {
        await deleteAvailabilityById(slot.id);
      } catch (e) {}
    }
  };

  const updateSlot = (day, idx, field, value) => {
    setAvailabilities((prev) => {
      const updated = [...prev[day]];
      updated[idx][field] = value;
      return { ...prev, [day]: updated };
    });
  };

  const handleSave = async () => {
    let oldData = [];
    try {
      oldData = await fetchInstructorAvailabilities();
    } catch (e) {}

    const allowedDays = [
      "saturday",
      "sunday",
      "monday",
      "tuesday",
      "wednesday",
      "thursday",
      "friday",
    ];
    const newList = [];
    for (const day of Object.keys(availabilities)) {
      for (const slot of availabilities[day]) {
        if (!allowedDays.includes(day)) continue;
        newList.push({
          day,
          from_time: formatToAmPm(slot.from),
          to_time: formatToAmPm(slot.to),
          timezone,
          enabled: true,
          note: slot.note || "",
        });
      }
    }

    for (const old of oldData) {
      const stillExist = newList.some(
        (n) =>
          n.day === old.day &&
          n.from_time === old.from_time &&
          n.to_time === old.to_time &&
          (n.note || "") === (old.note || "")
      );
      if (!stillExist) {
        try {
          await deleteAvailabilityById(old.id);
        } catch (e) {}
      }
    }

    for (const slot of newList) {
      const existing = oldData.find(
        (old) =>
          old.day === slot.day &&
          old.from_time === slot.from_time &&
          old.to_time === slot.to_time &&
          (old.note || "") === (slot.note || "")
      );
      if (existing) {
        try {
          await updateAvailability(existing.id, slot);
        } catch (err) {
          const msg = err.response?.data
            ? JSON.stringify(err.response.data)
            : err.message;
          alert("خطأ في تحديث الموعد: " + msg);
        }
      } else {
        try {
          await createAvailability(slot);
        } catch (err) {
          const msg = err.response?.data
            ? JSON.stringify(err.response.data)
            : err.message;
          alert("خطأ في إضافة الموعد: " + msg);
        }
      }
    }
    alert("تم حفظ المواعيد بنجاح");
  };

  return (
    <div
      dir="rtl"
      className="min-h-screen bg-gradient-to-b  from-white via-gray-50 to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900"
    >
      {/* Main content */}
      <main className="flex-grow mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 w-full max-w-7xl">
        {/* Top bar with share and faq */}
        <div className="hidden flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-0 mb-6 sm:mb-8">
          <div className="flex items-center gap-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-800 px-4 py-2 text-gray-700 dark:text-gray-300 cursor-pointer select-none hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 shadow-sm hover:shadow-md">
            <MdShare className="text-lg flex-shrink-0" />
            <span className="text-sm font-medium whitespace-nowrap">
              مشاركة صفحتي
            </span>
          </div>
          <button
            type="button"
            aria-label="الأسئلة الشائعة"
            className="flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            <MdInfoOutline size={18} className="flex-shrink-0" />
            <span className="text-sm font-medium">الأسئلة الشائعة</span>
          </button>
        </div>

        {/* Card container */}
        <section className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg dark:shadow-gray-900/20 p-6 sm:p-8 border border-gray-100 dark:border-gray-700 transition-all duration-300">
          {/* Title & subtitle */}
          <div className="mb-8">
            <h2 className="text-xl sm:text-2xl font-bold mb-2 text-gray-900 dark:text-white bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              المواعيد المتاحة
            </h2>
            <p className="text-gray-500 dark:text-gray-400 text-sm sm:text-base">
              اختر المواعيد التي ترغب تقديم خدمتك فيها
            </p>
          </div>

          {/* Tabs */}
          <div className="flex gap-8 border-b border-gray-200 dark:border-gray-700 mb-8 font-semibold text-sm sm:text-base">
            <button
              onClick={() => setActiveTab("weekly")}
              className={`pb-3 px-2 transition-all duration-200 relative ${
                activeTab === "weekly"
                  ? "text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400"
                  : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
              }`}
              type="button"
            >
              الساعات الأسبوعية
              {activeTab === "weekly" && (
                <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full"></div>
              )}
            </button>
            <button
              onClick={() => setActiveTab("calendar")}
              className={`pb-3 px-2 transition-all duration-200 relative ${
                activeTab === "calendar"
                  ? "text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400"
                  : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
              }`}
              type="button"
            >
              التقويم
              {activeTab === "calendar" && (
                <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full"></div>
              )}
            </button>
          </div>

          {/* Tab Content */}
          {activeTab === "weekly" && (
            <div className="space-y-8">
              {/* Timezone selector */}
              <div className="space-y-3">
                <label className="text-sm sm:text-base font-medium text-gray-600 dark:text-gray-300 block">
                  التوقيت الزمني (مطلوب)
                </label>
                <select
                  className="w-full rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 p-4 text-gray-700 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent transition-all duration-200 shadow-sm hover:shadow-md"
                  value={timezone}
                  onChange={(e) => setTimezone(e.target.value)}
                  aria-label="اختيار التوقيت الزمني"
                >
                  <option>أفريقيا / القاهرة</option>
                  <option>أوروبا / لندن</option>
                  <option>آسيا / طوكيو</option>
                  <option>أمريكا / نيويورك</option>
                </select>
              </div>

              {/* Working hours */}
              <div>
                <h3 className="font-bold text-lg sm:text-xl mb-6 text-gray-900 dark:text-white flex items-center gap-2">
                  <div className="w-1 h-6 bg-gradient-to-b from-blue-600 to-purple-600 rounded-full"></div>
                  ساعات العمل
                </h3>
                <div className="space-y-6">
                  {daysOfWeek.map(({ name, key }) => (
                    <div
                      key={key}
                      className="bg-gray-50 dark:bg-gray-700/30 rounded-xl p-4 sm:p-6 border border-gray-200 dark:border-gray-600 transition-all duration-200 hover:shadow-md"
                    >
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-4">
                        <span className="font-bold text-lg text-gray-900 dark:text-white">
                          {name}
                        </span>
                        <button
                          type="button"
                          className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white rounded-lg px-4 py-2 text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 flex items-center gap-2 w-fit"
                          onClick={() => addSlot(key)}
                        >
                          <span className="text-lg">+</span>
                          إضافة موعد
                        </button>
                      </div>

                      {availabilities[key].length === 0 && (
                        <div className="text-gray-500 dark:text-gray-400 text-sm bg-white dark:bg-gray-800 rounded-lg p-4 text-center border-2 border-dashed border-gray-300 dark:border-gray-600">
                          لا يوجد مواعيد محددة لهذا اليوم
                        </div>
                      )}

                      <div className="space-y-3">
                        {availabilities[key].map((slot, idx) => (
                          <div
                            key={idx}
                            className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600 shadow-sm"
                          >
                            <div className="flex flex-col lg:flex-row lg:items-center gap-3">
                              <div className="flex items-center gap-2 flex-1">
                                <input
                                  type="time"
                                  className="w-32 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all duration-200"
                                  value={slot.from}
                                  step="900" // ربع ساعة
                                  onChange={(e) =>
                                    updateSlot(key, idx, "from", e.target.value)
                                  }
                                />
                                <span className="text-gray-500 dark:text-gray-400 font-medium">
                                  إلى
                                </span>
                                <input
                                  type="time"
                                  className="w-32 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all duration-200"
                                  value={slot.to}
                                  step="900"
                                  onChange={(e) =>
                                    updateSlot(key, idx, "to", e.target.value)
                                  }
                                />
                              </div>

                              <div className="flex items-center gap-2 flex-1">
                                <input
                                  className="flex-1 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all duration-200"
                                  placeholder="ملاحظة/وصف (اختياري)"
                                  value={slot.note}
                                  onChange={(e) =>
                                    updateSlot(key, idx, "note", e.target.value)
                                  }
                                />
                                <button
                                  type="button"
                                  className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-200"
                                  onClick={() => removeSlot(key, idx)}
                                  title="حذف الموعد"
                                >
                                  <MdDeleteOutline size={20} />
                                </button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Save button */}
              <div className="mt-8 flex justify-center sm:justify-end">
                <button
                  type="button"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl py-3 px-8 font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-2 min-w-[120px] justify-center"
                  onClick={handleSave}
                >
                  <span>حفظ المواعيد</span>
                </button>
              </div>
            </div>
          )}

          {activeTab === "calendar" && (
            <div className="text-center text-gray-500 dark:text-gray-400 py-16">
              <div className="bg-gray-100 dark:bg-gray-700/50 rounded-2xl p-8 max-w-md mx-auto">
                <div className="text-6xl mb-4">📅</div>
                <p className="text-lg font-medium mb-2">عرض التقويم</p>
                <p className="text-sm">
                  عرض تقويم المواعيد غير متوفر في هذه النسخة التجريبية
                </p>
              </div>
            </div>
          )}
        </section>

        {/* Footer */}
        <footer className="hidden mt-16 mb-6 max-w-5xl mx-auto px-4 sm:px-6  flex-col sm:flex-row justify-between items-center text-gray-500 dark:text-gray-400 text-xs sm:text-sm select-none">
          <div className="flex items-center gap-4 sm:gap-6 mb-4 sm:mb-0">
            {/* WhatsApp with green dot */}
            <div className="relative group">
              <FaWhatsapp
                size={20}
                className="text-green-600 dark:text-green-500 cursor-pointer hover:text-green-700 dark:hover:text-green-400 transition-colors duration-200"
                aria-label="واتساب"
              />
              <span className="absolute -top-1 -left-1 w-3 h-3 rounded-full bg-green-500 border-2 border-white dark:border-gray-800 animate-pulse"></span>
            </div>
            <FaLinkedinIn
              size={20}
              className="cursor-pointer text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
              aria-label="لينكدإن"
            />
            <FaInstagram
              size={20}
              className="cursor-pointer text-gray-500 dark:text-gray-400 hover:text-pink-600 dark:hover:text-pink-400 transition-colors duration-200"
              aria-label="إنستجرام"
            />
            <FaTwitter
              size={20}
              className="cursor-pointer text-gray-500 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-300 transition-colors duration-200"
              aria-label="تويتر"
            />
            <FaFacebookF
              size={20}
              className="cursor-pointer text-gray-500 dark:text-gray-400 hover:text-blue-700 dark:hover:text-blue-500 transition-colors duration-200"
              aria-label="فيسبوك"
            />
          </div>
          <div className="text-center sm:text-right">
            الحقوق محفوظة لموقع نظامي © 2025
          </div>
        </footer>
      </main>
    </div>
  );
}

export default AppointmentsPage;
