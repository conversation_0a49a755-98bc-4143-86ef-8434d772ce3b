// صفحة تقارير المبيعات للمعلم مع ربط الباك اند - zaki alkholy
"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useSelector } from "react-redux";
import Cookies from "js-cookie";
import { toast } from "react-hot-toast";
import {
  DollarSign,
  TrendingUp,
  ShoppingCart,
  Calendar,
  Download,
  Eye,
  CreditCard,
  Target,
  BarChart3,
  Users,
} from "lucide-react";

import { selectCurrentUser, selectIsAuthenticated } from "@/store/authSlice";
import { API_BASE_URL } from "@/config/api";
import axios from "axios";

// مكون لعرض بطاقة إحصائية مالية - zaki alkholy
const SalesStatCard = ({
  icon: Icon,
  title,
  value,
  change,
  changeType,
  color = "blue",
}) => {
  const colorClasses = {
    blue: "bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 border-blue-200 dark:border-blue-700",
    green:
      "bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 border-green-200 dark:border-green-700",
    purple:
      "bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 border-purple-200 dark:border-purple-700",
    orange:
      "bg-orange-50 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 border-orange-200 dark:border-orange-700",
    red: "bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 border-red-200 dark:border-red-700",
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`p-6 rounded-xl border-2 ${colorClasses[color]} hover:shadow-lg dark:hover:shadow-gray-900/20 transition-all duration-300 bg-white dark:bg-gray-800`}
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
            {title}
          </p>
          <p className="text-2xl font-bold mt-1 text-gray-900 dark:text-gray-100">
            {value}
          </p>
          {change && (
            <div className="flex items-center mt-2">
              <TrendingUp
                className={`w-4 h-4 mr-1 ${
                  changeType === "increase"
                    ? "text-green-500 dark:text-green-400"
                    : "text-red-500 dark:text-red-400"
                }`}
              />
              <span
                className={`text-xs ${
                  changeType === "increase"
                    ? "text-green-600 dark:text-green-400"
                    : "text-red-600 dark:text-red-400"
                }`}
              >
                {change}
              </span>
            </div>
          )}
        </div>
        <Icon className="w-8 h-8" />
      </div>
    </motion.div>
  );
};

// مكون لعرض جدول المبيعات الأخيرة - zaki alkholy
const RecentSalesTable = ({ sales }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm"
    >
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
        <ShoppingCart className="w-5 h-5 text-blue-500 dark:text-blue-400 mr-2" />
        المبيعات الأخيرة
      </h3>
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-200 dark:border-gray-700">
              <th className="text-right py-3 px-4 font-medium text-gray-700 dark:text-gray-300">
                الطالب
              </th>
              <th className="text-right py-3 px-4 font-medium text-gray-700 dark:text-gray-300">
                الكورس
              </th>
              <th className="text-right py-3 px-4 font-medium text-gray-700 dark:text-gray-300">
                المبلغ
              </th>
              <th className="text-right py-3 px-4 font-medium text-gray-700 dark:text-gray-300">
                التاريخ
              </th>
              <th className="text-right py-3 px-4 font-medium text-gray-700 dark:text-gray-300">
                الحالة
              </th>
            </tr>
          </thead>
          <tbody>
            {sales?.slice(0, 10).map((sale, index) => (
              <tr
                key={index}
                className="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50"
              >
                <td className="py-3 px-4">
                  <div className="flex items-center">
                    <img
                      src={sale.student_image || "/images/default-course.jpg"}
                      alt={sale.student_name}
                      className="w-8 h-8 rounded-full object-cover mr-3"
                    />
                    <span className="font-medium text-gray-900 dark:text-gray-100">
                      {sale.student_name}
                    </span>
                  </div>
                </td>
                <td className="py-3 px-4 text-gray-700 dark:text-gray-300">
                  {sale.course_title}
                </td>
                <td className="py-3 px-4 font-semibold text-green-600 dark:text-green-400">
                  {sale.amount} ج.م
                </td>
                <td className="py-3 px-4 text-gray-500 dark:text-gray-400">
                  {new Date(sale.created_at).toLocaleDateString("ar-EG")}
                </td>
                <td className="py-3 px-4">
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${
                      sale.status === "completed"
                        ? "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400"
                        : sale.status === "pending"
                        ? "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400"
                        : "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400"
                    }`}
                  >
                    {sale.status === "completed"
                      ? "مكتمل"
                      : sale.status === "pending"
                      ? "معلق"
                      : "ملغي"}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </motion.div>
  );
};

// مكون لعرض أفضل الكورسات مبيعاً - zaki alkholy
const TopSellingCourses = ({ courses }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm"
    >
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
        <Target className="w-5 h-5 text-purple-500 dark:text-purple-400 mr-2" />
        أفضل الكورسات مبيعاً
      </h3>
      <div className="space-y-4">
        {courses?.slice(0, 5).map((course, index) => (
          <div
            key={course.id}
            className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg"
          >
            <div className="flex items-center">
              <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mr-3">
                <span className="text-sm font-bold text-purple-600 dark:text-purple-400">
                  {index + 1}
                </span>
              </div>
              <div>
                <p className="font-medium text-gray-900 dark:text-gray-100">
                  {course.title}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {course.sales_count} مبيعة
                </p>
              </div>
            </div>
            <div className="text-right">
              <p className="font-semibold text-green-600 dark:text-green-400">
                {course.total_revenue} ج.م
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {course.price} ج.م/كورس
              </p>
            </div>
          </div>
        ))}
      </div>
    </motion.div>
  );
};

// الصفحة الرئيسية لتقارير المبيعات - zaki alkholy
export default function InstructorSalesPage() {
  const [salesData, setSalesData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [timeRange, setTimeRange] = useState("30"); // آخر 30 يوم افتراضياً

  // الحصول على بيانات المستخدم من Redux - zaki alkholy
  const user = useSelector(selectCurrentUser);
  const isAuthenticated = useSelector(selectIsAuthenticated);

  // جلب بيانات المبيعات من API - zaki alkholy
  useEffect(() => {
    const fetchSalesData = async () => {
      if (!isAuthenticated || !user || !user.is_instructor) {
        setLoading(false);
        return;
      }

      try {
        const token =
          Cookies.get("authToken") || localStorage.getItem("access_token");
        if (!token) {
          throw new Error("لا يوجد رمز مصادقة");
        }

        const headers = {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        };

        // جلب تقارير المبيعات - zaki alkholy
        const salesResponse = await axios.get(
          `${API_BASE_URL}/api/instructor/sales/`,
          {
            headers,
            params: { period: timeRange },
          }
        );

        setSalesData(salesResponse.data);
      } catch (error) {
        console.error("خطأ في جلب بيانات المبيعات:", error);
        setError(error.message);
        toast.error("حدث خطأ في جلب البيانات");
      } finally {
        setLoading(false);
      }
    };

    fetchSalesData();
  }, [isAuthenticated, user, timeRange]);

  // عرض شاشة التحميل - zaki alkholy
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 dark:border-blue-400"></div>
      </div>
    );
  }

  // عرض رسالة خطأ إذا لم يكن المستخدم معلم - zaki alkholy
  if (!isAuthenticated || !user || !user.is_instructor) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            غير مصرح لك بالوصول
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            هذه الصفحة متاحة للمعلمين فقط
          </p>
        </div>
      </div>
    );
  }

  const stats = salesData?.overview || {};

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            تقارير المبيعات
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            تتبع إيراداتك ومبيعاتك وأداء كورساتك المالي
          </p>

          {/* فلتر الوقت */}
          <div className="mt-4 flex gap-2">
            {[
              { value: "7", label: "آخر 7 أيام" },
              { value: "30", label: "آخر 30 يوم" },
              { value: "90", label: "آخر 3 شهور" },
              { value: "365", label: "آخر سنة" },
            ].map((option) => (
              <button
                key={option.value}
                onClick={() => setTimeRange(option.value)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  timeRange === option.value
                    ? "bg-blue-600 dark:bg-blue-500 text-white"
                    : "bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-600"
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>

        {/* الإحصائيات المالية */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <SalesStatCard
            icon={DollarSign}
            title="إجمالي الإيرادات"
            value={`${stats.total_revenue || 0} ج.م`}
            change={stats.revenue_change}
            changeType={stats.revenue_change_type}
            color="green"
          />
          <SalesStatCard
            icon={ShoppingCart}
            title="إجمالي المبيعات"
            value={stats.total_sales || 0}
            change={stats.sales_change}
            changeType={stats.sales_change_type}
            color="blue"
          />
          <SalesStatCard
            icon={Users}
            title="عدد المشترين"
            value={stats.total_buyers || 0}
            change={stats.buyers_change}
            changeType={stats.buyers_change_type}
            color="purple"
          />
          <SalesStatCard
            icon={Target}
            title="متوسط سعر البيع"
            value={`${stats.average_sale_price || 0} ج.م`}
            change={stats.avg_price_change}
            changeType={stats.avg_price_change_type}
            color="orange"
          />
        </div>

        {/* المحتوى الرئيسي */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* المبيعات الأخيرة */}
          <div className="lg:col-span-2">
            <RecentSalesTable sales={salesData?.recent_sales} />
          </div>

          {/* أفضل الكورسات مبيعاً */}
          <TopSellingCourses courses={salesData?.top_selling_courses} />

          {/* إحصائيات إضافية */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <BarChart3 className="w-5 h-5 text-green-500 dark:text-green-400 mr-2" />
              إحصائيات إضافية
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <span className="text-gray-700 dark:text-gray-300">
                  معدل التحويل
                </span>
                <span className="font-semibold text-blue-600 dark:text-blue-400">
                  {stats.conversion_rate || 0}%
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <span className="text-gray-700 dark:text-gray-300">
                  متوسط الإيرادات الشهرية
                </span>
                <span className="font-semibold text-green-600 dark:text-green-400">
                  {stats.monthly_avg_revenue || 0} ج.م
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <span className="text-gray-700 dark:text-gray-300">
                  أعلى مبيعة
                </span>
                <span className="font-semibold text-purple-600 dark:text-purple-400">
                  {stats.highest_sale || 0} ج.م
                </span>
              </div>
            </div>
          </motion.div>
        </div>

        {/* رسالة إذا لم تكن هناك بيانات */}
        {!salesData && (
          <div className="text-center py-12">
            <DollarSign className="w-16 h-16 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              لا توجد مبيعات بعد
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              ابدأ بإنشاء كورسات وتسويقها لرؤية تقارير المبيعات
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
