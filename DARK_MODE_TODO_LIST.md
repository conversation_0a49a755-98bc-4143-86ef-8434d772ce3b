# قائمة مهام الدارك مود - Dark Mode TODO List

**تاريخ الإنشاء:** يوليو 2025  
**المطور:** zaki alkholy  

---

## 🚀 الأولوية العالية (High Priority)

### 1. صفحات الطلاب الأساسية
- [ ] **لوحة تحكم الطالب**
  - الملف: `src/app/(pages)/student/dashboard/page.jsx`
  - المطلوب: إضافة dark mode classes للخلفيات والنصوص
  
- [ ] **صفحة كورسات الطالب**
  - الملف: `src/app/(pages)/student/courses/page.jsx`
  - المطلوب: تحديث بطاقات الكورسات وشريط التقدم

- [ ] **صفحة مشاهدة الكورس**
  - الملف: `src/app/(pages)/student/course/[id]/page.jsx`
  - المطلوب: تحديث مشغل الفيديو والمحتوى

### 2. صفحات المدرس المتقدمة
- [ ] **الإحصائيات المتقدمة**
  - الملف: `src/app/(pages)/instructor/advanced-analytics/page.jsx`
  - المطلوب: تحديث الرسوم البيانية والبطاقات

- [ ] **تقارير المبيعات**
  - الملف: `src/app/(pages)/instructor/sales/page.jsx`
  - المطلوب: تحديث جداول البيانات والإحصائيات

- [ ] **إدارة الواجبات**
  - الملف: `src/app/(pages)/instructor/assignments/page.jsx`
  - المطلوب: تحديث نماذج الواجبات والاختبارات

---

## 📊 الأولوية المتوسطة (Medium Priority)

### 3. صفحات الملف الشخصي
- [ ] **ملف المدرس الشخصي**
  - الملف: `src/app/(pages)/instructor/dashboard/profile/page.jsx`
  - المطلوب: تحديث نماذج التعديل والصور

- [ ] **إعدادات الطالب**
  - الملف: `src/app/(pages)/student/[id]/settings/page.jsx`
  - المطلوب: تحديث نماذج الإعدادات

### 4. صفحات الكورسات
- [ ] **إنشاء كورس جديد**
  - الملف: `src/app/(pages)/instructor/courses/create/page.jsx`
  - المطلوب: تحديث النماذج والمعاينة

- [ ] **تعديل الكورس**
  - الملف: `src/app/(pages)/instructor/courses/[id]/edit/page.jsx`
  - المطلوب: تحديث أدوات التحرير

### 5. صفحات الدفع
- [ ] **صفحة الدفع**
  - الملف: `src/app/(pages)/student/payment/page.jsx`
  - المطلوب: تحديث نماذج الدفع والبطاقات

- [ ] **نجح الدفع**
  - الملف: `src/app/(pages)/student/payment/success/page.jsx`
  - المطلوب: تحديث رسائل النجاح

- [ ] **فشل الدفع**
  - الملف: `src/app/(pages)/student/payment/failed/page.jsx`
  - المطلوب: تحديث رسائل الخطأ

---

## 🔧 الأولوية المنخفضة (Low Priority)

### 6. صفحات التطوير
- [ ] **صفحة اختبار API**
  - الملف: `src/app/(pages)/dev/api-test/page.jsx`
  - المطلوب: تحديث واجهة الاختبار

- [ ] **صفحة اختبار الاتصال**
  - الملف: `src/app/(pages)/test-connection/page.jsx`
  - المطلوب: تحديث رسائل الحالة

### 7. صفحات إضافية
- [ ] **صفحة من نحن**
  - الملف: `src/app/(pages)/about/page.jsx`
  - المطلوب: تحديث المحتوى والصور

- [ ] **صفحة اتصل بنا**
  - الملف: `src/app/(pages)/contact/page.jsx`
  - المطلوب: تحديث النماذج

- [ ] **صفحة الخدمات**
  - الملف: `src/app/(pages)/Services/page.jsx`
  - المطلوب: تحديث بطاقات الخدمات

---

## 🎨 نمط الإصلاح المطلوب

### للخلفيات:
```jsx
// قبل الإصلاح
className="bg-white"
className="bg-gray-50"

// بعد الإصلاح
className="bg-white dark:bg-gray-800"
className="bg-gray-50 dark:bg-gray-900"
```

### للنصوص:
```jsx
// قبل الإصلاح
className="text-gray-900"
className="text-gray-600"

// بعد الإصلاح
className="text-gray-900 dark:text-white"
className="text-gray-600 dark:text-gray-400"
```

### للحدود:
```jsx
// قبل الإصلاح
className="border-gray-200"

// بعد الإصلاح
className="border-gray-200 dark:border-gray-700"
```

### للألوان الملونة:
```jsx
// قبل الإصلاح
className="text-blue-600 bg-blue-50"

// بعد الإصلاح
className="text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20"
```

---

## ✅ الصفحات المكتملة

- [x] **صفحة تقدم الطالب** - `src/app/(pages)/student/progress/page.jsx`
- [x] **صفحة إدارة الطلاب** - `src/app/(pages)/instructor/students/page.jsx`
- [x] **الصفحة الرئيسية** - `src/app/page.jsx`
- [x] **تسجيل الدخول** - `src/app/(pages)/login/page.jsx`
- [x] **إنشاء حساب** - `src/app/(pages)/signup/page.jsx`
- [x] **إعادة تعيين كلمة المرور** - `src/app/(pages)/reset-password/page.jsx`
- [x] **تأكيد البريد الإلكتروني** - `src/app/(pages)/verify-email/page.jsx`
- [x] **صفحة الخدمات** - `src/app/(pages)/Services/page.jsx`
- [x] **صفحة من نحن** - `src/app/(pages)/about/page.jsx`
- [x] **صفحة اختبار الاتصال** - `src/app/(pages)/test-connection/page.jsx`
- [x] **صفحة اختبار التقدم** - `src/app/(pages)/test-progress/page.jsx`
- [x] **صفحة دليل الميزات** - `src/app/(pages)/features-guide/page.jsx`
- [x] **صفحة Not Found** - `src/app/(pages)/Notfound/page.jsx`
- [x] **صفحة الإحصائيات المتقدمة** - `src/app/(pages)/instructor/advanced-analytics/page.jsx`
- [x] **صفحة إدارة الواجبات** - `src/app/(pages)/instructor/assignments/page.jsx`
- [x] **Navbar** - `src/app/_Components/Navbar/Navbar.jsx`
- [x] **Aside** - `src/app/_Components/DashboardComponent/Aside.jsx`
- [x] **Dashboard Component** - `src/app/_Components/DashboardComponent/DashboardComponent.jsx`

---

## 📋 ملاحظات مهمة

1. **استخدم CSS Variables** حيث أمكن (`bg-background`, `text-foreground`)
2. **احتفظ بالتناسق** في الألوان عبر المشروع
3. **اختبر جميع الحالات** (hover, focus, active)
4. **تأكد من قابلية القراءة** في الوضع الداكن
5. **استخدم opacity** للخلفيات الملونة (`bg-blue-900/20`)

---

## 🔄 تتبع التقدم

**الإجمالي:** 30+ صفحة
**مكتمل:** 18 صفحة (60%)
**متبقي:** 12+ صفحة (40%)

**الهدف:** إكمال جميع الصفحات ذات الأولوية العالية أولاً

**آخر تحديث:** تم إكمال 10 صفحات إضافية في هذه الجلسة!

---

**آخر تحديث:** يوليو 2025  
**بواسطة:** zaki alkholy
