"use client";
import React from "react";
import { UserPlus, Users, Eye, ClipboardCheck, Coins } from "lucide-react";

export default function Teacheradvantage() {
  const advantages = [
    {
      icon: UserPlus,
      title: "سجل مجانًا وابدأ فورًا",
      description: "انضم إلى منصتنا بسهولة وابدأ بتدريس الدروس الخاصة بك دون أي تكلفة أولية.",
      isSpecial: true
    },
    {
      icon: Users,
      title: "تحكم في عدد الطلاب",
      description: "اختر عدد الطلاب الذين تريد تدريسهم وقم بإدارة الفصول الدراسية بمرونة.",
      isSpecial: false
    },
    {
      icon: Eye,
      title: "اعرف مين شاف الدروس",
      description: "تابع تفاعل الطلاب مع دروسك من خلال تقارير وإحصائيات دقيقة.",
      isSpecial: false
    },
    {
      icon: ClipboardCheck,
      title: "أنشئ اختبارات وتابع التفاعل",
      description: "صمم اختبارات مخصصة وقيّم أداء طلابك بسهولة وفعالية.",
      isSpecial: false
    },
    {
      icon: Coins,
      title: "عمولة فقط 1% على كل دورة",
      description: "استمتع بأرباحك مع عمولة رمزية لا تتجاوز 1% على كل دورة.",
      isSpecial: false
    }
  ];

  return (
    <div className="bg-background">
      <section className="py-12 sm:py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl sm:text-4xl font-bold text-primary text-center mb-8 sm:mb-12">
            مميزات للمدرسين
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {advantages.map((advantage, index) => {
              const Icon = advantage.icon;
              return (
                <div
                  key={index}
                  className={`relative p-6 sm:p-8 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 ${
                    advantage.isSpecial
                      ? "bg-white dark:bg-gray-800 border-2 border-primary"
                      : "bg-gradient-to-br from-primary to-primary/80 text-white"
                  }`}
                >
                  {advantage.isSpecial && (
                    <div className="w-12 h-12 sm:w-14 sm:h-14 bg-primary rounded-full flex items-center justify-center absolute -top-6 sm:-top-7 left-1/2 transform -translate-x-1/2">
                      <Icon className="w-6 h-6 sm:w-7 sm:h-7 text-white" />
                    </div>
                  )}

                  {!advantage.isSpecial && (
                    <Icon className="w-8 h-8 sm:w-10 sm:h-10 mb-4 text-white" />
                  )}

                  <h3 className={`text-lg sm:text-xl lg:text-2xl font-semibold mb-3 sm:mb-4 ${
                    advantage.isSpecial
                      ? "text-foreground mt-4 sm:mt-6"
                      : "text-white"
                  }`}>
                    {advantage.title}
                  </h3>

                  <p className={`text-sm sm:text-base leading-relaxed ${
                    advantage.isSpecial
                      ? "text-secondary text-center"
                      : "text-blue-100"
                  }`}>
                    {advantage.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>
    </div>
  );
}
