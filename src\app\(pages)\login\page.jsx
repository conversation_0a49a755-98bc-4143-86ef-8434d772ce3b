"use client";
import React, { useState } from "react";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";
import { useLoginMutation, useGoogleLoginMutation } from "@/store/authApi";
import { setCredentials } from "@/store/authSlice";
import { useDispatch } from "react-redux";
import { ButtonLoader } from "@/components/common/UniversalLoader";
import { GoogleLogin } from "@react-oauth/google";
import { resendVerificationEmail } from "@/services/anyUserDataChange";
// import { signIn, getSession } from "next-auth/react";
// import { GoogleLogin } from "@react-oauth/google";
import Link from "next/link";
import {
  User,
  Lock,
  Eye,
  EyeOff,
  BookOpen,
  GraduationCap,
  AlertCircle,
} from "lucide-react";
export default function Login() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [emailNotVerified, setEmailNotVerified] = useState(false);
  const [userEmail, setUserEmail] = useState("");
  const [isResending, setIsResending] = useState(false);
  const [loginAttempts, setLoginAttempts] = useState(0);
  const [isLocked, setIsLocked] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();
  const dispatch = useDispatch();
  const [login] = useLoginMutation();

  const [googleLogin] = useGoogleLoginMutation();

  const initialValues = {
    username: "",
    password: "",
  };

  const validate = (values) => {
    const errors = {};
    if (!values.username.trim()) {
      errors.username = "اسم المستخدم مطلوب";
    }
    if (!values.password) {
      errors.password = "كلمة المرور مطلوبة";
    }
    return errors;
  };

  const onSubmit = async (values) => {
    if (isLocked) {
      setError(
        "تم تجاوز عدد المحاولات المسموح بها. يرجى المحاولة بعد 5 دقائق."
      );
      return;
    }
    try {
      setIsLoading(true);
      setError(null);
      setEmailNotVerified(false);
      const response = await login(values).unwrap();
      if (!response || !response.token) {
        throw new Error("فشل تسجيل الدخول: لم يتم استلام بيانات صالحة");
      }
      dispatch(setCredentials(response));
      // توجيه المستخدم حسب نوعه
      const user = response.user;
      router.push("/");
    } catch (err) {
      // التحقق من حالة عدم تفعيل البريد الإلكتروني
      if (err.data?.email_not_verified) {
        setEmailNotVerified(true);
        setUserEmail(values.username); // استخدام username كـ email
        setError(err.data?.error || "يجب تفعيل البريد الإلكتروني أولاً");
      } else {
        setLoginAttempts((prev) => prev + 1);
        if (loginAttempts >= 3) {
          setIsLocked(true);
          setTimeout(() => {
            setIsLocked(false);
            setLoginAttempts(0);
          }, 5 * 60 * 1000);
        }
        const errorMessage =
          err.data?.error ||
          err.data?.message ||
          err.data?.detail ||
          err.error?.data?.message ||
          err.error?.data?.detail ||
          err.message ||
          "فشل تسجيل الدخول. يرجى المحاولة مرة أخرى.";
        setError(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendVerification = async () => {
    if (!userEmail) return;

    setIsResending(true);
    try {
      await resendVerificationEmail(userEmail);
      setError(
        "تم إعادة إرسال رابط التفعيل إلى بريدك الإلكتروني. يرجى التحقق من بريدك."
      );
      setEmailNotVerified(false);
    } catch (err) {
      setError(
        err.response?.data?.error || "حدث خطأ أثناء إعادة إرسال رابط التفعيل."
      );
    } finally {
      setIsResending(false);
    }
  };

  const formik = useFormik({
    initialValues,
    validate,
    onSubmit,
  });

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-indigo-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-indigo-400/20 to-purple-400/20 rounded-full blur-3xl animate-float"></div>
        <div
          className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl animate-float"
          style={{ animationDelay: "2s" }}
        ></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-3xl animate-pulse-slow"></div>
      </div>

      <div className="max-w-md w-full space-y-8 relative z-10">
        {/* Logo and Header */}
        <div className="text-center animate-fade-in">
          <div className="mx-auto h-16 w-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <GraduationCap className="h-8 w-8 text-white" />
          </div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            مرحباً بك مرة أخرى
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            سجل دخولك للوصول إلى منصة مُعَلِّمِيّ التعليمية
          </p>
        </div>

        {/* Login Form */}
        <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-white/20 dark:border-gray-700/50 animate-slide-up">
          <form className="space-y-6" onSubmit={formik.handleSubmit}>
            {/* Username Field */}
            <div className="space-y-2">
              <label
                htmlFor="username"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                اسم المستخدم
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <User className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                </div>
                <input
                  id="username"
                  name="username"
                  type="text"
                  required
                  className="block w-full pr-10 pl-3 py-3 border border-gray-300 dark:border-gray-600 rounded-xl placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500"
                  placeholder="أدخل اسم المستخدم"
                  onChange={formik.handleChange}
                  value={formik.values.username}
                />
              </div>
              {formik.errors.username && (
                <p className="text-sm text-red-500 flex items-center gap-1 animate-slide-in-right">
                  <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                  {formik.errors.username}
                </p>
              )}
            </div>

            {/* Password Field */}
            <div className="space-y-2">
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                كلمة المرور
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  required
                  className="block w-full pr-10 pl-12 py-3 border border-gray-300 dark:border-gray-600 rounded-xl placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500"
                  placeholder="أدخل كلمة المرور"
                  onChange={formik.handleChange}
                  value={formik.values.password}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 left-0 pl-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors" />
                  )}
                </button>
              </div>
              {formik.errors.password && (
                <p className="text-sm text-red-500 flex items-center gap-1 animate-slide-in-right">
                  <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                  {formik.errors.password}
                </p>
              )}
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4 animate-slide-in-left">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <AlertCircle className="h-5 w-5 text-red-500" />
                  </div>
                  <div className="mr-3 flex-1">
                    <p className="text-sm text-red-700 dark:text-red-300">
                      {error}
                    </p>
                    {emailNotVerified && userEmail && (
                      <div className="mt-3">
                        <button
                          type="button"
                          onClick={handleResendVerification}
                          disabled={isResending}
                          className="text-sm bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
                        >
                          {isResending
                            ? "جاري الإرسال..."
                            : "إعادة إرسال رابط التفعيل"}
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Login Button */}
            <button
              type="submit"
              disabled={isLoading || isLocked}
              className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.02] hover:shadow-lg"
            >
              <span className="absolute left-0 inset-y-0 flex items-center pl-3">
                {!isLoading && (
                  <BookOpen className="h-5 w-5 text-indigo-300 group-hover:text-indigo-200 transition-colors" />
                )}
              </span>
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <ButtonLoader size="small" />
                  <span>جاري تسجيل الدخول...</span>
                </div>
              ) : (
                "تسجيل الدخول"
              )}
            </button>

            {/* Divider */}
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300 dark:border-gray-600"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">
                  أو
                </span>
              </div>
            </div>

            {/* Google Login */}
            <div className="w-full">
              <GoogleLogin
                onSuccess={async (credentialResponse) => {
                  try {
                    const result = await googleLogin({
                      id_token: credentialResponse.credential,
                    });

                    console.log("Result:", result);
                    console.log("Google login successful", credentialResponse);
                    console.log("Google login response:", result.data.user);

                    if (result.data.user.is_instructor == true) {
                      dispatch(setCredentials(result.data));
                      router.push("/instructor/dashboard");
                      return;
                    } else {
                      // لو مش مدرب، روح على صفحة التسجيل كمدرب
                      router.push("/areYouInstructor");
                    }
                    // ✅ لو كل حاجة تمام، روح على الـ dashboard أو الرئيسية
                  } catch (err) {
                    console.error("Google login error:", err);
                  }
                }}
                onError={() => {
                  console.log("Login Failed");
                }}
                theme="outline"
                size="large"
                width="100%"
                text="signin_with"
                shape="rectangular"
              />
            </div>

            {/* Forgot Password Link */}
            <div className="text-center">
              <Link
                href="/forgot-password"
                className="text-sm text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300 transition-colors duration-200 hover:underline"
              >
                نسيت كلمة المرور؟
              </Link>
            </div>
          </form>
        </div>

        {/* Footer */}
        <div
          className="text-center animate-fade-in"
          style={{ animationDelay: "0.3s" }}
        >
          <p className="text-sm text-gray-600 dark:text-gray-400">
            ليس لديك حساب؟{" "}
            <Link
              href="/signup"
              className="font-medium text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300 transition-colors duration-200"
            >
              إنشاء حساب جديد
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
