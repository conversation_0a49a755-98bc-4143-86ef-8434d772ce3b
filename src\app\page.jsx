"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import axios from "axios";
import Link from "next/link";
import Cookies from "js-cookie";
import HeroSection from "./_Components/(MainpageComponents)/HeroSection";
import FeaturesSection from "./_Components/(MainpageComponents)/FeaturesSection";
import HowItWorksSection from "./_Components/(MainpageComponents)/HowItWorksSection";
import FeaturedCoursesSection from "./_Components/(MainpageComponents)/FeaturedCoursesSection";
import InstructorsSection from "./_Components/(MainpageComponents)/InstructorsSection";
import TestimonialsSection from "./_Components/(MainpageComponents)/TestimonialsSection";
import FinalCTASection from "./_Components/(MainpageComponents)/FinalCTASection";
import Footer from "./_Components/(MainpageComponents)/Footer";
// import Teacheradvantage from "./_Components/(MainpageComponents)/Teacheradvantage";
import { API_BASE_URL } from "../config/api";
import { SectionLoader } from "@/components/common/UniversalLoader";

export default function Home() {
  const [searchQuery, setSearchQuery] = useState("");
  const [instructors, setInstructors] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const searchInstructors = async (query) => {
    if (!query.trim()) {
      setInstructors([]);
      return;
    }

    try {
      setLoading(true);
      const token = Cookies.get("authToken");

      const response = await axios.get(
        `${API_BASE_URL}/api/users/search-instructors/?q=${query}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      setInstructors(response.data.results || response.data.users || []);
    } catch (error) {
      console.error("Error searching instructors:", error);
      setInstructors([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      searchInstructors(searchQuery);
    }, 300);

    return () => clearTimeout(delayDebounceFn);
  }, [searchQuery]);

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <HeroSection />

      {/* Features Section */}
      <FeaturesSection />

      {/* How It Works Section */}
      <HowItWorksSection />

      {/* Featured Courses Section */}
      <FeaturedCoursesSection />

      {/* Instructors Section */}
      <InstructorsSection />

      {/* Teacher Advantages (keeping the original component) */}
      {/* <div className="py-20">
        <Teacheradvantage />
      </div> */}

      {/* Testimonials Section */}
      <TestimonialsSection />

      {/* Final CTA Section */}
      <FinalCTASection />

      {/* Footer */}
      <Footer />

      {/* Search Modal - Hidden by default, can be toggled */}
    </div>
  );
}
