"use client";
import React, { useState } from "react";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";

import { useSelector } from "react-redux";
import { selectCurrentToken, selectCurrentUser } from "@/store/authSlice";
import { userDataChange } from "../../services/anyUserDataChange";
import { ButtonLoader, PageLoader } from "@/components/common/UniversalLoader";

export default function Signup() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const router = useRouter();
  const token = useSelector(selectCurrentToken);
  const user = useSelector(selectCurrentUser);
  const initialValues = {
    phone_number: "",
    is_instructor: null,
  };

  const validate = (values) => {
    const errors = {};
    const regex = {
      phone: /^01[0-2,5]{1}[0-9]{8}$/,
    };
    if (!values.phone_number) {
      errors.phone_number = "رقم الهاتف مطلوب";
    } else if (!regex.phone.test(values.phone_number)) {
      errors.phone_number = "رقم هاتف مصري غير صحيح";
    }
    if (formik.values.is_instructor === null) {
      setError("يرجى تحديد ما إذا كنت مدربًا أم لا");
      setTimeout(() => {
        setError("");
      }, 5000);
    }
    return errors;
  };

  const onSubmit = async (values) => {
    try {
      setIsLoading(true);
      setError(null);

      const updatedFields = {
        phone_number: values.phone_number,
        is_instructor: values.is_instructor,
      };

      await userDataChange(user.id, token, updatedFields);
      setSuccess(true);
      router.push("/"); // أو أي صفحة مناسبة
    } catch (error) {
      console.error("خطأ أثناء تعديل بيانات المستخدم:", error);
      setError("حدث خطأ أثناء حفظ البيانات.");
    } finally {
      setIsLoading(false);
    }
  };

  const formik = useFormik({
    initialValues,
    validate,
    onSubmit,
  });

  const formFields = [
    {
      id: "phone_number",
      label: "رقم الهاتف",
      type: "tel",
      placeholder: "أدخل رقم الهاتف",
    },
  ];

  if (isLoading) {
    return <PageLoader text="جاري المعالجة..." />;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-indigo-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-indigo-400/20 to-purple-400/20 rounded-full blur-3xl animate-float"></div>
        <div
          className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl animate-float"
          style={{ animationDelay: "2s" }}
        ></div>
      </div>

      <div className="w-full max-w-md space-y-8 relative z-10">
        {/* Page Header */}
        <div className="text-center animate-fade-in">
          <div className="mx-auto h-16 w-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <svg
              className="w-8 h-8 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
          </div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            إكمال الملف الشخصي
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            يرجى إكمال بياناتك لإنهاء عملية التسجيل
          </p>
        </div>

        {/* Error Alert */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4 animate-slide-in-left">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5 text-red-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div className="mr-3 flex-1">
                <p className="text-sm text-red-700 dark:text-red-300">
                  {error}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Success Alert */}
        {success && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-4 animate-slide-in-left">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5 text-green-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <div className="mr-3 flex-1">
                <p className="text-sm text-green-700 dark:text-green-300">
                  تم حفظ البيانات بنجاح! جاري التوجيه...
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Form Container */}
        <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-white/20 dark:border-gray-700/50 animate-slide-up">
          <form className="space-y-6" onSubmit={formik.handleSubmit}>
            {/* Phone Number Field */}
            {formFields.map((field) => (
              <div key={field.id} className="space-y-2">
                <label
                  htmlFor={field.id}
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  {field.label}
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <svg
                      className="h-5 w-5 text-gray-400 dark:text-gray-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                      />
                    </svg>
                  </div>
                  <input
                    id={field.id}
                    name={field.id}
                    type={field.type}
                    required
                    placeholder={field.placeholder}
                    className={`block w-full pr-10 pl-3 py-3 border rounded-xl placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500 ${
                      formik.errors[field.id] && formik.touched[field.id]
                        ? "border-red-500"
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values[field.id]}
                  />
                </div>
                {formik.errors[field.id] && formik.touched[field.id] && (
                  <p className="text-sm text-red-500 flex items-center gap-1 animate-slide-in-right">
                    <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                    {formik.errors[field.id]}
                  </p>
                )}
              </div>
            ))}

            {/* Instructor Question */}
            <div className="space-y-4">
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  هل أنت مدرب؟
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  اختر الخيار المناسب لك <span className="text-red-500">*</span>
                </p>
              </div>

              <div className="flex justify-center gap-8">
                {/* Yes Option */}
                <div className="flex items-center">
                  <input
                    id="is_instructor_yes"
                    name="is_instructor"
                    type="radio"
                    value="true"
                    onChange={() => formik.setFieldValue("is_instructor", true)}
                    checked={formik.values.is_instructor === true}
                    className="h-4 w-4 text-indigo-600 border-gray-300 dark:border-gray-600 focus:ring-indigo-500"
                  />
                  <label
                    htmlFor="is_instructor_yes"
                    className="mr-3 text-sm font-medium text-gray-900 dark:text-gray-300 cursor-pointer"
                  >
                    نعم، أنا مدرب
                  </label>
                </div>

                {/* No Option */}
                <div className="flex items-center">
                  <input
                    id="is_instructor_no"
                    name="is_instructor"
                    type="radio"
                    value="false"
                    onChange={() =>
                      formik.setFieldValue("is_instructor", false)
                    }
                    checked={formik.values.is_instructor === false}
                    className="h-4 w-4 text-indigo-600 border-gray-300 dark:border-gray-600 focus:ring-indigo-500"
                  />
                  <label
                    htmlFor="is_instructor_no"
                    className="mr-3 text-sm font-medium text-gray-900 dark:text-gray-300 cursor-pointer"
                  >
                    لا، أنا طالب
                  </label>
                </div>
              </div>

              {formik.touched.is_instructor && formik.errors.is_instructor && (
                <p className="text-sm text-red-500 text-center flex items-center justify-center gap-1 animate-slide-in-right">
                  <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                  {formik.errors.is_instructor}
                </p>
              )}
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.02] hover:shadow-lg"
            >
              <span className="absolute left-0 inset-y-0 flex items-center pl-3">
                {!isLoading && (
                  <svg
                    className="h-5 w-5 text-indigo-300 group-hover:text-indigo-200 transition-colors"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    />
                  </svg>
                )}
              </span>
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <ButtonLoader size="small" />
                  <span>جاري المعالجة...</span>
                </div>
              ) : (
                "إكمال التسجيل"
              )}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}
