"use client";
import React, { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useSelector, useDispatch } from "react-redux";
import {
  selectCurrentUser,
  selectIsAuthenticated,
  logout,
} from "../../../store/authSlice";
import NotificationIcon from "./NotificationIcon";
import AdvancedFeaturesMenu from "./AdvancedFeaturesMenu"; // قائمة الميزات المتقدمة - zaki alkholy
import ThemeToggle from "@/components/common/ThemeToggle"; // مكون تبديل الثيم - zaki alkholy

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const router = useRouter();
  const dispatch = useDispatch();
  const user = useSelector(selectCurrentUser);
  const isAuthenticated = useSelector(selectIsAuthenticated);

  // مراقبة تحديث بيانات المستخدم - zaki alkholy
  useEffect(() => {
    console.log("Navbar user data updated:", user);
  }, [user]);

  useEffect(() => {
    setIsMounted(true);
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleLogout = () => {
    dispatch(logout());
    router.push("/login");
  };

  if (!isMounted) {
    return null;
  }

  return (
    <nav
      className={`fixed w-full z-50 transition-all duration-300 h-[70px] ${
        isScrolled ? "bg-white dark:bg-gray-900 shadow-md" : "bg-transparent"
      }`}
    >
      <div className="max-w-screen-xl flex flex-wrap items-center justify-between mx-auto p-4">
        <Link
          href="/"
          className="flex items-center space-x-3 rtl:space-x-reverse"
        >
          <h1 className="self-center text-3xl font-semibold whitespace-nowrap dark:text-white">
            مُعَلِّمِيّ
          </h1>
        </Link>
        <div className="flex md:order-2 space-x-3 md:space-x-0 rtl:space-x-reverse">
          {isAuthenticated && user ? (
            <div className="flex items-center gap-4">
              {/* صورة واسم المستخدم - ديناميكي */}
              <Link
                href={
                  user.is_instructor
                    ? "/instructor/dashboard/profile"
                    : `/student/${user.id}`
                }
                className="flex items-center gap-2 text-gray-700 hover:text-primary"
              >
                <img
                  src={user.profile_image || "/images/default-course.jpg"}
                  alt={user.first_name || "User"}
                  className="w-9 h-9 rounded-full object-cover border border-gray-300"
                  key={`${user.profile_image || "default"}-${user.id}`} // إضافة key لضمان إعادة التحميل - zaki alkholy
                />
                <span>
                  {user.first_name || ""} {user.last_name || ""}
                </span>
              </Link>
              {/* مكون تبديل الثيم - zaki alkholy */}
              <ThemeToggle size="small" showLabel={false} />
              <NotificationIcon
                onClick={() =>
                  router.push("/instructor/dashboard/notifications")
                }
              />
              {/* قائمة الميزات المتقدمة الجديدة - zaki alkholy */}
              <AdvancedFeaturesMenu />
              {/* زر تسجيل الخروج */}
              {/* <button
                onClick={handleLogout}
                type="button"
                className="text-white bg-primary hover:bg-primary/90 focus:ring-4 focus:outline-none focus:ring-primary/50 font-medium rounded-lg text-sm px-4 py-2 text-center"
              >
                تسجيل الخروج
              </button> */}
              <button
                className="bg-white text-center  rounded-2xl h-10 relative text-black text-xl font-semibold group"
                type="button"
                onClick={handleLogout}
              >
                <div className="bg-green-400 rounded-xl h-8 w-1/4 flex items-center justify-center absolute left-1 top-[4px] group-hover:w-[135px] z-10 duration-500">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 1024 1024"
                    height="25px"
                    width="25px"
                  >
                    <path
                      d="M224 480h640a32 32 0 1 1 0 64H224a32 32 0 0 1 0-64z"
                      fill="#000000"
                    />
                    <path
                      d="m237.248 512 265.408 265.344a32 32 0 0 1-45.312 45.312l-288-288a32 32 0 0 1 0-45.312l288-288a32 32 0 1 1 45.312 45.312L237.248 512z"
                      fill="#000000"
                    />
                  </svg>
                </div>
                <p className="me-[40px]">تسجيل الخروج</p>
              </button>
            </div>
          ) : (
            <div className="flex gap-4 items-center">
              {/* مكون تبديل الثيم للمستخدمين غير المسجلين - zaki alkholy */}
              <ThemeToggle size="small" showLabel={false} />
              <Link
                href="/login"
                className="text-white bg-primary hover:bg-primary/90 focus:ring-4 focus:outline-none focus:ring-primary/50 font-medium rounded-lg text-sm px-4 py-2 text-center"
              >
                تسجيل الدخول
              </Link>
              <Link
                href="/signup"
                className="text-primary bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-primary/50 font-medium rounded-lg text-sm px-4 py-2 text-center border border-primary"
              >
                إنشاء حساب
              </Link>
            </div>
          )}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            type="button"
            className="inline-flex items-center p-2 w-10 h-10 justify-center text-sm text-gray-500 rounded-lg md:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600"
          >
            <span className="sr-only">فتح القائمة الرئيسية</span>
            <svg
              className="w-5 h-5"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 17 14"
            >
              <path
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M1 1h15M1 7h15M1 13h15"
              />
            </svg>
          </button>
        </div>
        <div
          className={`items-center justify-between w-full md:flex md:w-auto md:order-1 ${
            isMenuOpen ? "block" : "hidden"
          }`}
        >
          <ul className="flex flex-col p-4 md:p-0 mt-4 font-medium border border-gray-100 rounded-lg bg-gray-50 md:space-x-8 rtl:space-x-reverse md:flex-row md:mt-0 md:border-0 md:bg-transparent dark:bg-gray-800 md:dark:bg-transparent dark:border-gray-700">
            {/* روابط ديناميكية حسب نوع المستخدم */}
            <li>
              <Link
                href="/"
                className="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-primary md:p-0 md:dark:hover:text-primary dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700"
              >
                الرئيسية
              </Link>
            </li>
            {user?.is_instructor && (
              <li>
                <Link
                  href="/instructor/dashboard"
                  className="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-primary md:p-0 md:dark:hover:text-primary dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700"
                >
                  لوحة التحكم
                </Link>
              </li>
            )}
            {/* روابط عامة */}
            <li>
              <Link
                href="/about"
                className="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-primary md:p-0 md:dark:hover:text-primary dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700"
              >
                من نحن
              </Link>
            </li>
            <li>
              <Link
                href="/contact"
                className="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-primary md:p-0 md:dark:hover:text-primary dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700"
              >
                اتصل بنا
              </Link>
            </li>
          </ul>
        </div>
      </div>
    </nav>
  );
}
