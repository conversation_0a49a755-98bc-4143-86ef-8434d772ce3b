import axios from "axios";
import { API_BASE_URL } from '../config/api';

//========================================= جلب ال dashboard
export async function fetchInstructorDashboardStats(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
  };

  const response = await axios.get(
    `${API_BASE_URL}/api/instructor/dashboard/`,
    { headers }
  );

  return response.data;
}
// =========================================== Date Table
export async function fetchSessionsByDate(dateString, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
  };

  const response = await axios.get(
    `${API_BASE_URL}/api/availabilities/?date=${dateString}`,
    { headers }
  );

  return response.data;
}

// =========================================== Todo List
// ================Get
export async function fetchInstructorTasks(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
  };

  const response = await axios.get(`${API_BASE_URL}/api/instructor-tasks/`, {
    headers,
  });

  return response.data;
}
// =====================Patch ToDo
export async function toggleInstructorTaskDone(id, done, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
  };

  const data = { done };

  const response = await axios.patch(
    `${API_BASE_URL}/api/instructor-tasks/${id}/`,
    data,
    { headers }
  );

  return response.data;
}
// ================================Post ToDo
export async function createInstructorTask(data, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };

  const response = await axios.post(
    `${API_BASE_URL}/api/instructor-tasks/`,
    data,
    { headers }
  );

  return response.data;
}
// ====================================Delete Todo
export async function deleteInstructorTask(id, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
  };

  const response = await axios.delete(
    `${API_BASE_URL}/api/instructor-tasks/${id}/`,
    { headers }
  );

  return response;
}