// خدمات الطالب: جميع استدعاءات API الخاصة بالطالب هنا - zaki alkholy
import axios from "axios";
import { API_BASE_URL } from "../config/api";

// ===============================
// خدمات الميزات المتقدمة للطلاب - zaki alkholy
// ===============================

/**
 * الحصول على لوحة تحكم الطالب الشاملة - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @returns {Promise} بيانات لوحة التحكم
 */
export async function fetchStudentDashboard(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };

  const response = await axios.get(`${API_BASE_URL}/api/student/dashboard/`, {
    headers,
    withCredentials: true,
  });

  return response.data;
}

/**
 * الحصول على نقاط الطالب وإحصائياته - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @returns {Promise} بيانات النقاط
 */
export async function fetchStudentPoints(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };

  const response = await axios.get(`${API_BASE_URL}/api/student/points/`, {
    headers,
    withCredentials: true,
  });

  return response.data;
}

/**
 * الحصول على إنجازات الطالب - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @returns {Promise} بيانات الإنجازات
 */
export async function fetchStudentAchievements(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };

  const response = await axios.get(
    `${API_BASE_URL}/api/student/achievements/`,
    {
      headers,
      withCredentials: true,
    }
  );

  return response.data;
}

/**
 * الحصول على سلسلة التعلم للطالب - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @returns {Promise} بيانات سلسلة التعلم
 */
export async function fetchStudentLearningStreak(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };

  const response = await axios.get(
    `${API_BASE_URL}/api/student/learning-streak/`,
    {
      headers,
      withCredentials: true,
    }
  );

  return response.data;
}

/**
 * الحصول على مقارنة أداء الطالب - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @returns {Promise} بيانات المقارنة
 */
export async function fetchStudentComparison(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };

  const response = await axios.get(`${API_BASE_URL}/api/student/comparison/`, {
    headers,
    withCredentials: true,
  });

  return response.data;
}

/**
 * الحصول على تاريخ النقاط - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {number} page - رقم الصفحة
 * @param {number} pageSize - حجم الصفحة
 * @returns {Promise} تاريخ النقاط
 */
export async function fetchPointsHistory(token, page = 1, pageSize = 10) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };

  const response = await axios.get(
    `${API_BASE_URL}/api/student/points/history/`,
    {
      headers,
      withCredentials: true,
      params: { page, page_size: pageSize },
    }
  );

  return response.data;
}

// ===============================
// خدمات المراجعة المتباعدة - zaki alkholy
// ===============================

/**
 * الحصول على المراجعة اليومية - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @returns {Promise} بيانات المراجعة اليومية
 */
export async function fetchDailyReview(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };

  const response = await axios.get(
    `${API_BASE_URL}/api/student/review/daily/`,
    {
      headers,
      withCredentials: true,
    }
  );

  return response.data;
}

/**
 * الحصول على إحصائيات المراجعة - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @returns {Promise} إحصائيات المراجعة
 */
export async function fetchReviewStats(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };

  const response = await axios.get(
    `${API_BASE_URL}/api/student/review/stats/`,
    {
      headers,
      withCredentials: true,
    }
  );

  return response.data;
}

/**
 * الحصول على توصيات المراجعة - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @returns {Promise} توصيات المراجعة
 */
export async function fetchReviewRecommendations(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };

  const response = await axios.get(
    `${API_BASE_URL}/api/student/review/recommendations/`,
    {
      headers,
      withCredentials: true,
    }
  );

  return response.data;
}

// ===============================
// خدمات متجر النقاط - zaki alkholy
// ===============================

/**
 * الحصول على بيانات متجر النقاط - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @returns {Promise} بيانات المتجر
 */
export async function fetchPointsStore(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };

  const response = await axios.get(`${API_BASE_URL}/api/points-store/`, {
    headers,
    withCredentials: true,
  });

  return response.data;
}

/**
 * استبدال مكافأة من متجر النقاط - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} rewardId - معرف المكافأة
 * @returns {Promise} نتيجة الاستبدال
 */
export async function redeemReward(token, rewardId) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };

  const response = await axios.post(
    `${API_BASE_URL}/api/points-store/redeem/`,
    {
      reward_id: rewardId,
    },
    {
      headers,
      withCredentials: true,
    }
  );

  return response.data;
}

/**
 * الحصول على لوحة المتصدرين الأسبوعية - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @returns {Promise} بيانات لوحة المتصدرين
 */
export async function fetchWeeklyLeaderboard(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };

  const response = await axios.get(`${API_BASE_URL}/api/leaderboard/weekly/`, {
    headers,
    withCredentials: true,
  });

  return response.data;
}

// ===============================
// خدمات الدورات والدروس الأساسية - zaki alkholy
// ===============================

export async function fetchStudentCourse(courseId, token, signal) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const response = await axios.get(`${API_BASE_URL}/api/courses/${courseId}/`, {
    headers,
    withCredentials: true,
    signal,
  });
  return response.data;
}

export async function fetchCourseLessons(courseId, token, signal) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const response = await axios.get(
    `${API_BASE_URL}/api/courses/${courseId}/lessons/`,
    {
      headers,
      withCredentials: true,
      signal,
    }
  );
  return response.data.results || response.data || [];
}

export async function fetchCourseReviews(courseId) {
  const response = await axios.get(
    `${API_BASE_URL}/api/reviews/?course=${courseId}`
  );
  return response.data;
}

export async function submitCourseReview(courseId, data, token) {
  await axios.post(
    `${API_BASE_URL}/api/reviews/`,
    {
      course: courseId,
      rating: Number(data.rating),
      comment: data.comment,
    },
    {
      headers: { Authorization: `Bearer ${token}` },
    }
  );
}

export async function fetchReviewComments(reviewId) {
  const res = await axios.get(
    `${API_BASE_URL}/api/reviews/${reviewId}/comments/`
  );
  return res.data;
}

export async function toggleCourseLike(courseId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const res = await axios.post(
    `${API_BASE_URL}/api/courses/${courseId}/toggle_like/`,
    {},
    { headers }
  );
  return res.data;
}

export async function toggleCommentLike(commentId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const res = await axios.post(
    `${API_BASE_URL}/api/comments/${commentId}/toggle_like/`,
    {},
    { headers }
  );
  return res.data;
}

export async function addReviewReply(reviewId, parentId, replyText, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  await axios.post(
    `${API_BASE_URL}/api/reviews/${reviewId}/add_comment/`,
    { text: replyText, parent: parentId },
    { headers }
  );
}

export async function fetchStudentProfile(studentId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const response = await axios.get(
    `${API_BASE_URL}/api/users/list-all/?id=${studentId}`,
    { headers }
  );
  return response.data.users?.find((user) => user.id === studentId);
}

export async function fetchStudentCourses(studentId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const response = await axios.get(
    `${API_BASE_URL}/api/courses/?students=${studentId}`,
    { headers }
  );
  // الـ Backend بيرجع الكورسات مفلترة ومميزة بالفعل
  return response.data;
}

// جلب الوقت المتبقي للامتحان للطالب - zaki alkholy
export async function fetchExamTimeLeft(quizId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const res = await axios.get(
    `${API_BASE_URL}/api/quizzes/${quizId}/get_time_left/`,
    { headers }
  );
  return res.data;
}

// جلب حالة الامتحان ونتيجته للطالب
export async function fetchExamStatus(quizId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const res = await axios.get(
    `${API_BASE_URL}/api/quizzes/${quizId}/exam_status/`,
    { headers }
  );
  return res.data;
}

// حفظ إجابة سؤال في الامتحان
export async function saveExamAnswer(quizId, questionId, answer, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const body = {
    question_id: questionId,
    answer_id: answer, // يجب أن يكون answer_id وليس answer
  };
  // تعديل: استخدام PATCH بدلاً من POST
  const res = await axios.patch(
    `${API_BASE_URL}/api/quizzes/${quizId}/save_answer/`,
    body,
    { headers }
  );
  return res.data;
}

// بدء الامتحان - zaki alkholy
export async function startExam(quizId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const res = await axios.post(
    `${API_BASE_URL}/api/quizzes/${quizId}/start/`,
    {},
    { headers }
  );
  return res.data;
}

// فحص انتهاء الوقت والتسليم التلقائي - zaki alkholy
export async function checkTimeAndAutoSubmit(quizId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const res = await axios.post(
    `${API_BASE_URL}/api/quizzes/${quizId}/check_time_and_auto_submit/`,
    {},
    { headers }
  );
  return res.data;
}

// تسليم الامتحان
export async function submitExam(quizId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const res = await axios.post(
    `${API_BASE_URL}/api/quizzes/${quizId}/submit/`,
    {},
    { headers }
  );
  return res.data;
}
