"use client";
import React, { useState, useRef, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import Cookies from "js-cookie";
import axios from "axios";
import { API_BASE_URL } from "../../../../../../config/api";

export default function AddLesson() {
  const theUrl = "https://res.cloudinary.com/djwhgnwp5/";
  const params = useParams();
  const router = useRouter();
  const courseId = params.id;
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [formData, setFormData] = useState({
    title: "",
    order: 0,
    lessonType: "video",
    content: "",
    isPreview: false,
    duration: 0,
    course: "", // سيتم تعيينه بعد جلب بيانات الكورس - zaki alkholy
  });
  const [courseData, setCourseData] = useState(null);
  const [videoFile, setVideoFile] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const cancelUploadRef = useRef(null);
  const [reservedOrders, setReservedOrders] = useState([]);
  useEffect(() => {
    const fetchCourseAndLessons = async () => {
      try {
        const token = Cookies.get("authToken");

        // جلب بيانات الكورس أولاً - zaki alkholy
        const courseResponse = await axios.get(
          `${API_BASE_URL}/api/courses/${courseId}/`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        setCourseData(courseResponse.data);

        // تحديث formData بـ course ID
        setFormData((prev) => ({
          ...prev,
          course: courseResponse.data.id,
        }));

        // جلب الدروس
        const lessonsResponse = await axios.get(
          `${API_BASE_URL}/api/courses/${courseId}/lessons/`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const orders = lessonsResponse.data.map((lesson) => lesson.order);
        setReservedOrders(orders);
      } catch (error) {
        // خطأ أثناء جلب بيانات الكورس والدروس
      }
    };

    if (courseId) fetchCourseAndLessons();
  }, [courseId]);

  const validateForm = () => {
    if (formData.lessonType === "video" && !videoFile) {
      setError("الرجاء اختيار فيديو للدرس");
      return false;
    }
    if (!formData.title.trim()) {
      setError("الرجاء إدخال عنوان للدرس");
      return false;
    }
    if (!formData.content.trim()) {
      setError("الرجاء إدخال محتوى للدرس");
      return false;
    }
    return true;
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (name === "lessonType" && value !== "video") {
      setVideoFile(null);
      setPreviewUrl(null);
    }

    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };
  const handleBlur = (e) => {
    const { name, value } = e.target;

    if (name === "order") {
      const order = parseInt(value);
      if (!order || order < 1) {
        setError("ترتيب الدرس يجب أن يكون رقماً موجباً");
        return;
      }
      if (reservedOrders.includes(order)) {
        setError(`الترتيب ${order} محجوز بالفعل، اختر رقماً آخر`);
        return;
      }
      setError(""); // Clear error if valid
    }
  };

  const handleVideoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (!file.type.startsWith("video/")) {
        setError("الرجاء اختيار ملف فيديو صالح");
        return;
      }
      if (file.size > 500 * 1024 * 1024) {
        setError("حجم الفيديو يجب أن لا يتجاوز 500 ميجابايت");
        return;
      }
      setVideoFile(file);
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setSuccess("");
    setUploading(true);
    setLoading(true);
    cancelUploadRef.current = null;
    try {
      const token = Cookies.get("authToken");
      if (!token) {
        setError("يرجى تسجيل الدخول أولاً");
        router.push("/login");
        return;
      }
      if (!validateForm()) {
        setUploading(false);
        setLoading(false);
        return;
      }
      // Custom upload logic to support cancel
      // Prepare lesson data
      const lessonFormData = new FormData();
      lessonFormData.append("title", formData.title.trim());
      lessonFormData.append("content", formData.content.trim());
      lessonFormData.append("order", parseInt(formData.order) || 1);
      lessonFormData.append("lesson_type", formData.lessonType);
      lessonFormData.append("is_preview", formData.isPreview || false);
      lessonFormData.append("duration", parseInt(formData.duration) || 0);
      lessonFormData.append("is_drm_protected", "true");
      lessonFormData.append("is_hls_encrypted", "true");
      lessonFormData.append("token_expiry_hours", "24");
      lessonFormData.append("watermark_enabled", "true");
      lessonFormData.append("course", formData.course);

      // Create lesson
      const lessonResponse = await axios.post(
        `${API_BASE_URL}/api/lessons/`,
        lessonFormData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "multipart/form-data",
          },
        }
      );
      if (!lessonResponse.data) {
        throw new Error("لم يتم استلام تأكيد إنشاء الدرس من الخادم");
      }
      const lessonId = lessonResponse.data.id;
      // رفع الفيديو حسب النوع - نظام مختلط - زكي الخولي
      if (videoFile && formData.lessonType === "video") {
        const videoFormData = new FormData();
        videoFormData.append("video", videoFile);

        // تحديد نوع الفيديو: ترويجي أم مدفوع - زكي الخولي
        const videoType = formData.isPreview ? "promo" : "premium";
        videoFormData.append("video_type", videoType);

        const cancelTokenSource = axios.CancelToken.source();
        cancelUploadRef.current = cancelTokenSource.cancel;
        await axios.post(
          `${API_BASE_URL}/api/lessons/${lessonId}/upload_video/`,
          videoFormData,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "multipart/form-data",
            },
            onUploadProgress: (progressEvent) => {
              if (progressEvent.total) {
                const percentCompleted = Math.round(
                  (progressEvent.loaded * 100) / progressEvent.total
                );
                setUploadProgress(percentCompleted);
              }
            },
            cancelToken: cancelTokenSource.token,
          }
        );
      }
      setSuccess("تم إنشاء الدرس بنجاح");
      setFormData({
        title: "",
        content: "",
        lessonType: "video",
        isPreview: false,
        order: 1,
        duration: 0,
        course: courseId,
      });
      setVideoFile(null);
      setPreviewUrl("");
      setUploadProgress(0);
      setTimeout(() => {
        router.push(`/instructor/dashboard/${courseId}`);
      }, 2000);
    } catch (err) {
      if (axios.isCancel(err)) {
        setError("تم إلغاء رفع الفيديو.");
      } else if (err?.response?.status === 401) {
        setError("انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى");
        router.push("/login");
        return;
      } else {
        setError(
          err?.response?.data?.message ||
            err?.message ||
            "حدث خطأ أثناء إنشاء الدرس"
        );
      }
    } finally {
      setUploading(false);
      setLoading(false);
      cancelUploadRef.current = null;
    }
  };

  const handleCancelUpload = () => {
    if (cancelUploadRef.current) {
      cancelUploadRef.current();
    }
    setUploading(false);
    setUploadProgress(0);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b  from-white via-gray-50 to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header Section */}
        <div className="flex gap-4 items-center mb-12 animate-fade-in">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-primary to-accent rounded-full mb-6 shadow-lg">
            <i className="fas fa-plus-circle text-white text-2xl"></i>
          </div>
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent mb-4 font-arabic">
              إضافة درس جديد
            </h1>
            <p className="text-gray-600 dark:text-gray-400 text-lg max-w-2xl mx-auto">
              {courseData
                ? `إضافة درس جديد للدورة: ${courseData.title}`
                : "أنشئ درساً تعليمياً جديداً وأضفه للدورة"}
            </p>
          </div>
        </div>
        {/* Alert Messages */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-400 p-4 rounded-xl mb-6 flex items-center animate-slide-in-left">
            <i className="fas fa-exclamation-circle mr-3 text-lg"></i>
            {error}
          </div>
        )}
        {success && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 text-green-700 dark:text-green-400 p-4 rounded-xl mb-6 flex items-center animate-slide-in-left">
            <i className="fas fa-check-circle mr-3 text-lg"></i>
            تم إنشاء الدرس بنجاح! جاري التحويل...
          </div>
        )}

        {/* Main Form Container */}
        <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-gray-200/50 dark:border-gray-700/50 p-8 animate-slide-up">
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* معلومات الدرس الأساسية */}
            <div className="space-y-6">
              <div className="flex items-center space-x-4 space-x-reverse mb-6">
                <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-primary to-accent rounded-full">
                  <i className="fas fa-book-open text-white"></i>
                </div>
                <h3 className="text-xl font-bold text-gray-800 dark:text-white font-arabic">
                  معلومات الدرس الأساسية
                </h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                {/* عنوان الدرس */}
                <div className="md:col-span-3 space-y-2 animate-slide-in-right">
                  <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                    <i className="fas fa-heading text-primary mr-3 text-lg ml-2"></i>
                    عنوان الدرس
                  </label>
                  <div className="relative group">
                    <input
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      required
                      placeholder="أدخل عنوان واضح ووصفي للدرس"
                      className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md font-arabic"
                    />
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300">
                      <i className="fas fa-edit"></i>
                    </div>
                  </div>
                </div>

                {/* ترتيب الدرس */}
                <div className="space-y-2 animate-slide-in-left">
                  <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                    <i className="fas fa-sort-numeric-up text-primary mr-3 text-lg ml-2"></i>
                    ترتيب الدرس
                  </label>
                  <div className="relative group">
                    <input
                      type="number"
                      name="order"
                      value={formData.order}
                      onChange={handleInputChange}
                      onBlur={handleBlur}
                      required
                      min="1"
                      placeholder="1"
                      className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md font-arabic"
                    />
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300">
                      <i className="fas fa-hashtag"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* إعدادات الدرس */}
            <div className="space-y-6">
              <div className="flex items-center space-x-4 space-x-reverse mb-6">
                <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-accent to-primary rounded-full">
                  <i className="fas fa-cogs text-white ml-2"></i>
                </div>
                <h3 className="text-xl font-bold text-gray-800 dark:text-white font-arabic">
                  إعدادات الدرس
                </h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* نوع الدرس */}
                <div className="space-y-2 animate-slide-in-right">
                  <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                    <i className="fas fa-layer-group text-primary mr-3 text-lg ml-2"></i>
                    نوع الدرس
                  </label>
                  <div className="relative group">
                    <select
                      name="lessonType"
                      value={formData.lessonType}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md appearance-none font-arabic"
                    >
                      <option value="video">📹 فيديو</option>
                      <option value="article">📝 مقال</option>
                      <option value="quiz">❓ اختبار</option>
                    </select>
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300 pointer-events-none">
                      <i className="fas fa-chevron-down"></i>
                    </div>
                  </div>
                </div>

                {/* مدة الدرس */}
                <div className="space-y-2 animate-slide-in-left">
                  <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                    <i className="fas fa-clock text-primary mr-3 text-lg ml-2"></i>
                    مدة الدرس (بالدقائق)
                  </label>
                  <div className="relative group">
                    <input
                      type="number"
                      name="duration"
                      value={formData.duration}
                      onChange={handleInputChange}
                      required
                      min="0"
                      placeholder="مثال: 15"
                      className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md font-arabic"
                    />
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300">
                      <i className="fas fa-stopwatch"></i>
                    </div>
                  </div>
                </div>
              </div>

              {/* محتوى الدرس */}
              <div className="space-y-2 animate-slide-in-right">
                <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                  <i className="fas fa-file-alt text-primary mr-3 text-lg ml-2"></i>
                  محتوى الدرس
                </label>
                <div className="relative group">
                  <textarea
                    name="content"
                    value={formData.content}
                    onChange={handleInputChange}
                    required
                    rows="4"
                    placeholder="اكتب وصفاً تفصيلياً لمحتوى الدرس وما سيتعلمه الطلاب"
                    className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md resize-none font-arabic"
                  ></textarea>
                  <div className="absolute right-4 top-4 text-gray-400 group-focus-within:text-primary transition-colors duration-300">
                    <i className="fas fa-paragraph"></i>
                  </div>
                </div>
              </div>
            </div>
            {/* خيارات الدرس */}
            <div className="space-y-6">
              <div className="flex items-center space-x-4 space-x-reverse mb-6">
                <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full">
                  <i className="fas fa-toggle-on text-white ml-2"></i>
                </div>
                <h3 className="text-xl font-bold text-gray-800 dark:text-white font-arabic">
                  خيارات الدرس
                </h3>
              </div>

              {/* درس تجريبي */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 rounded-xl border border-blue-200 dark:border-blue-700 animate-slide-in-left">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="relative">
                    <input
                      type="checkbox"
                      name="isPreview"
                      checked={formData.isPreview}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <label
                      htmlFor="isPreview"
                      className={`flex items-center justify-center w-6 h-6 rounded-md border-2 cursor-pointer transition-all duration-300 ${
                        formData.isPreview
                          ? "bg-primary border-primary text-white"
                          : "bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 hover:border-primary"
                      }`}
                    >
                      {formData.isPreview && (
                        <i className="fas fa-check text-sm ml-2"></i>
                      )}
                    </label>
                  </div>
                  <div className="flex-1">
                    <label
                      htmlFor="isPreview"
                      className="text-lg font-semibold text-gray-800 dark:text-white cursor-pointer font-arabic"
                    >
                      درس تجريبي مجاني
                    </label>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      اجعل هذا الدرس متاحاً مجاناً للمعاينة قبل شراء الدورة
                    </p>
                  </div>
                  <div className="text-2xl">
                    <i
                      className={`fas ${
                        formData.isPreview
                          ? "fa-eye text-green-500"
                          : "fa-lock text-primary"
                      }`}
                    ></i>
                  </div>
                </div>
              </div>
            </div>

            {/* معلومات عن نوع الفيديو */}
            {formData.lessonType === "video" && (
              <div className="space-y-4 animate-slide-in-right">
                <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-6 rounded-xl border border-purple-200 dark:border-purple-700">
                  <div className="flex items-center space-x-4 space-x-reverse mb-4">
                    <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full">
                      <i className="fas fa-info text-white text-sm"></i>
                    </div>
                    <h4 className="text-lg font-semibold text-gray-800 dark:text-white font-arabic">
                      نوع الفيديو:{" "}
                      {formData.isPreview ? "ترويجي مجاني" : "محمي مدفوع"}
                    </h4>
                  </div>
                  <div className="bg-white/50 dark:bg-gray-800/50 p-4 rounded-lg">
                    {formData.isPreview ? (
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <i className="fas fa-cloud text-blue-500 text-xl"></i>
                        <div>
                          <p className="font-medium text-blue-800 dark:text-blue-400">
                            Cloudinary
                          </p>
                          <p className="text-sm text-blue-600 dark:text-blue-300">
                            فيديو ترويجي مجاني للمعاينة
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <i className="fas fa-shield-alt text-green-500 text-xl"></i>
                        <div>
                          <p className="font-medium text-green-800 dark:text-green-400">
                            Bunny Stream
                          </p>
                          <p className="text-sm text-green-600 dark:text-green-300">
                            فيديو محمي بتشفير كامل + watermark شخصي
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* رفع الفيديو */}
            <div className="space-y-6">
              <div className="flex items-center space-x-4 space-x-reverse mb-6">
                <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-full">
                  <i className="fas fa-video text-white"></i>
                </div>
                <h3 className="text-xl font-bold text-gray-800 dark:text-white font-arabic">
                  رفع فيديو الدرس
                </h3>
              </div>

              <div className="space-y-4 animate-slide-in-left">
                <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                  <i className="fas fa-upload text-primary mr-3 text-lg ml-2"></i>
                  اختر فيديو الدرس
                </label>
                <div className="relative group">
                  <input
                    type="file"
                    accept="video/*"
                    onChange={handleVideoChange}
                    className="w-full px-4 py-4 bg-white dark:bg-gray-700 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-400 dark:hover:border-gray-500 shadow-sm hover:shadow-md file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-primary file:text-white hover:file:bg-primary-dark file:transition-colors file:duration-300"
                  />
                  <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300 pointer-events-none">
                    <i className="fas fa-cloud-upload-alt"></i>
                  </div>
                </div>
                <p className="text-sm text-gray-500 dark:text-gray-400 text-center">
                  الحد الأقصى لحجم الفيديو: 500 ميجابايت | الصيغ المدعومة: MP4,
                  AVI, MOV
                </p>
              </div>
              {/* شريط التقدم */}
              {uploadProgress > 0 && (
                <div className="space-y-3 animate-pulse">
                  <div className="flex items-center justify-between text-sm font-medium text-gray-700 dark:text-gray-300">
                    <span className="flex items-center">
                      <i className="fas fa-upload mr-2 text-primary"></i>
                      جاري رفع الفيديو...
                    </span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
                    <div
                      className="bg-gradient-to-r from-primary to-accent h-3 rounded-full transition-all duration-300 ease-out"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                  {uploading && (
                    <div className="flex justify-center">
                      <button
                        type="button"
                        onClick={handleCancelUpload}
                        className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-xl transition-all duration-300 font-arabic"
                      >
                        <i className="fas fa-times text-sm"></i>
                        <span>إلغاء التحميل</span>
                      </button>
                    </div>
                  )}
                </div>
              )}
              {/* معاينة الفيديو */}
              {previewUrl && (
                <div className="space-y-4 animate-slide-in-right">
                  <h4 className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 font-arabic">
                    <i className="fas fa-eye text-primary mr-3 text-lg"></i>
                    معاينة الفيديو
                  </h4>
                  <div className="relative group">
                    <video
                      src={previewUrl}
                      controls
                      className="w-full rounded-xl shadow-lg group-hover:shadow-xl transition-all duration-300"
                      style={{ maxHeight: "300px" }}
                    />
                    <div className="absolute top-4 right-4 bg-black/50 text-white px-3 py-1 rounded-lg text-sm">
                      <i className="fas fa-play mr-2"></i>
                      معاينة
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* أزرار التحكم */}
            <div className="flex flex-col sm:flex-row justify-end space-y-4 sm:space-y-0 sm:space-x-4 sm:space-x-reverse pt-6 animate-slide-in-left">
              <button
                type="button"
                onClick={() => router.back()}
                className="flex items-center justify-center space-x-2 space-x-reverse px-6 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-300 font-arabic"
              >
                <i className="fas fa-arrow-left text-sm"></i>
                <span>إلغاء</span>
              </button>
              <button
                type="submit"
                disabled={loading || uploading}
                className="flex items-center justify-center space-x-3 space-x-reverse px-8 py-3 bg-gradient-to-r from-primary to-accent hover:from-primary-dark hover:to-accent-light text-white rounded-xl font-bold transition-all duration-300 transform hover:scale-[1.02] hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none font-arabic shadow-lg"
              >
                {loading ? (
                  <>
                    <i className="fas fa-spinner fa-spin text-lg"></i>
                    <span>جاري الحفظ...</span>
                  </>
                ) : (
                  <>
                    <i className="fas fa-save text-lg"></i>
                    <span>حفظ الدرس</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>

        {/* ملاحظة */}
        <div className="mt-8 text-center">
          <div className="inline-flex items-center space-x-2 space-x-reverse text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800/50 px-4 py-3 rounded-xl">
            <i className="fas fa-info-circle text-primary"></i>
            <span>
              سيتم حفظ الدرس وإضافته للدورة فور الانتهاء من رفع الفيديو
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

/*
===================================================================================
شرح تفصيلي لصفحة إضافة درس جديد
===================================================================================

1. تحميل الصفحة:
   - يتم استخراج معرف الكورس من الرابط
   - تهيئة حالات المكون (states) للتحكم في:
     * بيانات النموذج (formData)
     * حالة التحميل (loading)
     * رسائل الخطأ (error)
     * رسائل النجاح (success)
     * حالة رفع الفيديو (uploading)
     * معاينة الفيديو (previewUrl)
     * تقدم الرفع (uploadProgress)

2. التحقق من البيانات:
   - عند تغيير أي حقل في النموذج:
     * يتم التحقق من صحة البيانات
     * التأكد من أن الترتيب رقم موجب
     * التحقق من نوع وحجم الفيديو
   - عند اختيار فيديو:
     * التحقق من نوع الملف (يجب أن يكون فيديو)
     * التحقق من حجم الملف (لا يتجاوز 500 ميجابايت)
     * إنشاء رابط معاينة للفيديو

3. رفع الفيديو:
   - عند الضغط على زر الحفظ:
     * التحقق من وجود جميع البيانات المطلوبة
     * إذا كان نوع الدرس فيديو، يتم رفع الفيديو أولاً
     * عرض شريط تقدم الرفع
     * إمكانية إلغاء الرفع
   - بعد اكتمال رفع الفيديو:
     * الحصول على رابط الفيديو
     * الحصول على معرف الفيديو العام
     * الحصول على مدة الفيديو

4. إنشاء الدرس:
   - تجهيز بيانات الدرس:
     * معلومات أساسية (العنوان، المحتوى، النوع)
     * معلومات الفيديو (الرابط، المعرف، المدة)
     * إعدادات الحماية (DRM، التشفير، العلامة المائية)
   - إرسال طلب إنشاء الدرس للخادم
   - معالجة الاستجابة:
     * في حالة النجاح: عرض رسالة نجاح وإعادة التوجيه
     * في حالة الفشل: عرض رسالة خطأ مناسبة

5. معالجة الأخطاء:
   - أخطاء التحقق من البيانات
   - أخطاء رفع الفيديو
   - أخطاء إنشاء الدرس
   - أخطاء الاتصال بالخادم

6. واجهة المستخدم:
   - نموذج إدخال البيانات
   - معاينة الفيديو
   - شريط تقدم الرفع
   - رسائل النجاح والخطأ
   - أزرار التحكم (حفظ، إلغاء)

7. إعادة التوجيه:
   - بعد نجاح إنشاء الدرس
   - العودة للصفحة السابقة
   - تحديث قائمة الدروس

ملاحظات هامة:
- يجب تسجيل الدخول قبل إضافة درس
- يجب اختيار فيديو إذا كان نوع الدرس فيديو
- يمكن إلغاء رفع الفيديو أثناء الرفع
- يتم التحقق من جميع البيانات قبل الإرسال
- يتم حماية الفيديو تلقائياً
===================================================================================
*/
