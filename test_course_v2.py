import requests
import cloudinary
import cloudinary.uploader

# تكوين Cloudinary
cloudinary.config(
    cloud_name="di5y7hn<PERSON>",
    api_key="423389939179339",
    api_secret="QQnRqLd3_i5HuhRgTF-GVjwSKP8"
)

# تكوين عنوان URL والهيدرز
url = 'http://127.0.0.1:8000/api/courses/'
headers = {
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ3MjUzODE2LCJpYXQiOjE3NDcyNTAyMTYsImp0aSI6ImFhNTlkMmVhODk3NDQyNjM4YzA4ZjgwNDlmMjQwYTZhIiwidXNlcl9pZCI6IjU4ZjMwNzRlLWVmYzEtNDIxOC1hMjA2LWM0YzU0MjhlZWIzZSJ9.w-nnTFjFJcSWicuEYIbapijBoyojp27roDaGYWVgq2I'
}

# إنشاء صورة PNG بسيطة
with open('test_image.png', 'wb') as f:
    f.write(bytes.fromhex('89504e470d0a1a0a0000000d49484452000000010000000108060000001f15c4890000000d4944415478da63fcffff3f03000804fe0201210147c8000000049454e44ae426082'))

# إعداد البيانات
data = {
    'title': 'Python للمبتدئين',
    'description': 'تعلم أساسيات البرمجة باستخدام Python من الصفر للاحتراف',
    'short_description': 'دورة شاملة للمبتدئين في Python',
    'price': '99.99',
    'level': 'beginner',
    'language': 'Arabic'
}

# إعداد الملفات
files = {
    'thumbnail': ('test_image.png', open('test_image.png', 'rb'), 'image/png')
}

# إرسال الطلب
response = requests.post(url, headers=headers, data=data, files=files)

# طباعة النتيجة
print(f'Status Code: {response.status_code}')
print('Response:')
print(response.text) 