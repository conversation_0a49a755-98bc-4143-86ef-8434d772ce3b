"use client";

import Create from "../../../_Components/DashboardComponent/Create";
import DashboardComponent from "../../../_Components/DashboardComponent/DashboardComponent";
import { useEffect, useState } from "react";

export default function InstructorDashboard() {
  // إصلاح مشكلة الهيدرشن: لا تعرض أي شيء حتى تتأكد من أن الكود يعمل على العميل
  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
  }, []);
  if (!mounted) return null;
  return (
    <div className="w-full">
      <div className="  ">
        <DashboardComponent />
      </div>
    </div>
  );
}
