import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON>A<PERSON>s,
  Toolt<PERSON>,
  ResponsiveContainer,
  CartesianGrid,
} from "recharts";
import PersonIcon from "@mui/icons-material/Person";
import EventAvailableIcon from "@mui/icons-material/EventAvailable";
import AttachMoneyIcon from "@mui/icons-material/AttachMoney";
import AccountBalanceWalletIcon from "@mui/icons-material/AccountBalanceWallet";
import EventNoteIcon from "@mui/icons-material/EventNote";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import NotificationsIcon from "@mui/icons-material/Notifications";
import Avatar from "@mui/material/Avatar";
import GroupIcon from "@mui/icons-material/Group";
import BookingPieChart from "./BookingPieChart";
import MiniCalendar from "./MiniCalendar";
import TodoList from "./TodoList";
import axios from "axios";
import Cookies from "js-cookie";
import DashboardNotification from "./DashboardNotification";
import { fetchInstructorDashboardStats } from "../../../services/newInstructorApis";
import NearAppointments from "./NearAppointments";
const StatCard = ({ title, icon, color, values, chartData }) => {
  const [showChart, setShowChart] = useState(false);

  return (
    <div className="group bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl dark:shadow-gray-900/20 p-5 sm:p-6 w-full transition-all duration-300 hover:-translate-y-1 border border-gray-100 dark:border-gray-700 hover:border-gray-200 dark:hover:border-gray-600 animate-fade-in">
      <div
        className={`bg-gradient-to-br ${color} text-white flex flex-col sm:flex-row justify-between items-start sm:items-center p-4 rounded-xl mb-5 gap-3 sm:gap-0 shadow-lg relative overflow-hidden`}
      >
        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-10 translate-x-10"></div>
        <div className="absolute bottom-0 left-0 w-16 h-16 bg-white/5 rounded-full translate-y-8 -translate-x-8"></div>

        <div className="flex items-center gap-3 relative z-10">
          <div className="text-xl sm:text-2xl p-2 bg-white/20 rounded-lg backdrop-blur-sm">
            {icon}
          </div>
          <h2 className="font-bold text-sm sm:text-base">{title}</h2>
        </div>
        <button
          className="bg-white/20 backdrop-blur-sm text-white text-xs px-3 sm:px-4 py-2 rounded-lg hover:bg-white/30 transition-all duration-200 w-full sm:w-auto text-center border border-white/20 hover:border-white/40 relative z-10"
          onClick={() => setShowChart(!showChart)}
        >
          {showChart ? "إخفاء الرسم البياني" : "عرض الرسم البياني"}
        </button>
      </div>

      <div className="grid grid-cols-2 sm:flex sm:justify-around text-center mb-5 gap-3 sm:gap-0">
        {values.map(({ label, value }, idx) => (
          <div
            key={idx}
            className="min-w-0 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200"
          >
            <p className="text-gray-500 dark:text-gray-400 text-xs sm:text-sm mb-2 truncate font-medium">
              {label}
            </p>
            <p className="text-xl sm:text-3xl font-bold text-gray-800 dark:text-gray-100 truncate bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              {value}
            </p>
          </div>
        ))}
      </div>

      {showChart && (
        <div className="h-40 animate-slide-up bg-gray-50 dark:bg-gray-700/30 rounded-xl p-3">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={chartData}>
              <CartesianGrid
                strokeDasharray="3 3"
                vertical={false}
                stroke="#e5e7eb"
              />
              <XAxis
                dataKey="name"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12 }}
              />
              <YAxis hide />
              <Tooltip
                contentStyle={{
                  backgroundColor: "rgba(255, 255, 255, 0.95)",
                  border: "none",
                  borderRadius: "12px",
                  boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
                }}
              />
              <Bar
                dataKey="income"
                fill="url(#incomeGradient)"
                radius={[6, 6, 0, 0]}
                barSize={12}
              />
              <Bar
                dataKey="expense"
                fill="url(#expenseGradient)"
                radius={[6, 6, 0, 0]}
                barSize={12}
              />
              <defs>
                <linearGradient id="incomeGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#4F46E5" />
                  <stop offset="100%" stopColor="#7C3AED" />
                </linearGradient>
                <linearGradient
                  id="expenseGradient"
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop offset="0%" stopColor="#10B981" />
                  <stop offset="100%" stopColor="#059669" />
                </linearGradient>
              </defs>
            </BarChart>
          </ResponsiveContainer>
        </div>
      )}
    </div>
  );
};

const Dashboard = () => {
  const [stats, setStats] = useState({
    total_courses: 0,
    total_students: 0,
    active_courses: 0,
    total_revenue: 0,
  });
  const [notifications, setNotifications] = useState([]);
  const [recentUsers, setRecentUsers] = useState([]);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const token = Cookies.get("authToken");
        if (!token) return;
        const data = await fetchInstructorDashboardStats(token);
        setStats(data);
      } catch (err) {
        console.error("Error fetching dashboard data", err);
      }
    };
    fetchStats();
  }, []);

  const cardsData = [
    {
      id: 1,
      title: "إجمالي الدورات",
      icon: <EventNoteIcon />,
      color: "from-blue-500 to-blue-700",
      values: [{ label: "عدد الدورات", value: stats.total_courses }],
      chartData: [],
    },
    {
      id: 2,
      title: "إجمالي الطلاب",
      icon: <PersonIcon />,
      color: "from-green-500 to-emerald-600",
      values: [{ label: "عدد الطلاب", value: stats.total_students }],
      chartData: [],
    },
    {
      id: 3,
      title: "الدورات النشطة",
      icon: <ArrowUpwardIcon />,
      color: "from-purple-500 to-purple-700",
      values: [{ label: "عدد الدورات النشطة", value: stats.active_courses }],
      chartData: [],
    },
    {
      id: 4,
      title: "إجمالي الإيرادات",
      icon: <AttachMoneyIcon />,
      color: "from-pink-500 to-rose-600",
      values: [{ label: "إجمالي الإيرادات", value: `${stats.total_revenue} ج.م` }],
      chartData: [],
    },
  ];

  return (
    <main
      dir="rtl"
      className="text-gray-700 dark:text-gray-300 min-h-screen bg-gradient-to-b  from-white via-gray-50 to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 p-6 md:p-8 lg:p-10 lg:max-w-[1400px] lg:mx-auto transition-all duration-300"
    >
      {/* Welcome Header */}
      <div className="mb-8 animate-fade-in">
        <h1 className="text-3xl md:text-4xl font-bold text-gray-800 dark:text-gray-100 mb-2">
          مرحباً بك في لوحة التحكم
        </h1>
        <p className="text-gray-600 dark:text-gray-400 text-lg">
          تابع إحصائياتك وإدارة دوراتك من مكان واحد
        </p>
      </div>

      <section className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-10">
        {cardsData.map((card, index) => (
          <div
            key={card.id}
            className="animate-slide-up"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <StatCard {...card} />
          </div>
        ))}
      </section>

      <section className="grid grid-cols-1 gap-6  mb-8">
        {/* <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl dark:shadow-gray-900/20 p-6 flex flex-col transition-all duration-300 border border-gray-100 dark:border-gray-700 animate-slide-in-right">
          <header className="flex items-center gap-3 border-b border-gray-200 dark:border-gray-700 pb-4 mb-6 text-gray-700 dark:text-gray-300 font-bold text-lg">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <EventNoteIcon
                fontSize="medium"
                className="text-blue-600 dark:text-blue-400"
              />
            </div>
            <h3>الحجوزات الأخيرة</h3>
          </header>
          <div className="flex-grow flex flex-col justify-center items-center text-center text-gray-400 dark:text-gray-500 select-none py-8">
            <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
              <EventNoteIcon
                fontSize="large"
                className="text-gray-400 dark:text-gray-500"
              />
            </div>
            <p className="text-lg font-medium">لا يوجد حجوزات</p>
            <p className="text-sm mt-1">ستظهر الحجوزات الجديدة هنا</p>
          </div>
        </div> */}

        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl dark:shadow-gray-900/20 p-6 flex flex-col transition-all duration-300 border border-gray-100 dark:border-gray-700 animate-slide-in-left">
          <NearAppointments />
        </div>
      </section>

      {/* Notifications Section */}
      <section className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl dark:shadow-gray-900/20 p-6 transition-all duration-300 border border-gray-100 dark:border-gray-700 animate-slide-up">
          <header className="flex items-center gap-3 border-b border-gray-200 dark:border-gray-700 pb-4 mb-6 text-gray-700 dark:text-gray-300 font-bold text-lg">
            <div className="p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
              <NotificationsIcon
                fontSize="medium"
                className="text-yellow-600 dark:text-yellow-400"
              />
            </div>
            <h3>الإشعارات</h3>
          </header>
          <div className="overflow-y-auto max-h-[200px] custom-scrollbar">
            <DashboardNotification />
          </div>
        </div>

        {/* Recent Users Section */}
        <div
          className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl dark:shadow-gray-900/20 p-6 transition-all duration-300 border border-gray-100 dark:border-gray-700 animate-slide-up"
          style={{ animationDelay: "100ms" }}
        >
          <header className="flex items-center gap-3 border-b border-gray-200 dark:border-gray-700 pb-4 mb-6 text-gray-700 dark:text-gray-300 font-bold text-lg">
            <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
              <GroupIcon
                fontSize="medium"
                className="text-green-600 dark:text-green-400"
              />
            </div>
            <h3>أحدث الطلاب</h3>
          </header>
          <div className="space-y-4">
            {recentUsers.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8 text-gray-400 dark:text-gray-500">
                <div className="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-3">
                  <GroupIcon fontSize="medium" />
                </div>
                <p className="text-sm font-medium">لا يوجد طلاب جدد</p>
                <p className="text-xs mt-1">سيظهر الطلاب الجدد هنا</p>
              </div>
            ) : (
              recentUsers.map((user, index) => (
                <div
                  key={user.id}
                  className="flex items-center gap-4 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200 animate-slide-in-right"
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  <Avatar className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 text-white font-bold">
                    {user.name[0]}
                  </Avatar>
                  <div className="min-w-0 flex-1">
                    <p className="font-medium text-gray-800 dark:text-gray-200 text-sm truncate">
                      {user.name}
                    </p>
                    <p className="text-xs text-gray-400 dark:text-gray-500 truncate">
                      {user.joined}
                    </p>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        <div className="animate-slide-up" style={{ animationDelay: "200ms" }}>
          <BookingPieChart
            data={[
              { name: "مؤكد", value: 400 },
              { name: "ملغي", value: 100 },
              { name: "قيد الانتظار", value: 200 },
            ]}
          />
        </div>

        <div className="animate-slide-up" style={{ animationDelay: "300ms" }}>
          <TodoList />
        </div>

        <div className="animate-slide-up" style={{ animationDelay: "400ms" }}>
          <MiniCalendar />
        </div>
      </section>
    </main>
  );
};

export default Dashboard;
