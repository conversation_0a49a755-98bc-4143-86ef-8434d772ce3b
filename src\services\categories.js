import axios from "axios";
import { API_BASE_URL } from "../config/api";

export const getMainCategories = async (token) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/api/main-categories/`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response; // رجّع الـ response كامل
  } catch (error) {
    console.error("Error fetching main categories:", error);
    throw error;
  }
};
