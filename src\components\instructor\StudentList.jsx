import React from "react";

const StudentList = ({ students = [], studentsCount = 0 }) => (
  <div>
    <div className="flex items-center gap-3 mb-4">
      <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
        <svg
          className="w-4 h-4 text-white"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
          />
        </svg>
      </div>
      <h3 className="text-lg font-bold text-gray-800 dark:text-white">
        المشتركين
      </h3>
      <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded-full text-xs font-medium">
        {studentsCount}
      </span>
    </div>

    {students && students.length > 0 ? (
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {students.slice(0, 10).map((student, idx) => (
          <div
            key={student.id || student.email || student.username || idx}
            className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200"
          >
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-white font-bold text-sm">
                {(student.username || student.email || "طالب")
                  .charAt(0)
                  .toUpperCase()}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="font-medium text-gray-800 dark:text-white truncate">
                {student.username || "طالب مجهول"}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                {student.email}
              </p>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                نشط
              </span>
            </div>
          </div>
        ))}
        {students.length > 10 && (
          <div className="text-center py-2">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              و {students.length - 10} طالب آخر...
            </span>
          </div>
        )}
      </div>
    ) : (
      <div className="text-center py-8">
        <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-3">
          <svg
            className="w-8 h-8 text-gray-400 dark:text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
            />
          </svg>
        </div>
        <h4 className="font-medium text-gray-900 dark:text-white mb-1">
          لا يوجد مشتركين بعد
        </h4>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          عندما يشترك الطلاب في الكورس، ستظهر أسماؤهم هنا
        </p>
      </div>
    )}
  </div>
);

export default StudentList;
