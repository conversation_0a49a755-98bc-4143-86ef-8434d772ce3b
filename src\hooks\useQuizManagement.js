import { useState, useCallback } from 'react';
import axios from 'axios';
import Cookies from 'js-cookie';
import { fetchInstructorLessons } from '../services/instructor';

/**
 * Custom Hook لإدارة الكويزات والأسئلة
 * @param {string} courseId - معرف الكورس
 * @param {Array} lessons - قائمة الدروس الحالية
 * @param {Object} croppedImages - الصور المقصوصة
 * @param {function} setLessons - دالة تحديث الدروس
 * @param {function} setError - دالة تحديث الأخطاء
 */
export const useQuizManagement = (courseId, lessons, croppedImages, setLessons, setError) => {
  const [questionLoading, setQuestionLoading] = useState({});
  const [quizLoading, setQuizLoading] = useState({});

  // إضافة كويز جديد
  const handleAddQuiz = useCallback(async (lessonId, quizType, formData = {}) => {
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }

    console.log("Adding quiz with data:", { lessonId, quizType, formData });

    try {
      setQuizLoading(prev => ({ ...prev, [lessonId]: true }));

      // إعداد البيانات حسب نوع الكويز
      const quizData = {
        lesson: lessonId,
        title: formData.title || (quizType === "exam" ? "امتحان جديد" : "واجب جديد"),
        description: formData.description || (quizType === "exam" ? "امتحان جديد للدرس" : "واجب جديد للدرس"),
        quiz_type: quizType,
      };

      // للامتحانات: إضافة الدرجة النهائية والوقت - zaki alkholy
      if (quizType === "exam") {
        quizData.max_score = parseInt(formData.max_score) || 100;
        quizData.time_limit = parseInt(formData.time_limit) || 0;
      } else {
        // للواجبات: الدرجة النهائية = 0 (سيتم تحديثها لاحقاً لتساوي عدد الأسئلة) - zaki alkholy
        quizData.max_score = parseInt(formData.max_score) || 0;
        quizData.time_limit = parseInt(formData.time_limit) || 0;
      }

      console.log("Quiz data being sent to API:", quizData);

      await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL}/api/quizzes/`,
        quizData,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      // تحديث الدروس
      const lessonsList = await fetchInstructorLessons(courseId, token);
      setLessons(lessonsList);
    } catch {
      setError("فشل في إضافة الاختبار/الواجب");
    } finally {
      setQuizLoading(prev => ({ ...prev, [lessonId]: false }));
    }
  }, [courseId, setLessons, setError]);

  // حذف كويز
  const handleDeleteQuiz = useCallback(async (lessonId, quizId) => {
    if (!window.confirm("هل أنت متأكد من حذف هذا الامتحان/الواجب؟")) return;
    
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }

    try {
      setQuizLoading(prev => ({ ...prev, [quizId]: true }));
      
      await axios.delete(
        `${process.env.NEXT_PUBLIC_API_URL}/api/quizzes/${quizId}/`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      // تحديث الدروس
      const lessonsList = await fetchInstructorLessons(courseId, token);
      setLessons(lessonsList);
    } catch {
      setError("فشل في حذف الامتحان/الواجب");
    } finally {
      setQuizLoading(prev => ({ ...prev, [quizId]: false }));
    }
  }, [courseId, setLessons, setError]);

  // تعديل كويز
  const handleEditQuiz = useCallback(async (lessonId, quizId, quizData) => {
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }

    try {
      setQuizLoading(prev => ({ ...prev, [quizId]: true }));

      console.log("Editing quiz with data:", quizData); // للتأكد من البيانات
      console.log("Quiz ID:", quizId); // للتأكد من الـ ID

      const response = await axios.patch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/quizzes/${quizId}/`,
        quizData,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      console.log("Edit response:", response.data); // للتأكد من الاستجابة

      // تحديث الدروس
      const lessonsList = await fetchInstructorLessons(courseId, token);
      setLessons(lessonsList);
    } catch (error) {
      console.error("Edit quiz error:", error.response?.data || error.message);
      setError("فشل في تعديل الامتحان/الواجب");
    } finally {
      setQuizLoading(prev => ({ ...prev, [quizId]: false }));
    }
  }, [courseId, setLessons, setError]);

  // إضافة سؤال - نفس منطق الـ old version
  const handleAddQuestion = useCallback(async (quizId, answers, image, questionForm = {}) => {
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }

    console.log("Adding question with data:", { quizId, answers, image, questionForm });

    try {
      setQuestionLoading(prev => ({ ...prev, [quizId]: true }));

      // جلب نوع الكويز (امتحان أو واجب) - نفس منطق الـ old version
      const quizObj = lessons
        .flatMap((l) => l.quizzes || [])
        .find((q) => q.id === quizId);

      // تحويل نوع السؤال من mcq إلى multiple_choice - نفس منطق الـ old version
      let qType = questionForm?.question_type || "mcq";
      if (qType === "mcq") qType = "multiple_choice";

      // points=1 تلقائياً في الواجب - نفس منطق الـ old version
      let points = 1;
      if (quizObj && quizObj.quiz_type === "exam") {
        points = Number(questionForm?.points) || 1;
      }

      // إعداد FormData للسؤال مع الصورة
      const formData = new FormData();
      formData.append("quiz", quizId);
      formData.append("text", questionForm?.text || "");
      formData.append("question_type", qType);
      formData.append("points", points);
      formData.append("order", Number(questionForm?.order) || 1);

      // إضافة الصورة إذا كانت موجودة - نفس منطق الـ old version
      if (image) {
        formData.append("image", image, "question-image.jpg");
      } else if (croppedImages[quizId]) {
        formData.append("image", croppedImages[quizId], "question-image.jpg");
      }

      console.log("FormData contents:");
      for (let [key, value] of formData.entries()) {
        console.log(key, value);
      }

      // إضافة السؤال
      const qRes = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL}/api/questions/`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const questionId = qRes.data.id;

      // إضافة الإجابات إذا كانت موجودة
      if (answers?.length > 0) {
        for (const ans of answers) {
          await axios.post(
            `${process.env.NEXT_PUBLIC_API_URL}/api/answers/`,
            {
              question: questionId,
              text: ans.text,
              is_correct: ans.is_correct,
            },
            { headers: { Authorization: `Bearer ${token}` } }
          );
        }
      }

      // تحديث الدروس
      const lessonsList = await fetchInstructorLessons(courseId, token);
      setLessons(lessonsList);

      // للواجبات: تحديث الدرجة النهائية تلقائياً - zaki alkholy
      const updatedQuiz = lessonsList
        .flatMap((l) => l.quizzes || [])
        .find((q) => q.id === quizId);

      if (updatedQuiz?.quiz_type === "assignment") {
        const questionCount = updatedQuiz.questions?.length || 0;
        if (questionCount > 0 && updatedQuiz.max_score !== questionCount) {
          await axios.patch(
            `${process.env.NEXT_PUBLIC_API_URL}/api/quizzes/${quizId}/`,
            { max_score: questionCount },  // النظام الجديد - zaki alkholy
            { headers: { Authorization: `Bearer ${token}` } }
          );

          // تحديث البيانات فوراً بعد تحديث الدرجة النهائية
          const refreshedLessons = await fetchInstructorLessons(courseId, token);
          setLessons(refreshedLessons);
        }
      }
    } catch {
      setError("فشل في إضافة السؤال");
    } finally {
      setQuestionLoading(prev => ({ ...prev, [quizId]: false }));
    }
  }, [courseId, setLessons, setError, lessons, croppedImages]);

  // حذف سؤال
  const handleDeleteQuestion = useCallback(async (quizId, questionId) => {
    if (!window.confirm("هل أنت متأكد من حذف هذا السؤال؟")) return;
    
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }

    try {
      setQuestionLoading(prev => ({ ...prev, [quizId]: true }));

      await axios.delete(
        `${process.env.NEXT_PUBLIC_API_URL}/api/questions/${questionId}/`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      // تحديث الدروس
      const lessonsList = await fetchInstructorLessons(courseId, token);
      setLessons(lessonsList);

      // للواجبات: تحديث درجة النجاح تلقائياً
      const updatedQuiz = lessonsList
        .flatMap((l) => l.quizzes || [])
        .find((q) => q.id === quizId);

      if (updatedQuiz?.quiz_type === "assignment") {
        const questionCount = updatedQuiz.questions?.length || 0;
        if (updatedQuiz.max_score !== questionCount) {
          await axios.patch(
            `${process.env.NEXT_PUBLIC_API_URL}/api/quizzes/${quizId}/`,
            { max_score: questionCount },  // النظام الجديد - zaki alkholy
            { headers: { Authorization: `Bearer ${token}` } }
          );

          // تحديث البيانات فوراً بعد تحديث الدرجة النهائية
          const refreshedLessons = await fetchInstructorLessons(courseId, token);
          setLessons(refreshedLessons);
        }
      }
    } catch {
      setError("فشل في حذف السؤال");
    } finally {
      setQuestionLoading(prev => ({ ...prev, [quizId]: false }));
    }
  }, [courseId, setLessons, setError]);

  return {
    questionLoading,
    quizLoading,
    handleAddQuiz,
    handleDeleteQuiz,
    handleEditQuiz,
    handleAddQuestion,
    handleDeleteQuestion,
  };
};
