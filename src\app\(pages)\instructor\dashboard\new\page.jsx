"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import axios from "axios";
import Cookies from "js-cookie";
import { ButtonLoader } from "@/components/common/UniversalLoader";
import { getMainCategories } from "../../../../../services/categories";
import { API_BASE_URL } from "../../../../../config/api";

const NewCourse = () => {
  const { user } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [thumbnail, setThumbnail] = useState(null);
  const [promoVideo, setPromoVideo] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isClient, setIsClient] = useState(false);
  const [mainCategories, setMainCategories] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedMainCategory, setSelectedMainCategory] = useState("");
  const [isPublished, setIsPublished] = useState(false);


  useEffect(() => {
    setIsClient(true);
    fetchMainCategories(); // ← استبدال fetchCategories بالوظيفة الجديدة
  }, []);

  const fetchMainCategories = async () => {
    try {
      const token = Cookies.get("authToken");
      if (!token) return;

      const response = await getMainCategories(token);

      if (Array.isArray(response.data)) {
        setMainCategories(response.data);
      } else {
        setMainCategories([]);
        toast.error("حدث خطأ في تنسيق البيانات المستلمة");
      }
    } catch (error) {
      toast.error("حدث خطأ أثناء جلب التصنيفات الرئيسية");
      setMainCategories([]);
    }
  };
  useEffect(() => {
    if (selectedMainCategory) {
      const selected = mainCategories.find(
        (cat) => cat.id === selectedMainCategory
      );
      if (selected) {
        setCategories(selected.subcategories || []);
      }
    } else {
      setCategories([]);
    }
  }, [selectedMainCategory, mainCategories]);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm();

  const handleFileChange = (e, type) => {
    const file = e.target.files[0];
    if (type === "thumbnail") {
      setThumbnail(file);
    } else if (type === "promoVideo") {
      setPromoVideo(file);
    }
  };

  const onSubmit = async (data) => {
    // تحقق من أن السعر بعد الخصم لا يتجاوز السعر الأصلي
    if (
      data.discount_price &&
      Number(data.discount_price) >= Number(data.price)
    ) {
      toast.error("السعر بعد الخصم يجب أن يكون أقل من السعر الأصلي");
      return;
    }
    try {
      if (!isClient) return;
      setLoading(true);

      // استخدام FormData للصور والفيديوهات - zaki alkholy
      const formData = new FormData();

      // إضافة البيانات النصية
      formData.append("title", data.title);
      formData.append("description", data.description);
      formData.append("short_description", data.short_description || "");
      formData.append("price", data.price);
      formData.append("currency", data.currency || "USD");
      formData.append("language", data.language || "Arabic");
      formData.append("level", data.level || "beginner");
      formData.append("prerequisites", data.prerequisites || "");
      formData.append("learning_outcomes", data.learning_outcomes || "");
      formData.append("is_published", isPublished);
      formData.append("discount_price", data.discount_price);

      // إضافة category إذا كان موجود ومش "اخرى" - zaki alkholy
      if (data.category && data.category !== "اخرى") {
        formData.append("category", data.category); // استخدام UUID كما هو
      }

      // إضافة max_students إذا كان موجود
      if (data.max_students) {
        formData.append("max_students", parseInt(data.max_students));
      }

      // إضافة الملفات - zaki alkholy
      if (thumbnail) {
        formData.append("thumbnail", thumbnail);
      }
      if (promoVideo) {
        formData.append("promo_video", promoVideo);
      }

      // إضافة التوكن
      const token = Cookies.get("authToken");
      if (!token) {
        toast.error("يرجى تسجيل الدخول أولاً");
        router.push("/login");
        return;
      }

      const response = await axios.post(
        `${API_BASE_URL}/api/courses/`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "multipart/form-data",
          },
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            setUploadProgress(percentCompleted);
          },
        }
      );

      toast.success("تم إنشاء الدورة بنجاح");

      // الانتقال لصفحة إدارة الكورس الجديد - zaki alkholy
      if (response.data.slug) {
        router.push(`/instructor/dashboard/${response.data.slug}`);
      } else {
        router.push("/instructor/dashboard");
      }
    } catch (error) {
      // التحقق من نوع الخطأ
      if (error.response?.status === 400) {
        const errorData = error.response.data;

        // خطأ في السعر
        if (errorData.price && errorData.price[0]) {
          toast.error(errorData.price[0]);
        }
        // خطأ في السعر بعد الخصم
        else if (errorData.discount_price && errorData.discount_price[0]) {
          toast.error(errorData.discount_price[0]);
        }
        // خطأ عام في validation
        else if (errorData.non_field_errors && errorData.non_field_errors[0]) {
          toast.error(errorData.non_field_errors[0]);
        }
        // خطأ في المحفظة (من الـ backend validation)
        else if (errorData.error && (errorData.error.includes('محفظة') || errorData.error.includes('wallet') || errorData.error.includes('payment'))) {
          toast.error("⚠️ يجب إضافة رقم محفظة ووسيلة دفع قبل إنشاء الكورس");
          setTimeout(() => {
            toast("💡 يرجى الذهاب إلى إعدادات الحساب وإضافة معلومات الدفع", {
              duration: 5000,
              icon: "ℹ️"
            });
          }, 1000);
        }
        // أي خطأ آخر
        else {
          const firstError = Object.values(errorData)[0];
          if (Array.isArray(firstError)) {
            toast.error(firstError[0]);
          } else {
            toast.error(firstError || "حدث خطأ في البيانات المدخلة");
          }
        }
      } else if (error.response?.status === 403) {
        toast.error("غير مصرح لك بإنشاء كورسات. تأكد من أنك مسجل كمعلم.");
      } else {
        toast.error("حدث خطأ أثناء إنشاء الدورة");
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isClient) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-b  from-white via-gray-50 to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header Section */}
        <div className="flex gap-4 items-center mb-12 animate-fade-in">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-primary to-accent rounded-full mb-6 shadow-lg">
            <i className="fas fa-plus text-white text-2xl"></i>
          </div>
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent mb-4 font-arabic">
              إنشاء دورة جديدة
            </h1>
            <p className="text-gray-600 dark:text-gray-400 text-lg max-w-2xl mx-auto">
              أنشئ دورة تعليمية متميزة وشاركها مع الطلاب حول العالم
            </p>
          </div>
        </div>

        {/* Form Container */}
        <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-gray-200/50 dark:border-gray-700/50 p-8 animate-slide-up">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
            {/* العنوان */}
            <div className="space-y-2 animate-slide-in-right">
              <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                <i className="fas fa-heading text-primary mr-3 text-lg ml-2"></i>
                عنوان الدورة
              </label>
              <div className="relative group">
                <input
                  type="text"
                  {...register("title", { required: "هذا الحقل مطلوب" })}
                  placeholder="أدخل عنوان جذاب ووصفي للدورة"
                  className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md font-arabic"
                />
                <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300">
                  <i className="fas fa-edit"></i>
                </div>
              </div>
              {errors.title && (
                <div className="flex items-center text-red-500 text-sm mt-2 animate-slide-in-left">
                  <i className="fas fa-exclamation-circle mr-2"></i>
                  {errors.title.message}
                </div>
              )}
            </div>

            {/* اختيار الفئة */}
            {/* <div>
          <label className="block text-sm font-medium mb-2">الفئة</label>
          <select
            {...register("category", { required: "هذا الحقل مطلوب" })}
            className="w-full p-2 border rounded"
          >
            <option value="">اختر الفئة...</option>
            {Array.isArray(categories) &&
              categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {`${category.name}  (${category.description}) `}
                </option>
              ))}
          </select>
          {errors.category && (
            <p className="text-red-500 text-sm mt-1">
              {errors.category.message}
            </p>
          )}
        </div> */}
            {/* التصنيفات */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 animate-slide-in-left">
              {/* التصنيف الرئيسي */}
              <div className="space-y-2">
                <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                  <i className="fas fa-layer-group text-primary mr-3 text-lg ml-2"></i>
                  التصنيف الرئيسي
                </label>
                <div className="relative group">
                  <select
                    value={selectedMainCategory}
                    onChange={(e) => setSelectedMainCategory(e.target.value)}
                    className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md appearance-none font-arabic"
                  >
                    <option value="">اختر تصنيف رئيسي...</option>
                    {mainCategories.map((main) => (
                      <option key={main.id} value={main.id}>
                        {main.name}
                      </option>
                    ))}
                  </select>
                  <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300 pointer-events-none">
                    <i className="fas fa-chevron-down"></i>
                  </div>
                </div>
              </div>

              {/* الفئة الفرعية */}
              <div className="space-y-2">
                <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                  <i className="fas fa-tags text-primary mr-3 text-lg ml-2"></i>
                  الفئة الفرعية
                </label>
                <div className="relative group">
                  <select
                    {...register("category", { required: "هذا الحقل مطلوب" })}
                    className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md appearance-none font-arabic"
                  >
                    <option value="">اختر الفئة...</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {`${category.name}  (${category.description})`}
                      </option>
                    ))}
                    <option value="اخرى">أخرى</option>
                  </select>
                  <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300 pointer-events-none">
                    <i className="fas fa-chevron-down"></i>
                  </div>
                </div>
                {errors.category && (
                  <div className="flex items-center text-red-500 text-sm mt-2 animate-slide-in-left">
                    <i className="fas fa-exclamation-circle mr-2"></i>
                    {errors.category.message}
                  </div>
                )}
              </div>
            </div>

            {/* الأوصاف */}
            <div className="space-y-6 animate-slide-in-right">
              {/* الوصف القصير */}
              <div className="space-y-2">
                <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                  <i className="fas fa-align-left text-primary mr-3 text-lg ml-2"></i>
                  الوصف القصير
                </label>
                <div className="relative group">
                  <textarea
                    {...register("short_description", {
                      required: "هذا الحقل مطلوب",
                    })}
                    placeholder="اكتب وصفاً مختصراً وجذاباً للدورة (2-3 جمل)"
                    rows={3}
                    className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md resize-none font-arabic"
                  />
                  <div className="absolute right-4 top-4 text-gray-400 group-focus-within:text-primary transition-colors duration-300">
                    <i className="fas fa-quote-right"></i>
                  </div>
                </div>
                {errors.short_description && (
                  <div className="flex items-center text-red-500 text-sm mt-2 animate-slide-in-left">
                    <i className="fas fa-exclamation-circle mr-2"></i>
                    {errors.short_description.message}
                  </div>
                )}
              </div>

              {/* الوصف الكامل */}
              <div className="space-y-2">
                <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                  <i className="fas fa-file-alt text-primary mr-3 text-lg ml-2"></i>
                  الوصف الكامل
                </label>
                <div className="relative group">
                  <textarea
                    {...register("description", {
                      required: "هذا الحقل مطلوب",
                    })}
                    placeholder="اكتب وصفاً تفصيلياً شاملاً للدورة، أهدافها، محتواها، والفوائد التي سيحصل عليها الطلاب"
                    rows={5}
                    className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md resize-none font-arabic"
                  />
                  <div className="absolute right-4 top-4 text-gray-400 group-focus-within:text-primary transition-colors duration-300">
                    <i className="fas fa-paragraph"></i>
                  </div>
                </div>
                {errors.description && (
                  <div className="flex items-center text-red-500 text-sm mt-2 animate-slide-in-left">
                    <i className="fas fa-exclamation-circle mr-2"></i>
                    {errors.description.message}
                  </div>
                )}
              </div>
            </div>

            {/* الملفات */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 animate-slide-in-left">
              {/* الصورة المصغرة */}
              <div className="space-y-2">
                <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                  <i className="fas fa-image text-primary mr-3 text-lg ml-2"></i>
                  الصورة المصغرة
                </label>
                <div className="relative group">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleFileChange(e, "thumbnail")}
                    className="w-full px-4 py-4 bg-white dark:bg-gray-700 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-400 dark:hover:border-gray-500 shadow-sm hover:shadow-md file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-primary file:text-white hover:file:bg-primary-dark file:transition-colors file:duration-300"
                  />
                  <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300 pointer-events-none">
                    <i className="fas fa-upload"></i>
                  </div>
                </div>
                {thumbnail && (
                  <div className="flex items-center text-green-600 dark:text-green-400 text-sm mt-2">
                    <i className="fas fa-check-circle mr-2"></i>
                    تم اختيار الصورة: {thumbnail.name}
                  </div>
                )}
              </div>

              {/* الفيديو الترويجي */}
              <div className="space-y-2">
                <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                  <i className="fas fa-video text-primary mr-3 text-lg ml-2"></i>
                  الفيديو الترويجي
                </label>
                <div className="relative group">
                  <input
                    type="file"
                    accept="video/*"
                    onChange={(e) => handleFileChange(e, "promoVideo")}
                    className="w-full px-4 py-4 bg-white dark:bg-gray-700 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-400 dark:hover:border-gray-500 shadow-sm hover:shadow-md file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-primary file:text-white hover:file:bg-primary-dark file:transition-colors file:duration-300"
                  />
                  <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300 pointer-events-none">
                    <i className="fas fa-upload"></i>
                  </div>
                </div>
                {promoVideo && (
                  <div className="flex items-center text-green-600 dark:text-green-400 text-sm mt-2">
                    <i className="fas fa-check-circle mr-2"></i>
                    تم اختيار الفيديو: {promoVideo.name}
                  </div>
                )}
              </div>
            </div>

            {/* معلومات إضافية */}
            <div className="space-y-6 animate-slide-in-right">
              {/* عنوان القسم */}
              <div className="flex items-center space-x-4 mb-6 gap-3">
                <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-primary to-accent rounded-full">
                  <i className="fas fa-cog text-white"></i>
                </div>
                <h3 className="text-xl font-bold text-gray-800 dark:text-white font-arabic ">
                  إعدادات الدورة
                </h3>
              </div>

              {/* اختيار مجاني */}
              {/* <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 rounded-xl border border-blue-200 dark:border-blue-700">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="relative">
                    <input
                      type="checkbox"
                      id="isFree"
                      checked={isFree}
                      onChange={(e) => {
                        setIsFree(e.target.checked);
                      }}
                      className="sr-only"
                    />
                    <label
                      htmlFor="isFree"
                      className={`flex items-center justify-center w-6 h-6 rounded-md border-2 cursor-pointer transition-all duration-300 ${
                        isFree
                          ? "bg-primary border-primary text-white"
                          : "bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 hover:border-primary"
                      }`}
                    >
                      {isFree && <i className="fas fa-check text-sm"></i>}
                    </label>
                  </div>
                  <div className="flex-1">
                    <label
                      htmlFor="isFree"
                      className="text-lg font-semibold text-gray-800 dark:text-white cursor-pointer font-arabic"
                    >
                      الدورة مجانية
                    </label>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      اجعل الدورة متاحة مجاناً لجميع الطلاب
                    </p>
                  </div>
                  <div className="text-2xl">
                    <i
                      className={`fas ${
                        isFree
                          ? "fa-gift text-green-500"
                          : "fa-dollar-sign text-primary"
                      }`}
                    ></i>
                  </div>
                </div>
              </div> */}

              {/* معلومات التسعير والمستوى */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* السعر */}
                <div className="space-y-2">
                  <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                    <i className="fas fa-dollar-sign text-primary mr-3 text-lg ml-2"></i>
                    السعر
                  </label>
                  <div className="relative group">
                    <input
                      type="number"
                      {...register("price", {
                        required: "هذا الحقل مطلوب",
                        validate: (value) => {
                          const price = parseFloat(value);
                          if (price < 100) return "السعر يجب أن يكون 100 جنيه على الأقل";
                          return true;
                        }
                      })}
                      placeholder="100.00"
                      className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md font-arabic"
                    />
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300">
                      <i className="fas fa-coins"></i>
                    </div>
                  </div>
                  {errors.price && (
                    <p className="text-red-500 text-sm mt-1 font-arabic">
                      {errors.price.message}
                    </p>
                  )}
                </div>

                {/* السعر بعد الخصم */}
                <div className="space-y-2">
                  <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                    <i className="fas fa-percentage text-primary mr-3 text-lg ml-2"></i>
                    السعر بعد الخصم
                  </label>
                  <div className="relative group">
                    <input
                      type="number"
                      {...register("discount_price", {
                        validate: (value) => {
                          if (!value) return true;
                          const discountPrice = parseFloat(value);
                          if (discountPrice < 100) return "سعر الخصم يجب أن يكون 100 جنيه على الأقل";

                          const price = parseFloat(watch("price"));
                          if (price && discountPrice >= price) {
                            return "السعر بعد الخصم يجب أن يكون أقل من السعر الأصلي";
                          }

                          return true;
                        }
                      })}
                      placeholder="السعر المخفض (اختياري)"
                      className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md font-arabic"
                    />
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300">
                      <i className="fas fa-tag"></i>
                    </div>
                  </div>
                  {errors.discount_price && (
                    <p className="text-red-500 text-sm mt-1 font-arabic">
                      {errors.discount_price.message}
                    </p>
                  )}
                </div>

                {/* العملة */}
                <div className="space-y-2">
                  <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                    <i className="fas fa-money-bill-wave text-primary mr-3 text-lg ml-2"></i>
                    العملة
                  </label>
                  <div className="relative group">
                    <select
                      {...register("currency", { required: "هذا الحقل مطلوب" })}
                      className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md appearance-none font-arabic"
                    >
                      <option value="EGP">جنيه مصري (EGP)</option>
                      <option value="USD">دولار أمريكي (USD)</option>
                      <option value="EUR">يورو (EUR)</option>
                    </select>
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300 pointer-events-none">
                      <i className="fas fa-chevron-down"></i>
                    </div>
                  </div>
                </div>

                {/* المستوى */}
                <div className="space-y-2">
                  <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                    <i className="fas fa-signal text-primary mr-3 text-lg ml-2"></i>
                    مستوى الدورة
                  </label>
                  <div className="relative group">
                    <select
                      {...register("level", { required: "هذا الحقل مطلوب" })}
                      className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md appearance-none font-arabic"
                    >
                      <option value="beginner">مبتدئ</option>
                      <option value="intermediate">متوسط</option>
                      <option value="advanced">متقدم</option>
                    </select>
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300 pointer-events-none">
                      <i className="fas fa-chevron-down"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* خيارات النشر والإرسال */}
            <div className="space-y-6 animate-slide-in-left">
              {/* خيار النشر */}
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-6 rounded-xl border border-green-200 dark:border-green-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 space-x-reverse">
                    <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full">
                      <i className="fas fa-globe text-white"></i>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-800 dark:text-white font-arabic">
                        نشر الدورة
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        اجعل الدورة متاحة للطلاب فور الإنشاء
                      </p>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={isPublished}
                      onChange={(e) => setIsPublished(e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-14 h-7 bg-gray-200 dark:bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-6 after:w-6 after:transition-all peer-checked:bg-gradient-to-r peer-checked:from-primary peer-checked:to-accent"></div>
                  </label>
                </div>
              </div>

              {/* شريط التقدم */}
              {uploadProgress > 0 && (
                <div className="space-y-3 animate-pulse">
                  <div className="flex items-center justify-between text-sm font-medium text-gray-700 dark:text-gray-300">
                    <span className="flex items-center">
                      <i className="fas fa-upload mr-2 text-primary"></i>
                      جاري رفع الملفات...
                    </span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
                    <div
                      className="bg-gradient-to-r from-primary to-accent h-3 rounded-full transition-all duration-300 ease-out"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}

              {/* زر الإرسال */}
              <button
                type="submit"
                disabled={loading}
                className="w-full bg-gradient-to-r from-primary to-accent hover:from-primary-dark hover:to-accent-light text-white py-4 px-8 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-[1.02] hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-3 space-x-reverse font-arabic shadow-lg"
              >
                {loading ? (
                  <>
                    <ButtonLoader size="small" />
                    <span>جاري الإنشاء...</span>
                  </>
                ) : (
                  <>
                    <i className="fas fa-plus-circle text-xl"></i>
                    <span>إنشاء الدورة</span>
                  </>
                )}
              </button>

              {/* ملاحظة */}
              <div className="text-center text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800/50 p-4 rounded-xl">
                <i className="fas fa-info-circle mr-2"></i>
                بعد إنشاء الدورة، ستتمكن من إضافة الدروس والمحتوى التعليمي
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default NewCourse;
