import React, { useState } from "react";
import InstructorReviewComments from "./ReviewComments";

const ReviewList = ({
  reviews,
  commentText,
  setCommentText,
  commentLoading,
  handleAddComment,
  approving,
  handleApproveReview,
  courseData,
  // إضافة props جديدة للتعليقات الشجرية - zaki alkholy
  reviewComments = {},
  replyText = {},
  setReplyText = () => {},
  showReplyForm = {},
  onToggleReplyForm = () => {},
  expandedComments = {},
  onToggleExpand = () => {},
  currentUser = null,
  handleDeleteComment = () => {},
  handleDeleteReview = () => {},
}) => {
  // تتبع التقييمات المعتمدة والمحذوفة محلياً
  const [approvedReviews, setApprovedReviews] = useState(new Set());
  const [deletedReviews, setDeletedReviews] = useState(new Set());

  const handleApproveWithUpdate = async (reviewId, approve = true) => {
    await handleApproveReview(reviewId, approve, (approvedId) => {
      if (approve) {
        setApprovedReviews(prev => new Set([...prev, approvedId]));
      }
    });
  };

  const handleDeleteCommentWithConfirm = async (reviewId, commentId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا التعليق؟')) {
      await handleDeleteComment(reviewId, commentId);
    }
  };

  const handleCommentsUpdate = (reviewId, newComments) => {
    // تحديث التعليقات في الوقت الفعلي
    console.log('Updating comments for review:', reviewId, newComments);
    // يمكن إضافة logic لتحديث التعليقات في الـ state
    // أو استدعاء handleFetchComments لإعادة جلب التعليقات
    handleFetchComments(reviewId);
  };

  const handleDeleteReviewWithConfirm = async (reviewId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا التقييم نهائياً؟ سيتم حذف جميع التعليقات المرتبطة به أيضاً.')) {
      await handleDeleteReview(reviewId, (deletedId) => {
        setDeletedReviews(prev => new Set([...prev, deletedId]));
      });
    }
  };

  return (
  <div>
    <div className="flex items-center gap-3 mb-6">
      <div className="w-10 h-10 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-xl flex items-center justify-center">
        <svg
          className="w-5 h-5 text-white"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
          />
        </svg>
      </div>
      <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
        تقييمات الطلاب
      </h2>
      <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-full text-sm font-medium">
        {reviews ? reviews.filter(review => !deletedReviews.has(review.id)).length : 0} تقييم
      </span>
    </div>

    {reviews && reviews.length > 0 ? (
      <div className="space-y-6">
        {reviews.filter(review => !deletedReviews.has(review.id)).map((review) => (
          <div
            key={review.id}
            className="bg-gradient-to-r from-white to-gray-50 dark:from-gray-700 dark:to-gray-800 rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-gray-600 hover:shadow-xl transition-all duration-300"
          >
            <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
              {/* Review Content */}
              <div className="flex-1">
                {/* User Info and Rating */}
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-lg">
                      {(review.user?.username || "طالب مجهول").charAt(0)}
                    </span>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-bold text-gray-800 dark:text-white text-lg">
                      {review.user?.username || "طالب مجهول"}
                    </h3>
                    <div className="flex items-center gap-2">
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <svg
                            key={i}
                            className={`w-4 h-4 ${
                              i < review.rating
                                ? "text-yellow-500"
                                : "text-gray-300 dark:text-gray-600"
                            }`}
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        ))}
                        <span className="text-sm text-gray-600 dark:text-gray-400 mr-2">
                          ({review.rating}/5)
                        </span>
                      </div>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {review.created_at &&
                          review.created_at.replace("T", " ").substring(0, 16)}
                      </span>
                    </div>
                  </div>
                  {/* Approval Status */}
                  <div
                    className={`px-3 py-1 rounded-full text-xs font-medium ${
                      (review.is_approved || approvedReviews.has(review.id))
                        ? "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200"
                        : "bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200"
                    }`}
                  >
                    {(review.is_approved || approvedReviews.has(review.id)) ? "معتمد" : "في الانتظار"}
                  </div>
                </div>

                {/* Review Comment */}
                <div className="mb-4">
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed bg-gray-50 dark:bg-gray-700 p-4 rounded-xl">
                    "{review.comment}"
                  </p>
                </div>

                {/* Instructor Reply */}
                {review.reply && (
                  <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
                    <div className="flex items-center gap-2 mb-2">
                      <svg
                        className="w-4 h-4 text-blue-600 dark:text-blue-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"
                        />
                      </svg>
                      <span className="font-bold text-blue-800 dark:text-blue-300">
                        {courseData.instructor?.username || "المدرب"}:
                      </span>
                    </div>
                    <p className="text-blue-700 dark:text-blue-300">
                      {review.reply}
                    </p>
                  </div>
                )}

                {/* التعليقات الشجرية - zaki alkholy */}
                <InstructorReviewComments
                  review={review}
                  comments={reviewComments[review.id] || []}
                  commentText={commentText}
                  setCommentText={setCommentText}
                  replyText={replyText}
                  setReplyText={setReplyText}
                  showReplyForm={showReplyForm}
                  onAddComment={handleAddComment}
                  onReply={handleAddComment}
                  onDeleteComment={handleDeleteCommentWithConfirm}
                  onCommentsUpdate={handleCommentsUpdate}
                  onToggleReplyForm={onToggleReplyForm}
                  commentLoading={commentLoading}
                  expandedComments={expandedComments}
                  onToggleExpand={onToggleExpand}
                  currentUser={currentUser}
                />
              </div>

              {/* Action Buttons */}
              {(review.is_approved || approvedReviews.has(review.id)) && (
                <div className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-4 py-2 rounded-lg font-medium">
                  ✅ تم اعتماد التقييم
                </div>
              )}

              {!review.is_approved && !approvedReviews.has(review.id) && (
                <div className="flex flex-col gap-3 lg:ml-4">
                  <button
                    className="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                    disabled={approving}
                    onClick={() => handleApproveWithUpdate(review.id)}
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    موافقة
                  </button>

                </div>
              )}

              {/* زر حذف التقييم - للمعلم فقط */}
              {currentUser?.is_instructor && (
                <div className="mt-3">
                  <button
                    className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 text-sm"
                    onClick={() => handleDeleteReviewWithConfirm(review.id)}
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                      />
                    </svg>
                    حذف التقييم
                  </button>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    ) : (
      <div className="text-center py-12">
        <div className="w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg
            className="w-12 h-12 text-gray-400 dark:text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
            />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          لا توجد تقييمات بعد
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          عندما يقوم الطلاب بتقييم الكورس، ستظهر تقييماتهم هنا
        </p>
      </div>
    )}
  </div>
);
};

export default ReviewList;
