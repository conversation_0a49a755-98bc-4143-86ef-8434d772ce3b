import { NextRequest, NextResponse } from "next/server";

// فك الـ JWT بدون مكتبات خارجية
function parseJwt(token: string) {
  try {
    const base64Url = token.split(".")[1];
    const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split("")
        .map((c) => "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2))
        .join("")
    );

    return JSON.parse(jsonPayload);
  } catch (error) {
    return null;
  }
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  if (
    pathname.startsWith("/instructor") &&
    !/^\/instructor\/[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(
      pathname
    )
  ) {
    const parts = pathname.split("/"); // ['', 'instructor', 'something']
    const secondPart = parts[2]; // الجزء اللي بعد instructor

    // لو ده مسار عرض بروفايل instructor (رقم فقط) ← نسمح بالمرور
    const isInstructorProfile = secondPart && /^\d+$/.test(secondPart);

    if (!isInstructorProfile) {
      const token = request.cookies.get("authToken")?.value;

      if (!token) {
        return NextResponse.redirect(new URL("/login", request.url));
      }

      const decoded = parseJwt(token);

      if (!decoded?.is_instructor) {
        return NextResponse.redirect(new URL("/", request.url));
      }
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: ["/instructor/:path*"],
};
