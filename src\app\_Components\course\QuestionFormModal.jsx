import React, { useRef, useEffect, useState } from "react";
import Modal from "../Modal";
import ReactCrop from "react-image-crop";
import "react-image-crop/dist/ReactCrop.css";

export default function QuestionFormModal({
  isOpen,
  onClose,
  onSubmit,
  quizId,
  formState,
  setFormState,
  answers,
  loading,
  quiz, // إضافة quiz ك prop
  existingOrders = [],
}) {
  // الإجابات

  const [parentAnswers, setParentAnswers] = useState(answers || null); // الإجابات الأصلية
  // الصورة
  const [image, setImage] = useState(null); // File
  const [imagePreview, setImagePreview] = useState(null); // DataURL
  const [showCropper, setShowCropper] = useState(false);
  const [crop, setCrop] = useState({
    unit: "%",
    width: 50,
    height: 50,
    x: 25,
    y: 25,
  });
  const [completedCrop, setCompletedCrop] = useState(null);
  const [croppedBlob, setCroppedBlob] = useState(null);
  const imgRef = useRef();
  const formRef = useRef();
  const isDuplicateOrder =
    existingOrders.includes(Number(formState.order)) &&
    quiz.questions?.find((q) => q.id === formState.id)?.order !==
      Number(formState.order);

  // تهيئة الإجابات والصورة عند أول فتح للمودال فقط
  useEffect(() => {
    if (!isOpen) return;
    if (isOpen) {
      // لو بنضيف سؤال جديد (مافيش formState.order)
      if (
        (formState.order === undefined ||
          formState.order === null ||
          formState.order === "") &&
        quiz?.questions?.length >= 0
      ) {
        const usedOrders = quiz.questions.map((q) => q.order);
        const maxOrder = usedOrders.length > 0 ? Math.max(...usedOrders) : 0;
        setFormState((f) => ({ ...f, order: maxOrder + 1 }));
      }
    }
    // لو عندك إجابات جاية من السؤال (تعديل)، استخدمها
    if (Array.isArray(answers) && answers.length > 0) {
      setParentAnswers(answers);
    } else {
      // لو بتضيف سؤال جديد، هنعتمد على نوع السؤال
      if (formState.question_type === "mcq") {
        setParentAnswers([
          { text: "", is_correct: false },
          { text: "", is_correct: false },
        ]);
      } else if (formState.question_type === "true_false") {
        setParentAnswers([
          { text: "صح", is_correct: true },
          { text: "خطأ", is_correct: false },
        ]);
      }
    }

    // الصورة
    const imgUrl = formState.image_url || formState.imageUrl || null;
    if (imgUrl) {
      setImage(null);
      setImagePreview(imgUrl);
      setCroppedBlob(null);
    } else {
      setImage(null);
      setImagePreview(null);
      setCroppedBlob(null);
    }
  }, [isOpen, formState.id, formState.question_type, answers, quiz]);

  useEffect(() => {
    if (!isOpen) return;

    // فقط للأسئلة الجديدة (بدون id) - لا نعيد تعيين الإجابات للأسئلة الموجودة
    if (!formState.id) {
      if (formState.question_type === "mcq") {
        setParentAnswers((prev) =>
          prev && prev.length
            ? prev
            : [
                { text: "", is_correct: false },
                { text: "", is_correct: false },
              ]
        );
      } else if (formState.question_type === "true_false") {
        setParentAnswers([
          { text: "صح", is_correct: true },
          { text: "خطأ", is_correct: false },
        ]);
      }
    }
  }, [formState.question_type, formState.id]);

  // تهيئة معاينة الصورة عند اختيار صورة
  useEffect(() => {
    if (image) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target.result);
        setShowCropper(true); // فتح الكروب تلقائياً عند اختيار صورة
      };
      reader.readAsDataURL(image);
    }
    // لا تمسح imagePreview إلا لو المستخدم اختار صورة جديدة
    // else {
    //   setImagePreview(null);
    //   setCroppedBlob(null);
    // }
  }, [image]);

  // crop الصورة
  const getCroppedImg = async () => {
    if (!imgRef.current || !completedCrop?.width || !completedCrop?.height)
      return;

    try {
      const canvas = document.createElement("canvas");
      const scaleX = imgRef.current.naturalWidth / imgRef.current.width;
      const scaleY = imgRef.current.naturalHeight / imgRef.current.height;
      canvas.width = completedCrop.width;
      canvas.height = completedCrop.height;
      const ctx = canvas.getContext("2d");

      // تأكد من أن الصورة محملة بشكل صحيح
      if (imgRef.current.complete && imgRef.current.naturalHeight !== 0) {
        ctx.drawImage(
          imgRef.current,
          completedCrop.x * scaleX,
          completedCrop.y * scaleY,
          completedCrop.width * scaleX,
          completedCrop.height * scaleY,
          0,
          0,
          completedCrop.width,
          completedCrop.height
        );

        return new Promise((resolve, reject) => {
          canvas.toBlob(
            (blob) => {
              if (blob) {
                setCroppedBlob(blob);
                resolve(blob);
              } else {
                reject(new Error("فشل في إنشاء الصورة المقصوصة"));
              }
            },
            "image/jpeg",
            0.8
          );
        });
      } else {
        throw new Error("الصورة غير محملة بشكل صحيح");
      }
    } catch (error) {
      console.error("خطأ في قص الصورة:", error);
      alert("حدث خطأ في قص الصورة. تأكد من أن الصورة محملة بشكل صحيح.");
      return null;
    }
  };

  // عند تطبيق crop
  const handleApplyCrop = async () => {
    try {
      const result = await getCroppedImg();
      if (result) {
        setShowCropper(false);
      }
    } catch (error) {
      console.error("خطأ في تطبيق القص:", error);
      alert("حدث خطأ في تطبيق القص. حاول مرة أخرى.");
    }
  };

  // حذف الصورة
  const handleRemoveImage = () => {
    setImage(null);
    setImagePreview(null);
    setCroppedBlob(null);
    setShowCropper(false);
  };

  // عند الحفظ
  const handleSubmit = (e) => {
    e.preventDefault();
    if (!parentAnswers.some((a) => a.is_correct)) {
      alert("يجب اختيار إجابة صحيحة قبل الحفظ");
      return;
    }
    // يمكنك هنا إرسال croppedBlob مع باقي البيانات إذا أردت رفع الصورة
    if (isDuplicateOrder) {
      alert("⚠️ هذا الترتيب مستخدم بالفعل. يرجى اختيار رقم ترتيب مختلف.");
      return;
    }

    console.log("Submitting question with formState:", formState);
    console.log("Submitting question with answers:", parentAnswers);

    onSubmit(quizId, {
      answers: parentAnswers,
      image: croppedBlob,
    });
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} width="max-w-2xl">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <svg
              className="w-8 h-8 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            {formState.id ? "تعديل السؤال" : "إضافة سؤال جديد"}
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {formState.id
              ? "قم بتعديل بيانات السؤال"
              : "أضف سؤالاً جديداً للاختبار"}
          </p>
        </div>

        <form ref={formRef} onSubmit={handleSubmit} className="space-y-6">
          {/* نص السؤال */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              نص السؤال
            </label>
            <textarea
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
              placeholder="اكتب نص السؤال هنا..."
              value={formState.text || ""}
              onChange={(e) =>
                setFormState((f) => ({ ...f, text: e.target.value }))
              }
              rows={3}
              required
            />
          </div>

          {/* نوع السؤال */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              نوع السؤال
            </label>
            <select
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              value={formState.question_type || "mcq"}
              onChange={(e) =>
                setFormState((f) => ({ ...f, question_type: e.target.value }))
              }
            >
              <option value="mcq">اختيار من متعدد</option>
              <option value="true_false">صح أو خطأ</option>
            </select>
          </div>

          {/* حقل الدرجة - يظهر فقط للامتحانات */}
          {quiz?.quiz_type === "exam" && (
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                الدرجة
              </label>
              <input
                type="number"
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                placeholder="أدخل درجة السؤال"
                value={formState.points || ""}
                onChange={(e) =>
                  setFormState((f) => ({ ...f, points: e.target.value }))
                }
                min="1"
                required
              />
            </div>
          )}

          {/* ملاحظة للواجبات */}
          {quiz?.quiz_type === "assignment" && (
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800 rounded-xl p-4">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-green-500 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
                  <svg
                    className="w-3 h-3 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <div>
                  <h4 className="font-medium text-green-800 dark:text-green-300 mb-1">
                    ملاحظة حول الواجبات
                  </h4>
                  <p className="text-sm text-green-700 dark:text-green-400">
                    كل سؤال في الواجب يحصل على درجة واحدة تلقائياً
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* ترتيب السؤال */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              ترتيب السؤال
            </label>
            <input
              type="number"
              className={`w-full px-4 py-3 border rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:border-transparent transition-all duration-200 ${
                isDuplicateOrder
                  ? "border-red-500 dark:border-red-400 bg-red-50 dark:bg-red-900/20 focus:ring-red-500"
                  : "border-gray-300 dark:border-gray-600 focus:ring-blue-500"
              }`}
              placeholder="رقم ترتيب السؤال"
              value={formState.order || ""}
              onChange={(e) =>
                setFormState((f) => ({ ...f, order: Number(e.target.value) }))
              }
              min={1}
            />
            {isDuplicateOrder && (
              <div className="bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 border border-red-200 dark:border-red-800 rounded-xl p-3">
                <div className="flex items-start gap-3">
                  <div className="w-5 h-5 bg-red-500 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
                    <svg
                      className="w-3 h-3 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                      />
                    </svg>
                  </div>
                  <p className="text-sm text-red-700 dark:text-red-400">
                    هذا الترتيب مستخدم بالفعل لسؤال آخر. يرجى اختيار رقم مختلف.
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* رفع صورة السؤال */}
          <div className="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-blue-900/20 border border-gray-200 dark:border-gray-700 rounded-xl p-6">
            <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
              <svg
                className="w-5 h-5 text-blue-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
              صورة السؤال (اختيارية)
            </label>
            {!imagePreview ? (
              <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl p-6 text-center hover:border-blue-400 dark:hover:border-blue-500 transition-colors duration-200">
                <input
                  type="file"
                  accept="image/*"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:file:bg-blue-900/20 dark:file:text-blue-400 dark:hover:file:bg-blue-900/30 transition-all duration-200"
                  onChange={(e) => {
                    const file = e.target.files[0];
                    if (file) setImage(file);
                  }}
                />
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                  اختر صورة لإضافتها للسؤال
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="relative">
                  <img
                    src={
                      croppedBlob
                        ? URL.createObjectURL(croppedBlob)
                        : imagePreview
                    }
                    alt="معاينة الصورة"
                    crossOrigin="anonymous"
                    className="max-w-full h-40 object-contain border border-gray-200 dark:border-gray-600 rounded-xl mx-auto bg-white dark:bg-gray-800"
                  />
                </div>
                <div className="flex gap-3 justify-center">
                  <button
                    type="button"
                    className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2"
                    onClick={handleRemoveImage}
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                      />
                    </svg>
                    حذف الصورة
                  </button>
                  <button
                    type="button"
                    className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2"
                    onClick={() => setShowCropper(true)}
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                      />
                    </svg>
                    تعديل الصورة
                  </button>
                </div>
              </div>
            )}
          </div>
          {/* cropper */}
          {showCropper && imagePreview && (
            <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-fade-in">
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden border border-gray-200 dark:border-gray-700 animate-slide-up">
                {/* Header */}
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                        <svg
                          className="w-5 h-5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                          />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold">تعديل الصورة</h3>
                        <p className="text-blue-100 text-sm">
                          اسحب لتحديد المنطقة المطلوبة
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
                  <div className="mb-6 flex justify-center">
                    <ReactCrop
                      crop={crop}
                      onChange={setCrop}
                      onComplete={setCompletedCrop}
                      aspect={undefined}
                      minWidth={50}
                      minHeight={50}
                      keepSelection={true}
                      style={{ maxWidth: "100%", maxHeight: "60vh" }}
                    >
                      <img
                        ref={imgRef}
                        src={imagePreview}
                        alt="للتعديل"
                        crossOrigin="anonymous"
                        style={{
                          maxWidth: "100%",
                          maxHeight: "60vh",
                          display: "block",
                        }}
                        className="rounded-lg"
                      />
                    </ReactCrop>
                  </div>

                  {/* Tips */}
                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4 mb-6">
                    <div className="flex items-start gap-3">
                      <div className="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
                        <svg
                          className="w-3 h-3 text-white"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-1">
                          نصائح للتعديل
                        </h4>
                        <p className="text-sm text-blue-700 dark:text-blue-400">
                          اسحب الزوايا لتغيير حجم المنطقة، واسحب المنطقة
                          لتحريكها
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-4">
                    <button
                      type="button"
                      onClick={handleApplyCrop}
                      className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                      disabled={!completedCrop}
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      تطبيق القص
                    </button>
                    <button
                      type="button"
                      onClick={() => setShowCropper(false)}
                      className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-xl font-medium hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200"
                    >
                      إلغاء
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
          {/* الإجابات */}
          <div className="bg-gradient-to-r from-gray-50 to-indigo-50 dark:from-gray-800 dark:to-indigo-900/20 border border-gray-200 dark:border-gray-700 rounded-xl p-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                <svg
                  className="w-4 h-4 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                الإجابات
              </h4>
            </div>
            {parentAnswers.map((ans, idx) => (
              <div
                key={idx}
                className={`flex items-center gap-4 mb-4 p-4 rounded-xl border transition-all duration-200 ${
                  ans.is_correct
                    ? "bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-green-200 dark:border-green-800"
                    : "bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                }`}
              >
                <div className="flex items-center gap-3 min-w-0 flex-1">
                  <div
                    className={`w-8 h-8 rounded-lg flex items-center justify-center text-sm font-bold ${
                      ans.is_correct
                        ? "bg-green-500 text-white"
                        : "bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300"
                    }`}
                  >
                    {String.fromCharCode(65 + idx)}
                  </div>
                  <input
                    type="text"
                    className="flex-1 min-w-0 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    placeholder={`الإجابة ${idx + 1}`}
                    value={ans.text}
                    disabled={formState.question_type === "true_false"}
                    onChange={(e) =>
                      setParentAnswers((prev) =>
                        prev.map((a, i) =>
                          i === idx ? { ...a, text: e.target.value } : a
                        )
                      )
                    }
                    required
                  />
                </div>
                <div className="flex items-center gap-3 whitespace-nowrap">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="correct"
                      checked={ans.is_correct}
                      onChange={() =>
                        setParentAnswers((prev) =>
                          prev.map((a, i) => ({ ...a, is_correct: i === idx }))
                        )
                      }
                      className="w-5 h-5 text-green-600 focus:ring-green-500 focus:ring-2"
                    />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      صحيحة
                    </span>
                  </label>
                  {formState.question_type === "mcq" &&
                    parentAnswers.length > 2 && (
                      <button
                        type="button"
                        className="w-8 h-8 flex items-center justify-center rounded-lg bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-900/30 transition-all duration-200"
                        onClick={() =>
                          setParentAnswers((prev) =>
                            prev.filter((_, i) => i !== idx)
                          )
                        }
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                          />
                        </svg>
                      </button>
                    )}
                </div>
              </div>
            ))}
            {formState.question_type === "mcq" && (
              <button
                type="button"
                className="bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 hover:from-blue-200 hover:to-indigo-200 dark:hover:from-blue-900/30 dark:hover:to-indigo-900/30 text-blue-700 dark:text-blue-400 px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 flex items-center gap-2 border border-blue-200 dark:border-blue-800"
                onClick={() =>
                  setParentAnswers((prev) => [
                    ...prev,
                    { text: "", is_correct: false },
                  ])
                }
                disabled={parentAnswers.length >= 6}
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                <span>إضافة اختيار جديد</span>
              </button>
            )}

            {/* تحذير الإجابة الصحيحة */}
            <div className="bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-red-500 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
                  <svg
                    className="w-3 h-3 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                    />
                  </svg>
                </div>
                <div>
                  <h4 className="font-medium text-red-800 dark:text-red-300 mb-1">
                    تنبيه مهم
                  </h4>
                  <p className="text-sm text-red-700 dark:text-red-400">
                    يجب اختيار إجابة صحيحة قبل الحفظ
                  </p>
                </div>
              </div>
            </div>
          </div>
          {/* إحصائيات الدرجات */}
          {quiz?.questions?.length >= 0 && (
            <div className="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4">
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-blue-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <svg
                    className="w-4 h-4 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-2">
                    إحصائيات الدرجات
                  </h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between items-center">
                      <span className="text-blue-700 dark:text-blue-400">
                        مجموع الدرجات الحالية:
                      </span>
                      <span className="font-bold text-blue-800 dark:text-blue-300">
                        {quiz.questions.reduce(
                          (sum, q) => sum + Number(q.points || 0),
                          0
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-blue-700 dark:text-blue-400">
                        درجة النجاح المطلوبة:
                      </span>
                      <span className="font-bold text-blue-800 dark:text-blue-300">
                        {quiz.max_score}
                      </span>
                    </div>
                    <div className="pt-2 border-t border-blue-200 dark:border-blue-700">
                      <span className="text-blue-700 dark:text-blue-400">
                        {(() => {
                          const total = quiz.questions.reduce(
                            (sum, q) => sum + Number(q.points || 0),
                            0
                          );
                          const diff = total - quiz.max_score;
                          if (diff === 0) return "✅ المجموع متطابق تمامًا";
                          if (diff > 0) return `🔺 زيادة بمقدار ${diff} درجة`;
                          return `🔻 ناقص ${Math.abs(diff)} درجة`;
                        })()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* أزرار التحكم */}
          <div className="flex gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="submit"
              className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
              disabled={loading}
            >
              {loading ? (
                <>
                  <svg
                    className="w-5 h-5 animate-spin"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    />
                  </svg>
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  حفظ السؤال
                </>
              )}
            </button>
            <button
              type="button"
              className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-xl font-medium hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200"
              onClick={onClose}
            >
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </Modal>
  );
}
