"use client";
import React from "react";

/**
 * Universal Loader Component - مكون التحميل الموحد
 * يستخدم في جميع أنحاء التطبيق لضمان التناسق
 * - zaki alkholy
 */
const UniversalLoader = ({
  size = "medium",
  variant = "spinner",
  text = "جاري التحميل...",
  showText = true,
  fullscreen = false,
  className = "",
  color = "primary",
}) => {
  // تحديد أحجام الـ spinner - zaki alkholy
  const sizeClasses = {
    small: "h-4 w-4",
    medium: "h-8 w-8",
    large: "h-12 w-12",
    xlarge: "h-16 w-16",
    xxlarge: "h-32 w-32",
  };

  // تحديد ألوان الـ spinner - zaki alkholy
  const colorClasses = {
    primary: "border-blue-500 dark:border-blue-400",
    blue: "border-blue-500 dark:border-blue-400",
    white: "border-white",
    gray: "border-gray-500 dark:border-gray-400",
  };

  // تحديد أحجام النص - zaki alkholy
  const textSizeClasses = {
    small: "text-sm",
    medium: "text-base",
    large: "text-lg",
    xlarge: "text-xl",
    xxlarge: "text-2xl",
  };

  // Spinner Component - zaki alkholy
  const Spinner = () => (
    <div
      className={`
        animate-spin rounded-full border-2 border-transparent
        bg-gradient-to-r from-blue-500 to-purple-600 dark:from-blue-400 dark:to-purple-500
        ${sizeClasses[size]}
        ${className}
      `}
      style={{
        background: `conic-gradient(from 0deg, transparent, ${
          color === "white" ? "#ffffff" : "#3b82f6"
        })`,
        borderRadius: "50%",
      }}
    >
      <div
        className={`${sizeClasses[size]} bg-white dark:bg-gray-800 rounded-full`}
        style={{ margin: "2px" }}
      ></div>
    </div>
  );

  // Dots Loader Component - zaki alkholy
  const DotsLoader = () => (
    <div className="flex space-x-1 justify-center items-center">
      <div
        className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full animate-bounce"
        style={{ animationDelay: "0ms" }}
      ></div>
      <div
        className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full animate-bounce"
        style={{ animationDelay: "150ms" }}
      ></div>
      <div
        className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full animate-bounce"
        style={{ animationDelay: "300ms" }}
      ></div>
    </div>
  );

  // Pulse Loader Component - zaki alkholy
  const PulseLoader = () => (
    <div
      className={`${sizeClasses[size]} bg-gradient-to-r from-blue-500 to-purple-600 rounded-full animate-pulse`}
    ></div>
  );

  // اختيار نوع الـ loader - zaki alkholy
  const renderLoader = () => {
    switch (variant) {
      case "dots":
        return <DotsLoader />;
      case "pulse":
        return <PulseLoader />;
      case "spinner":
      default:
        return <Spinner />;
    }
  };

  // إذا كان fullscreen - zaki alkholy
  if (fullscreen) {
    return (
      <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 animate-fade-in">
        <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-2xl border border-gray-200 dark:border-gray-700 animate-slide-up">
          <div className="flex flex-col items-center space-y-4">
            {renderLoader()}
            {showText && (
              <p
                className={`text-gray-700 dark:text-gray-300 ${textSizeClasses[size]} font-medium`}
              >
                {text}
              </p>
            )}
          </div>
        </div>
      </div>
    );
  }

  // الـ loader العادي - zaki alkholy
  return (
    <div className={`flex items-center justify-center py-8 ${className}`}>
      <div className="flex flex-col items-center space-y-4">
        {renderLoader()}
        {showText && (
          <span
            className={`text-gray-700 dark:text-gray-300 ${textSizeClasses[size]} font-medium`}
          >
            {text}
          </span>
        )}
      </div>
    </div>
  );
};

// مكونات مخصصة للاستخدامات الشائعة - zaki alkholy

// Page Loader - للصفحات الكاملة
export const PageLoader = ({ text = "جاري تحميل الصفحة..." }) => (
  <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
    <div className="text-center bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-2xl border border-gray-200 dark:border-gray-700">
      <UniversalLoader
        size="xlarge"
        text={text}
        showText={true}
        className="mb-4"
      />
    </div>
  </div>
);

// Section Loader - للأقسام داخل الصفحة
export const SectionLoader = ({ text = "جاري التحميل..." }) => (
  <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg border border-gray-200 dark:border-gray-700">
    <UniversalLoader size="large" text={text} showText={true} />
  </div>
);

// Button Loader - للأزرار
export const ButtonLoader = ({ size = "small" }) => (
  <UniversalLoader
    size={size}
    showText={false}
    color="white"
    className="mx-2"
  />
);

// Inline Loader - للاستخدام داخل النصوص
export const InlineLoader = () => (
  <UniversalLoader size="small" showText={false} className="inline-flex mx-2" />
);

export default UniversalLoader;
