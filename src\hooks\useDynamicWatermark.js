/**
 * Hook لإدارة Dynamic Watermark - زكي الخولي
 * يدير حالة وسلوك الـ watermark الديناميكي
 */

import { useState, useEffect, useCallback } from 'react';

export const useDynamicWatermark = (user, isVideoProtected = false) => {
  const [isWatermarkVisible, setIsWatermarkVisible] = useState(false);
  const [watermarkConfig, setWatermarkConfig] = useState({
    opacity: 0.8,
    fontSize: 16,
    moveInterval: 10000, // 10 ثواني
    positions: [
      { x: 20, y: 20 },           // أعلى يسار
      { x: -20, y: 20 },          // أعلى يمين
      { x: 20, y: -20 },          // أسفل يسار
      { x: -20, y: -20 },         // أسفل يمين
      { x: 0, y: 20 },            // أعلى وسط
      { x: 0, y: -20 },           // أسفل وسط
      { x: -100, y: 0 },          // وسط يمين
      { x: 100, y: 0 },           // وسط يسار
    ]
  });

  // تفعيل الـ watermark للفيديوهات المحمية فقط - زكي الخولي
  useEffect(() => {
    console.log('Hook Debug - زكي الخولي:', {
      isVideoProtected,
      user: !!user,
      userEmail: user?.email,
      userName: user?.username
    });

    if (isVideoProtected && user) {
      console.log('Setting watermark visible = true - زكي الخولي');
      setIsWatermarkVisible(true);
    } else {
      console.log('Setting watermark visible = false - زكي الخولي');
      setIsWatermarkVisible(false);
    }
  }, [isVideoProtected, user]);

  // دالة لتحديث إعدادات الـ watermark - زكي الخولي
  const updateWatermarkConfig = useCallback((newConfig) => {
    setWatermarkConfig(prev => ({
      ...prev,
      ...newConfig
    }));
  }, []);

  // دالة لإخفاء/إظهار الـ watermark مؤقتاً - زكي الخولي
  const toggleWatermark = useCallback((visible) => {
    setIsWatermarkVisible(visible);
  }, []);

  // دالة للحصول على نص الـ watermark - زكي الخولي
  const getWatermarkText = useCallback(() => {
    if (!user) return '';
    
    const email = user.email || '<EMAIL>';
    const username = user.username || 'username';
    return `${email} | ${username}`;
  }, [user]);

  // دالة للتحقق من صحة بيانات المستخدم - زكي الخولي
  const isUserDataValid = useCallback(() => {
    return user && (user.email || user.username);
  }, [user]);

  return {
    isWatermarkVisible,
    watermarkConfig,
    updateWatermarkConfig,
    toggleWatermark,
    getWatermarkText,
    isUserDataValid
  };
};
