"use client";
import React, { useState } from 'react';
import { useTheme } from '@/contexts/ThemeContext';

/**
 * Theme Toggle Component - مكون تبديل الثيم
 * يسمح للمستخدم بالتبديل بين الوضع الفاتح والداكن
 * - zaki alkholy
 */
const ThemeToggle = ({ 
  size = 'medium',
  showLabel = true,
  className = ''
}) => {
  const { isDarkMode, toggleTheme, isLoaded } = useTheme();
  const [isAnimating, setIsAnimating] = useState(false);

  // تحديد أحجام الزر - zaki alkholy
  const sizeClasses = {
    small: 'w-8 h-8 text-sm',
    medium: 'w-10 h-10 text-base',
    large: 'w-12 h-12 text-lg'
  };

  // معالجة النقر على الزر - zaki alkholy
  const handleToggle = () => {
    setIsAnimating(true);
    toggleTheme();
    
    // إنهاء الأنيميشن بعد فترة قصيرة
    setTimeout(() => {
      setIsAnimating(false);
    }, 300);
  };

  // إذا لم يتم تحميل الثيم بعد - zaki alkholy
  if (!isLoaded) {
    return (
      <div className={`${sizeClasses[size]} bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse ${className}`} />
    );
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {showLabel && (
        <span className="text-sm font-medium text-foreground">
          {isDarkMode ? 'الوضع الداكن' : 'الوضع الفاتح'}
        </span>
      )}
      
      <button
        onClick={handleToggle}
        className={`
          ${sizeClasses[size]}
          relative rounded-full border-2 border-gray-300 dark:border-gray-600
          bg-background hover:bg-gray-100 dark:hover:bg-gray-800
          transition-all duration-300 ease-in-out
          focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2
          ${isAnimating ? 'scale-110' : 'scale-100'}
        `}
        title={isDarkMode ? 'تبديل إلى الوضع الفاتح' : 'تبديل إلى الوضع الداكن'}
        aria-label={isDarkMode ? 'تبديل إلى الوضع الفاتح' : 'تبديل إلى الوضع الداكن'}
      >
        <div className="relative w-full h-full flex items-center justify-center">
          {/* أيقونة الشمس - zaki alkholy */}
          <svg
            className={`
              absolute transition-all duration-300 ease-in-out
              ${isDarkMode ? 'opacity-0 rotate-90 scale-0' : 'opacity-100 rotate-0 scale-100'}
            `}
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <circle cx="12" cy="12" r="5"/>
            <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
          </svg>

          {/* أيقونة القمر - zaki alkholy */}
          <svg
            className={`
              absolute transition-all duration-300 ease-in-out
              ${isDarkMode ? 'opacity-100 rotate-0 scale-100' : 'opacity-0 -rotate-90 scale-0'}
            `}
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
          </svg>
        </div>
      </button>
    </div>
  );
};

/**
 * Theme Toggle Switch - مفتاح تبديل الثيم على شكل switch
 * - zaki alkholy
 */
export const ThemeToggleSwitch = ({ className = '' }) => {
  const { isDarkMode, toggleTheme, isLoaded } = useTheme();

  if (!isLoaded) {
    return (
      <div className={`w-12 h-6 bg-gray-200 rounded-full animate-pulse ${className}`} />
    );
  }

  return (
    <button
      onClick={toggleTheme}
      className={`
        relative w-12 h-6 rounded-full transition-colors duration-300 ease-in-out
        focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2
        ${isDarkMode ? 'bg-primary' : 'bg-gray-300'}
        ${className}
      `}
      title={isDarkMode ? 'تبديل إلى الوضع الفاتح' : 'تبديل إلى الوضع الداكن'}
      aria-label={isDarkMode ? 'تبديل إلى الوضع الفاتح' : 'تبديل إلى الوضع الداكن'}
    >
      <div
        className={`
          absolute top-0.5 w-5 h-5 bg-white rounded-full shadow-md
          transition-transform duration-300 ease-in-out
          ${isDarkMode ? 'transform translate-x-6' : 'transform translate-x-0.5'}
        `}
      >
        <div className="w-full h-full flex items-center justify-center">
          {isDarkMode ? (
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
            </svg>
          ) : (
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="5"/>
              <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
            </svg>
          )}
        </div>
      </div>
    </button>
  );
};

export default ThemeToggle;
