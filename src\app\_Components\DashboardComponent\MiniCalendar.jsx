import React, { useState } from "react";
import Calendar from "react-calendar";
import "react-calendar/dist/Calendar.css";
import EventNoteIcon from "@mui/icons-material/EventNote";
import axios from "axios";
import Cookies from "js-cookie";
import { fetchSessionsByDate } from "../../../services/newInstructorApis";
const MiniCalendar = () => {
  const [date, setDate] = useState(new Date());
  const [sessions, setSessions] = useState([]);

  const handleDayClick = async (value) => {
    setDate(value);

    const localDate =
      value.getFullYear() +
      "-" +
      String(value.getMonth() + 1).padStart(2, "0") +
      "-" +
      String(value.getDate()).padStart(2, "0");

    const token = Cookies.get("authToken");
    if (!token) return;

    try {
      const data = await fetchSessionsByDate(localDate, token);
      setSessions(data);
      console.log("Data", data);
    } catch (e) {
      setSessions([]);
    }
  };
  const formatTo12Hour = (timeStr) => {
    if (!timeStr || typeof timeStr !== "string") return "توقيت غير صالح";

    // AM/PM Format
    const ampmMatch = timeStr.match(/(AM|PM)\s(\d{1,2}):(\d{2})/);
    if (ampmMatch) {
      return `${ampmMatch[2]}:${ampmMatch[3]} ${ampmMatch[1]}`;
    }

    // 24-hour Format
    const [hour, minute] = timeStr.split(":");
    const date = new Date();
    date.setHours(+hour);
    date.setMinutes(+minute);
    return date.toLocaleTimeString("ar-EG", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl dark:shadow-gray-900/20 border border-gray-100 dark:border-gray-700 p-6 transition-all duration-300 animate-slide-up">
      <header className="flex items-center gap-3 border-b border-gray-200 dark:border-gray-700 pb-4 mb-6 text-gray-700 dark:text-gray-300 font-bold text-lg">
        <div className="p-2 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg">
          <EventNoteIcon
            fontSize="medium"
            className="text-emerald-600 dark:text-emerald-400"
          />
        </div>
        <h3>المواعيد القادمة</h3>
      </header>

      <div className="calendar-container mb-4">
        <Calendar
          onChange={handleDayClick}
          value={date}
          locale="ar-EG"
          className="w-full rounded-xl border-0 shadow-sm dark:bg-gray-700 dark:text-white calendar-modern"
        />
      </div>

      {sessions.length > 0 && (
        <div className="mt-6 animate-slide-up">
          <h4 className="font-bold mb-4 text-gray-800 dark:text-gray-200 flex items-center gap-2">
            <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
            جلسات اليوم:
          </h4>
          <div className="space-y-3">
            {sessions.map((s, index) => (
              <div
                key={s.id}
                className="flex justify-between items-center p-3 bg-gradient-to-r from-emerald-50 to-blue-50 dark:from-emerald-900/20 dark:to-blue-900/20 rounded-xl border border-emerald-200 dark:border-emerald-700/50 hover:shadow-md transition-all duration-300 animate-slide-in-right"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex-1">
                  <p className="font-medium text-gray-800 dark:text-gray-200 text-sm">
                    {s.note}
                  </p>
                </div>
                <div className="flex items-center gap-2 text-xs font-medium text-emerald-700 dark:text-emerald-300 bg-white dark:bg-gray-800 px-3 py-1 rounded-lg shadow-sm">
                  <span>{formatTo12Hour(s.from_time)}</span>
                  <span className="text-gray-400">-</span>
                  <span>{formatTo12Hour(s.to_time)}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default MiniCalendar;
