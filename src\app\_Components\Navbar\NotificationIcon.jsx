import React, { useEffect, useState } from "react";
import NotificationsIcon from "@mui/icons-material/Notifications";
import Badge from "@mui/material/Badge";
import axios from "axios";
import Cookies from "js-cookie";
import { fetchNotifications } from "../../../services/notifications";
export default function NotificationIcon({ onClick }) {
  const [count, setCount] = useState(0);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchNotificationsData = async () => {
      const token = Cookies.get("authToken");
      if (!token) return;
      setLoading(true);
      try {
        const res = await fetchNotifications(token);

        const unread = res.filter((n) => !n.is_read).length;
        setCount(unread);
      } catch (e) {
        setCount(0);
      } finally {
        setLoading(false);
      }
    };
    fetchNotificationsData();
  }, []);
  console.log("Unread", count);

  return (
    <button onClick={onClick} className="relative">
      <Badge badgeContent={count} color="error">
        <NotificationsIcon fontSize="large" />
      </Badge>
    </button>
  );
}
