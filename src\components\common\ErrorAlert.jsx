import React from "react";

export default function ErrorAlert({ message }) {
  if (!message) return null;
  return (
    <div className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 border border-red-200 dark:border-red-700 rounded-2xl p-4 mb-6 animate-slide-down">
      <div className="flex items-center gap-3">
        <div className="w-10 h-10 bg-red-500 rounded-xl flex items-center justify-center flex-shrink-0">
          <svg
            className="w-5 h-5 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <div className="flex-1">
          <h4 className="font-medium text-red-800 dark:text-red-300 mb-1">
            حدث خطأ
          </h4>
          <p className="text-red-700 dark:text-red-400 text-sm">{message}</p>
        </div>
      </div>
    </div>
  );
}
