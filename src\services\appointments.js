// 📁 services/appointments.js
import axios from "axios";
import Cookies from "js-cookie";
import { API_BASE_URL } from '../config/api';

function getToken() {
  return Cookies.get("authToken") || "";
}

export async function fetchInstructorAvailabilities(period = null) {
  const token = getToken();
  const url = `${API_BASE_URL}/api/availabilities/` + (period ? `?period=${period}` : "");
  const response = await axios.get(url, {
    headers: {
      Authorization: `Bearer ${token}`,
      Accept: "application/json",
    },
  });
  return response.data;
}

export async function deleteAvailabilityById(id) {
  const token = getToken();
  await axios.delete(`${API_BASE_URL}/api/availabilities/${id}/`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

export async function updateAvailability(id, data) {
  const token = getToken();
  const response = await axios.patch(`${API_BASE_URL}/api/availabilities/${id}/`, data, {
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
  });
  return response.data;
}

export async function createAvailability(data) {
  const token = getToken();
  const response = await axios.post(`${API_BASE_URL}/api/availabilities/`, data, {
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
  });
  return response.data;
}
