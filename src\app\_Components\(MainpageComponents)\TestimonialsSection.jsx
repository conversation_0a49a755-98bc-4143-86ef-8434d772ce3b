"use client";
import React, { useEffect, useState } from "react";
import { Star, Quote, ChevronLeft, ChevronRight } from "lucide-react";

export default function TestimonialsSection() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  const testimonials = [
    {
      id: 1,
      name: "أحمد محمود",
      role: "مطور ويب",
      image: "/api/placeholder/80/80",
      rating: 5,
      text: "منصة رائعة ساعدتني في تطوير مهاراتي في البرمجة. المدرسون محترفون والمحتوى عالي الجودة. أنصح بها بشدة!",
      course: "تطوير تطبيقات الويب بـ React",
      location: "الرياض، السعودية",
    },
    {
      id: 2,
      name: "فاطمة علي",
      role: "مسوقة رقمية",
      image: "/api/placeholder/80/80",
      rating: 5,
      text: "تعلمت التسويق الرقمي من الصفر وأصبحت الآن أعمل في شركة كبيرة. الكورسات عملية ومفيدة جداً.",
      course: "التسويق الرقمي من الصفر",
      location: "دبي، الإمارات",
    },
    {
      id: 3,
      name: "محمد خالد",
      role: "مصمم جرافيك",
      image: "/api/placeholder/80/80",
      rating: 5,
      text: "أفضل منصة تعليمية جربتها. التفاعل مع المدرسين ممتاز والمواد التعليمية منظمة بشكل رائع.",
      course: "تصميم الجرافيك بـ Photoshop",
      location: "القاهرة، مصر",
    },
    {
      id: 4,
      name: "سارة أحمد",
      role: "مديرة مشاريع",
      image: "/api/placeholder/80/80",
      rating: 5,
      text: "حصلت على شهادة PMP بفضل الكورسات الموجودة هنا. المحتوى محدث ويواكب أحدث المعايير العالمية.",
      course: "إدارة المشاريع الاحترافية",
      location: "عمان، الأردن",
    },
    {
      id: 5,
      name: "عمر حسن",
      role: "مهندس ذكاء اصطناعي",
      image: "/api/placeholder/80/80",
      rating: 5,
      text: "دخلت مجال الذكاء الاصطناعي من خلال هذه المنصة. الشرح واضح والأمثلة العملية مفيدة جداً.",
      course: "الذكاء الاصطناعي للمبتدئين",
      location: "الكويت، الكويت",
    },
    {
      id: 6,
      name: "ليلى محمد",
      role: "مترجمة",
      image: "/api/placeholder/80/80",
      rating: 5,
      text: "طورت مهاراتي في اللغة الإنجليزية بشكل كبير. الآن أعمل كمترجمة محترفة في شركة دولية.",
      course: "اللغة الإنجليزية للأعمال",
      location: "بيروت، لبنان",
    },
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % testimonials.length);
  };

  const prevSlide = () => {
    setCurrentSlide(
      (prev) => (prev - 1 + testimonials.length) % testimonials.length
    );
  };

  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(nextSlide, 5000);
    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-5 h-5 ${
          i < rating
            ? "text-yellow-400 fill-current"
            : "text-gray-300 dark:text-gray-600"
        }`}
      />
    ));
  };

  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 via-purple-50 to-indigo-50 dark:from-gray-900 dark:via-purple-900/20 dark:to-indigo-900/20">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              ماذا يقول
            </span>
            <br />
            طلابنا؟
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            اكتشف تجارب طلابنا الناجحة وكيف غيرت حياتهم المهنية
          </p>
        </div>

        {/* Testimonials Carousel */}
        <div
          className="relative max-w-6xl mx-auto"
          onMouseEnter={() => setIsAutoPlaying(false)}
          onMouseLeave={() => setIsAutoPlaying(true)}
        >
          {/* Main Testimonial */}
          <div className="relative overflow-hidden rounded-3xl">
            <div
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(${currentSlide * -100}%)` }}
            >
              {testimonials.map((testimonial, index) => (
                <div key={testimonial.id} className="w-full flex-shrink-0">
                  <div className="bg-white dark:bg-gray-800 rounded-3xl shadow-2xl p-8 md:p-12 mx-4">
                    <div className="grid md:grid-cols-3 gap-8 items-center">
                      {/* Quote Icon */}
                      <div className="md:col-span-1 text-center">
                        <div className="relative">
                          <Quote className="w-20 h-20 text-blue-200 dark:text-blue-800 mx-auto mb-4" />
                          <div className="absolute inset-0 flex items-center justify-center">
                            <Quote className="w-12 h-12 text-blue-600 dark:text-blue-400" />
                          </div>
                        </div>
                      </div>

                      {/* Testimonial Content */}
                      <div className="md:col-span-2">
                        {/* Rating */}
                        <div className="flex items-center justify-center md:justify-start mb-6">
                          {renderStars(testimonial.rating)}
                        </div>

                        {/* Text */}
                        <blockquote className="text-xl md:text-2xl text-gray-700 dark:text-gray-300 leading-relaxed mb-8 text-center md:text-right">
                          "{testimonial.text}"
                        </blockquote>

                        {/* Author Info */}
                        <div className="flex items-center justify-center md:justify-start space-x-4 space-x-reverse">
                          {/* Avatar */}
                          <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center">
                            <span className="text-white text-xl font-bold">
                              {testimonial.name.charAt(0)}
                            </span>
                          </div>

                          {/* Details */}
                          <div className="text-center md:text-right">
                            <h4 className="text-lg font-bold text-gray-900 dark:text-white">
                              {testimonial.name}
                            </h4>
                            <p className="text-blue-600 dark:text-blue-400 font-medium">
                              {testimonial.role}
                            </p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {testimonial.location}
                            </p>
                            <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                              كورس: {testimonial.course}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Navigation Buttons */}
          <button
            onClick={prevSlide}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:shadow-xl flex items-center justify-center text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-300 hover:scale-110"
          >
            <ChevronRight className="w-6 h-6" />
          </button>

          <button
            onClick={nextSlide}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:shadow-xl flex items-center justify-center text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-300 hover:scale-110"
          >
            <ChevronLeft className="w-6 h-6" />
          </button>

          {/* Dots Indicator */}
          <div className="flex justify-center mt-8 space-x-2 space-x-reverse">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentSlide
                    ? "bg-blue-600 dark:bg-blue-400 scale-125"
                    : "bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500"
                }`}
              />
            ))}
          </div>
        </div>

        {/* Bottom Stats */}
        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div>
            <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">
              98%
            </div>
            <div className="text-gray-600 dark:text-gray-300 text-sm">
              معدل الرضا
            </div>
          </div>
          <div>
            <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">
              15,000+
            </div>
            <div className="text-gray-600 dark:text-gray-300 text-sm">
              طالب سعيد
            </div>
          </div>
          <div>
            <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">
              4.9
            </div>
            <div className="text-gray-600 dark:text-gray-300 text-sm">
              متوسط التقييم
            </div>
          </div>
          <div>
            <div className="text-3xl font-bold text-orange-600 dark:text-orange-400 mb-2">
              85%
            </div>
            <div className="text-gray-600 dark:text-gray-300 text-sm">
              معدل الإكمال
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
