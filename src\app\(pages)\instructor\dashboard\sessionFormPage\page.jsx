"use client";
import React from "react";
/**
 * SessionFormPage
 * Responsive Arabic form page to add a new individual session.
 * Uses Tailwind CSS.
 *
 * Responsive breakpoints observed: mobile (320-767px), desktop (768-1439px), large desktop (1440+).
 * RTL layout.
 * Icons from Google Material Icons as inline SVG.
 */

const InfoIcon = () => (
  <svg
    className="w-5 h-5 text-gray-600"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    viewBox="0 0 24 24"
    aria-hidden="true"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M13 16h-1v-4h-1m1-4h.01M12 20.25c4.556 0 8.25-3.694 8.25-8.25S16.556 3.75 12 3.75 3.75 7.444 3.75 12 7.444 20.25 12 20.25z"
    />
  </svg>
);

const ShareIcon = () => (
  <svg
    className="w-5 h-5"
    fill="currentColor"
    viewBox="0 0 24 24"
    aria-hidden="true"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M15 8a3 3 0 00-2.824 2H9a3 3 0 000 6h3a3 3 0 10-2.824-4H9a1 1 0 110-2h3.176A3.001 3.001 0 1015 8zM5 19a2 2 0 100-4 2 2 0 000 4zm14-10a2 2 0 11-4 0 2 2 0 014 0zM11 12a2 2 0 10-4 0 2 2 0 004 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z" />
  </svg>
);

export default function SessionFormPage() {
  return (
    <div
      dir="rtl"
      className="min-h-screen bg-gray-50 p-4 md:p-8 lg:px-16 lg:py-12 font-sans text-gray-900"
    >
      {/* Header */}
      {/* <header className="flex flex-col md:flex-row md:justify-between md:items-center mb-6">
        <div className="flex items-center gap-3 justify-start md:justify-end mb-2 md:mb-0">
          <button
            type="button"
            aria-label="مشاركة صفحتي"
            className="flex items-center gap-2 bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-300 transition"
          >
            <ShareIcon />
            <span>مشاركة صفحتي</span>
          </button>

          <button
            type="button"
            aria-label="الأسئلة الشائعة"
            className="flex items-center gap-2 text-gray-700 text-sm hover:underline focus:outline-none"
          >
            <InfoIcon />
            <span>الأسئلة الشائعة</span>
          </button>
        </div>

        <nav
          aria-label="breadcrumb"
          className="text-gray-500 text-sm truncate max-w-full md:max-w-xs"
        >
          <ol className="flex flex-row gap-1 items-center whitespace-nowrap">
            <li>حسابي</li>
            <li>&bull;</li>
            <li>جلسات فردية</li>
            <li>&bull;</li>
            <li
              className="text-gray-900 font-semibold truncate"
              title="أضف جلسة فردية جديدة"
            >
              أضف جلسة فردية جديدة
            </li>
          </ol>
          <p className="text-gray-400 text-xs mt-1 truncate max-w-full md:max-w-xs">
            أضف جلسة فردية جديدة
          </p>
        </nav>
      </header> */}

      {/* Form card */}
      <main>
        <section className="bg-white rounded-xl shadow-md p-6 max-w-4xl mx-auto">
          <h2 className="text-right text-lg font-semibold mb-4">
            أضف جلسة فردية جديدة
          </h2>

          <form
            className="flex flex-col gap-6"
            onSubmit={(e) => {
              e.preventDefault(); /* handle form submission here */
            }}
          >
            {/* عنوان المنتج */}
            <div>
              <input
                type="text"
                required
                placeholder="عنوان المنتج (مطلوب)"
                className="w-full rounded-md border border-gray-300 px-4 py-3 text-gray-700 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                aria-required="true"
                aria-label="عنوان المنتج"
              />
            </div>

            {/* وصف المنتج */}
            <div className="flex flex-col">
              <textarea
                rows={6}
                required
                placeholder="وصف المنتج (مطلوب)"
                className="w-full rounded-md border border-gray-300 px-4 py-3 text-gray-700 placeholder-gray-400 resize-y focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                aria-required="true"
                aria-label="وصف المنتج"
              />
              <p className="text-xs text-gray-500 mt-1">
                أدخل وصف وافي لمساعدة العملاء على فهم محتوى هذا المنتج
              </p>
            </div>

            {/* Button container */}
            <div className="flex justify-end">
              <button
                type="submit"
                className="bg-indigo-600 text-white text-sm font-medium rounded-md px-6 py-3 hover:bg-indigo-700 transition focus:outline-none focus:ring-2 focus:ring-indigo-400"
              >
                التالي
              </button>
            </div>
          </form>
        </section>
      </main>
    </div>
  );
}
