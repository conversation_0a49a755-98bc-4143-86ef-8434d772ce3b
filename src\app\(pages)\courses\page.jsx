"use client";
import React, { useState, useEffect } from "react";
import Link from "next/link";
import { useSelector } from "react-redux";
import { selectCurrentUser } from "../../../store/authSlice";
import Cookies from "js-cookie";
import {
  Search,
  Filter,
  Star,
  Clock,
  Users,
  ChevronLeft,
  AlertCircle,
  RefreshCw,
  BookOpen,
  Grid,
  List,
} from "lucide-react";
import { fetchAllCourses, fetchCategories } from "../../../services/courses";
import { API_BASE_URL } from "../../../config/api";
import { useRouter } from "next/navigation";

// مكون زر بدء الكورس مع التحقق من تسجيل الدخول - zaki alkholy
function CourseStartButton({ course }) {
  const router = useRouter();
  const user = useSelector(selectCurrentUser);

  const handleStartCourse = () => {
    // التحقق من تسجيل الدخول
    if (!user) {
      // توجيه لصفحة تسجيل الدخول
      router.push("/login");
      return;
    }

    // توجيه لصفحة الكورس باستخدام slug
    router.push(`/student/course/${course.slug}`);
  };

  return (
    <button
      onClick={handleStartCourse}
      className="group/btn inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-medium hover:shadow-lg transform hover:scale-105 transition-all duration-300"
    >
      <span>ابدأ الآن</span>
      <ChevronLeft className="w-4 h-4 mr-2 transform group-hover/btn:-translate-x-1 transition-transform duration-300" />
    </button>
  );
}

export default function CoursesPage() {
  const user = useSelector(selectCurrentUser);
  const theUrl = "https://res.cloudinary.com/djwhgnwp5/";

  // حالات البيانات
  const [courses, setCourses] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // حالات الفلترة والبحث
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("الكل");
  const [selectedLevel, setSelectedLevel] = useState("الكل");
  const [sortBy, setSortBy] = useState("newest");

  // حالات العرض والتصفح
  const [viewMode, setViewMode] = useState("grid");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCourses, setTotalCourses] = useState(0);
  const [pageSize] = useState(12);

  // حالات التحميل
  const [searchLoading, setSearchLoading] = useState(false);

  // جلب البيانات الأولية
  useEffect(() => {
    loadInitialData();
  }, []);

  // جلب الكورسات عند تغيير الفلاتر
  useEffect(() => {
    if (categories.length > 0) {
      loadCourses();
    }
  }, [selectedCategory, selectedLevel, sortBy, currentPage, searchTerm]);

  // دالة جلب البيانات الأولية
  const loadInitialData = async () => {
    try {
      setLoading(true);
      setError(null);

      // جلب الفئات والكورسات بشكل متوازي - بدون token
      const [categoriesData, coursesData] = await Promise.all([
        fetchCategories(),
        fetchAllCourses({ page: 1, pageSize }),
      ]);

      setCategories(categoriesData);
      setCourses(coursesData.results || coursesData || []);
      setTotalPages(Math.ceil((coursesData.count || 0) / pageSize));
      setTotalCourses(coursesData.count || 0);
    } catch (err) {
      console.error("خطأ في جلب البيانات الأولية:", err);
      setError(err.message || "حدث خطأ أثناء تحميل الكورسات");
    } finally {
      setLoading(false);
    }
  };

  // دالة جلب الكورسات مع الفلترة - بدون token
  const loadCourses = async () => {
    try {
      setSearchLoading(true);

      const options = {
        page: currentPage,
        pageSize,
        category: selectedCategory === "الكل" ? null : selectedCategory,
        level: selectedLevel === "الكل" ? null : selectedLevel,
        search: searchTerm || null,
      };

      const coursesData = await fetchAllCourses(options);

      setCourses(coursesData.results || coursesData || []);
      setTotalPages(Math.ceil((coursesData.count || 0) / pageSize));
      setTotalCourses(coursesData.count || 0);
    } catch (err) {
      console.error("خطأ في جلب الكورسات:", err);
      setError(err.message || "حدث خطأ أثناء تحميل الكورسات");
    } finally {
      setSearchLoading(false);
    }
  };

  // دالة إعادة المحاولة
  const handleRetry = () => {
    loadInitialData();
  };

  // دالة البحث
  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    loadCourses();
  };

  // دوال مساعدة لمعالجة البيانات
  const getImageUrl = (thumbnailPath) => {
    if (!thumbnailPath) return "/api/placeholder/300/200";

    if (
      thumbnailPath.startsWith("http://") ||
      thumbnailPath.startsWith("https://")
    ) {
      return thumbnailPath;
    }

    return `${API_BASE_URL}${
      thumbnailPath.startsWith("/") ? thumbnailPath : `/${thumbnailPath}`
    }`;
  };

  const formatPrice = (price, currency = "ر.س") => {
    if (!price) return "مجاني";
    return `${price} ${currency}`;
  };

  const formatDuration = (duration) => {
    if (!duration) return "غير محدد";
    if (typeof duration === "string") return duration;

    const hours = Math.floor(duration / 60);
    const minutes = duration % 60;

    if (hours > 0) {
      return `${hours} ساعة${minutes > 0 ? ` و ${minutes} دقيقة` : ""}`;
    }
    return `${minutes} دقيقة`;
  };

  const renderStars = (rating) => {
    const numRating = parseFloat(rating) || 0;
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < Math.floor(numRating)
            ? "text-yellow-400 fill-current"
            : i < numRating
            ? "text-yellow-400 fill-current opacity-50"
            : "text-gray-300 dark:text-gray-600"
        }`}
      />
    ));
  };

  const levels = [
    { value: "الكل", label: "جميع المستويات" },
    { value: "beginner", label: "مبتدئ" },
    { value: "intermediate", label: "متوسط" },
    { value: "advanced", label: "متقدم" },
  ];

  const sortOptions = [
    { value: "newest", label: "الأحدث" },
    { value: "oldest", label: "الأقدم" },
    { value: "price_low", label: "السعر: من الأقل للأعلى" },
    { value: "price_high", label: "السعر: من الأعلى للأقل" },
    { value: "rating", label: "التقييم" },
    { value: "popular", label: "الأكثر شعبية" },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center mb-8">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                جميع الكورسات
              </span>
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              اكتشف مجموعة واسعة من الكورسات التعليمية في مختلف المجالات
            </p>
          </div>

          {/* Search and Filters */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-2xl p-6">
            {/* Search Bar */}
            <form onSubmit={handleSearch} className="mb-6">
              <div className="relative">
                <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="ابحث عن كورس..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pr-12 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                />
                <button
                  type="submit"
                  disabled={searchLoading}
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-blue-600 text-white px-4 py-1.5 rounded-md hover:bg-blue-700 transition-colors duration-300 disabled:opacity-50"
                >
                  {searchLoading ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    "بحث"
                  )}
                </button>
              </div>
            </form>

            {/* Filters */}
            <div className="grid md:grid-cols-4 gap-4">
              {/* Category Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  الفئة
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => {
                    setSelectedCategory(e.target.value);
                    setCurrentPage(1);
                  }}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                >
                  <option value="الكل">جميع الفئات</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.name}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Level Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  المستوى
                </label>
                <select
                  value={selectedLevel}
                  onChange={(e) => {
                    setSelectedLevel(e.target.value);
                    setCurrentPage(1);
                  }}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                >
                  {levels.map((level) => (
                    <option key={level.value} value={level.value}>
                      {level.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Sort Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  ترتيب حسب
                </label>
                <select
                  value={sortBy}
                  onChange={(e) => {
                    setSortBy(e.target.value);
                    setCurrentPage(1);
                  }}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                >
                  {sortOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* View Mode */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  طريقة العرض
                </label>
                <div className="flex gap-2">
                  <button
                    onClick={() => setViewMode("grid")}
                    className={`flex-1 p-2 rounded-lg transition-colors duration-300 ${
                      viewMode === "grid"
                        ? "bg-blue-600 text-white"
                        : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600"
                    }`}
                  >
                    <Grid className="w-4 h-4 mx-auto" />
                  </button>
                  <button
                    onClick={() => setViewMode("list")}
                    className={`flex-1 p-2 rounded-lg transition-colors duration-300 ${
                      viewMode === "list"
                        ? "bg-blue-600 text-white"
                        : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600"
                    }`}
                  >
                    <List className="w-4 h-4 mx-auto" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        {/* Results Info */}
        {!loading && !error && (
          <div className="flex justify-between items-center mb-6">
            <p className="text-gray-600 dark:text-gray-300">
              عرض {courses.length} من أصل {totalCourses} كورس
            </p>
            {searchTerm && (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                نتائج البحث عن: "{searchTerm}"
              </p>
            )}
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-20">
            <div className="text-center">
              <RefreshCw className="w-12 h-12 animate-spin text-blue-600 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-300">
                جاري تحميل الكورسات...
              </p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && !loading && (
          <div className="flex justify-center items-center py-20">
            <div className="text-center">
              <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
              <button
                onClick={handleRetry}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-300"
              >
                إعادة المحاولة
              </button>
            </div>
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && courses.length === 0 && (
          <div className="flex justify-center items-center py-20">
            <div className="text-center">
              <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-300 mb-2">
                لا توجد كورسات تطابق معايير البحث
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                جرب تغيير الفلاتر أو البحث بكلمات مختلفة
              </p>
            </div>
          </div>
        )}

        {/* Courses Grid/List */}
        {!loading && !error && courses.length > 0 && (
          <>
            <div
              className={
                viewMode === "grid"
                  ? "grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
                  : "space-y-6"
              }
            >
              {courses.map((course) => (
                <div
                  key={course.id}
                  className={`group bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden ${
                    viewMode === "list" ? "flex" : ""
                  }`}
                >
                  {/* Course Image */}
                  <div
                    className={`relative overflow-hidden ${
                      viewMode === "list" ? "w-64 flex-shrink-0" : ""
                    }`}
                  >
                    <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 relative">
                      {course.thumbnail ? (
                        <img
                          src={`${theUrl}${course.thumbnail}`}
                          alt={course.title}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.target.style.display = "none";
                            e.target.nextSibling.style.display = "flex";
                          }}
                        />
                      ) : null}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <BookOpen className="w-16 h-16 text-blue-500 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300" />
                      </div>
                    </div>

                    {/* Category Badge */}
                    {/* {course.category && (
                      <div className="absolute top-4 right-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium">
                        {typeof course.category === "object"
                          ? course.category.name
                          : course.category}
                      </div>
                    )} */}

                    {/* Discount Badge */}
                    {course.discount_price &&
                      course.price &&
                      course.discount_price < course.price && (
                        <div className="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                          خصم{" "}
                          {Math.round(
                            ((course.price - course.discount_price) /
                              course.price) *
                              100
                          )}
                          %
                        </div>
                      )}
                  </div>

                  {/* Course Content */}
                  <div className="p-6 flex-1">
                    {/* Title */}
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                      {course.title}
                    </h3>

                    {/* Description */}
                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">
                      {course.short_description || course.description}
                    </p>

                    {/* Instructor */}
                    <div className="flex items-center mb-4">
                      <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center ml-3">
                        <span className="text-white text-sm font-bold uppercase">
                          {course.instructor?.username?.charAt(0) ||
                            course.instructor?.charAt(0) ||
                            "م"}
                        </span>
                      </div>
                      <span className="text-gray-700 dark:text-gray-300 text-sm">
                        {(course.instructor?.first_name ||
                          course.instructor ||
                          "معلم") +
                          (course.instructor?.last_name
                            ? " " + course.instructor.last_name
                            : "")}
                      </span>
                    </div>

                    {/* Rating and Stats */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <div className="flex items-center">
                          {renderStars(course.rating)}
                        </div>
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          ({course.rating || "0.0"})
                        </span>
                      </div>
                      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <Users className="w-4 h-4 ml-1" />
                        {course.students_count || 0}
                      </div>
                    </div>

                    {/* Duration and Level */}
                    <div className="flex items-center justify-between mb-4 text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 ml-1" />
                        {formatDuration(course.duration)}
                      </div>
                      <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs">
                        {course.level === "beginner"
                          ? "مبتدئ"
                          : course.level === "intermediate"
                          ? "متوسط"
                          : course.level === "advanced"
                          ? "متقدم"
                          : course.level || "غير محدد"}
                      </span>
                    </div>

                    {/* Price and CTA */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                          {course.discount_price
                            ? formatPrice(course.discount_price)
                            : formatPrice(course.price)}
                        </span>
                        {course.discount_price &&
                          course.price &&
                          course.discount_price < course.price && (
                            <span className="text-sm text-gray-500 dark:text-gray-400 line-through">
                              {formatPrice(course.price)}
                            </span>
                          )}
                      </div>
                      <CourseStartButton course={course} />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center items-center mt-12 space-x-2 space-x-reverse">
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(prev - 1, 1))
                  }
                  disabled={currentPage === 1 || searchLoading}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  السابق
                </button>

                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const page = i + 1;
                  return (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      disabled={searchLoading}
                      className={`px-4 py-2 rounded-lg ${
                        currentPage === page
                          ? "bg-blue-600 text-white"
                          : "border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                      } disabled:opacity-50 disabled:cursor-not-allowed`}
                    >
                      {page}
                    </button>
                  );
                })}

                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                  }
                  disabled={currentPage === totalPages || searchLoading}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  التالي
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
