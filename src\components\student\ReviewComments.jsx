import React from "react";

const CommentItem = ({ 
  comment, 
  reviewId, 
  onReply, 
  onToggleReplyForm, 
  showReplyForm, 
  replyText, 
  setReplyText, 
  commentLoading,
  depth = 0 
}) => {
  const maxDepth = 3; // أقصى عمق للتعليقات
  const canReply = depth < maxDepth;
  const replyKey = `${reviewId}-${comment.id}`;

  return (
    <div className={`${depth > 0 ? 'mr-6 border-r-2 border-gray-200 dark:border-gray-600 pr-4' : ''}`}>
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-3">
        {/* معلومات المستخدم */}
        <div className="flex items-center gap-3 mb-2">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <span className="text-white font-bold text-sm">
              {(comment.user?.username || "مستخدم").charAt(0)}
            </span>
          </div>
          <div className="flex-1">
            <h4 className="font-semibold text-gray-800 dark:text-white text-sm">
              {comment.user?.username || "مستخدم مجهول"}
            </h4>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {comment.created_at && new Date(comment.created_at).toLocaleDateString('ar-EG')}
            </span>
          </div>
          {canReply && (
            <button
              onClick={() => onToggleReplyForm(reviewId, comment.id)}
              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium transition-colors"
            >
              رد
            </button>
          )}
        </div>

        {/* نص التعليق */}
        <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed mb-3">
          {comment.text}
        </p>

        {/* نموذج الرد */}
        {showReplyForm[replyKey] && canReply && (
          <div className="mt-3 p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
            <div className="flex gap-2">
              <input
                type="text"
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                placeholder="اكتب ردك..."
                value={replyText[replyKey] || ""}
                onChange={(e) => setReplyText(prev => ({ ...prev, [replyKey]: e.target.value }))}
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    onReply(reviewId, comment.id);
                  }
                }}
              />
              <button
                onClick={() => onReply(reviewId, comment.id)}
                disabled={commentLoading[reviewId] || !replyText[replyKey]?.trim()}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 text-sm"
              >
                {commentLoading[reviewId] ? (
                  <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                ) : (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                )}
                إرسال
              </button>
            </div>
          </div>
        )}
      </div>

      {/* الردود */}
      {comment.replies && comment.replies.length > 0 && (
        <div className="mt-2">
          {comment.replies.map((reply) => (
            <CommentItem
              key={reply.id}
              comment={reply}
              reviewId={reviewId}
              onReply={onReply}
              onToggleReplyForm={onToggleReplyForm}
              showReplyForm={showReplyForm}
              replyText={replyText}
              setReplyText={setReplyText}
              commentLoading={commentLoading}
              depth={depth + 1}
            />
          ))}
        </div>
      )}
    </div>
  );
};

const ReviewComments = ({
  review,
  comments = [],
  commentText,
  setCommentText,
  replyText,
  setReplyText,
  showReplyForm,
  onAddComment,
  onToggleReplyForm,
  commentLoading,
  expandedComments,
  onToggleExpand
}) => {
  const topLevelComments = comments.filter(comment => !comment.parent);
  const hasComments = topLevelComments.length > 0;
  const isExpanded = expandedComments[review.id];



  return (
    <div className="mt-4 border-t border-gray-200 dark:border-gray-600 pt-4">
      {/* عنوان التعليقات */}
      <div className="flex items-center justify-between mb-4">
        <h4 className="font-semibold text-gray-800 dark:text-white flex items-center gap-2">
          <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
          التعليقات ({topLevelComments.length})
        </h4>
        {hasComments && (
          <button
            onClick={() => onToggleExpand(review.id)}
            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium transition-colors"
          >
            {isExpanded ? "إخفاء" : "عرض"} التعليقات
          </button>
        )}
      </div>

      {/* نموذج إضافة تعليق جديد */}
      <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
        <div className="flex gap-3">
          <input
            type="text"
            className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            placeholder="اكتب تعليقك على هذا التقييم..."
            value={commentText[review.id] || ""}
            onChange={(e) => setCommentText(prev => ({ ...prev, [review.id]: e.target.value }))}
            onKeyPress={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                onAddComment(review.id);
              }
            }}
          />
          <button
            onClick={() => onAddComment(review.id)}
            disabled={commentLoading[review.id] || !commentText[review.id]?.trim()}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {commentLoading[review.id] ? (
              <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            ) : (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            )}
            إضافة تعليق
          </button>
        </div>
      </div>

      {/* عرض التعليقات */}
      {hasComments && isExpanded && (
        <div className="space-y-3">
          {topLevelComments.map((comment) => (
            <CommentItem
              key={comment.id}
              comment={comment}
              reviewId={review.id}
              onReply={onAddComment}
              onToggleReplyForm={onToggleReplyForm}
              showReplyForm={showReplyForm}
              replyText={replyText}
              setReplyText={setReplyText}
              commentLoading={commentLoading}
              depth={0}
            />
          ))}
        </div>
      )}

      {/* رسالة عدم وجود تعليقات */}
      {!hasComments && (
        <div className="text-center py-6 text-gray-500 dark:text-gray-400">
          <svg className="w-12 h-12 mx-auto mb-2 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
          <p className="text-sm">لا توجد تعليقات على هذا التقييم بعد</p>
          <p className="text-xs mt-1">كن أول من يعلق!</p>
        </div>
      )}
    </div>
  );
};

export default ReviewComments;
