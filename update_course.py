import requests

# تكوين عنوان URL والهيدرز
course_id = "95512418-380f-492c-91e0-91026a4208b3"
url = f'http://127.0.0.1:8000/api/courses/{course_id}/'
headers = {
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ3MjUzODE2LCJpYXQiOjE3NDcyNTAyMTYsImp0aSI6ImFhNTlkMmVhODk3NDQyNjM4YzA4ZjgwNDlmMjQwYTZhIiwidXNlcl9pZCI6IjU4ZjMwNzRlLWVmYzEtNDIxOC1hMjA2LWM0YzU0MjhlZWIzZSJ9.w-nnTFjFJcSWicuEYIbapijBoyojp27roDaGYWVgq2I',
    'Content-Type': 'application/json'
}

# إعداد البيانات
data = {
    'is_published': True
}

# إرسال الطلب
response = requests.patch(url, headers=headers, json=data)

# طباعة النتيجة
print(f'Status Code: {response.status_code}')
print('Response:')
print(response.text) 