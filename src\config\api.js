// إعدادات API الشاملة للمنصة التعليمية - zaki alkholy
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';

export const API_CONFIG = {
  baseURL: API_BASE_URL,
  timeout: 30000, // زيادة المهلة الزمنية للعمليات الكبيرة
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  withCredentials: true, // لدعم الكوكيز
};

// جميع نقاط النهاية المتاحة في النظام - zaki alkholy
export const ENDPOINTS = {
  // ===============================
  // نقاط نهاية المصادقة - zaki alkholy
  // ===============================
  auth: {
    login: '/api/auth/login/',
    register: '/api/auth/register/',
    logout: '/api/auth/logout/',
    refresh: '/api/auth/refresh/',
    user: '/api/auth/user/',
    googleLogin: '/api/google-login/',
    emailVerification: '/api/email-verification/',
    requestPasswordReset: '/api/auth/request-reset-password/',
    resetPassword: '/api/auth/reset-password/',
  },

  // ===============================
  // نقاط نهاية الطلاب - zaki alkholy
  // ===============================
  student: {
    dashboard: '/api/student/dashboard/',
    points: '/api/student/points/',
    pointsHistory: '/api/student/points/history/',
    achievements: '/api/student/achievements/',
    learningStreak: '/api/student/learning-streak/',
    comparison: '/api/student/comparison/',
    profile: '/api/student/profile/',
    courses: '/api/student/courses/',
    progress: '/api/student-progress/',

    // المراجعة المتباعدة
    review: {
      daily: '/api/student/review/daily/',
      stats: '/api/student/review/stats/',
      recommendations: '/api/student/review/recommendations/',
      autoSchedule: '/api/student/review/auto-schedule/',
      settings: '/api/student/review/settings/',
      export: '/api/student/review/export/',
    }
  },

  // ===============================
  // نقاط نهاية المعلمين - zaki alkholy
  // ===============================
  instructor: {
    dashboard: '/api/instructor/dashboard/',
    profile: '/api/instructor-profiles/',
    courses: '/api/courses/',

    // التحليلات والإحصائيات
    analytics: {
      overview: '/api/instructor/analytics/',
      course: '/api/instructor/analytics/course/',
      students: '/api/instructor/students/',
      sales: '/api/instructor/sales/',
      lesson: '/api/instructor/lesson/',
      dashboardStats: '/api/instructor/dashboard/stats/',
      topContent: '/api/instructor/top-content/',
      assignments: '/api/instructor/assignments/analytics/',
      export: '/api/instructor/analytics/export/',
    }
  },

  // ===============================
  // نقاط نهاية الدورات والمحتوى - zaki alkholy
  // ===============================
  courses: {
    list: '/api/courses/',
    detail: '/api/courses/',
    lessons: '/api/lessons/',
    categories: '/api/categories/',
    mainCategories: '/api/main-categories/',
    reviews: '/api/reviews/',
    enrollments: '/api/enrollments/',
    faqs: '/api/faqs/',
    announcements: '/api/announcements/',
    certificates: '/api/certificates/',
  },

  // ===============================
  // نقاط نهاية الاختبارات والواجبات - zaki alkholy
  // ===============================
  assessments: {
    quizzes: '/api/quizzes/',
    questions: '/api/questions/',
    answers: '/api/answers/',
    attempts: '/api/quiz-attempts/',
    assignments: '/api/assignments/',
    submissions: '/api/assignment-submissions/',
  },

  // ===============================
  // نقاط نهاية النقاط والمكافآت - zaki alkholy
  // ===============================
  gamification: {
    pointsStore: '/api/points-store/',
    leaderboard: '/api/leaderboard/weekly/',
    achievements: '/api/achievements/',
    redeem: '/api/gamification/redeem_points/',
  },

  // ===============================
  // نقاط نهاية الإشعارات - zaki alkholy
  // ===============================
  notifications: {
    list: '/api/notifications/',
    markAsRead: '/api/notifications/',
    markAllAsRead: '/api/notifications/mark-all-read/',
    unreadCount: '/api/notifications/unread-count/',
    settings: '/api/notifications/settings/',
    pushSubscribe: '/api/notifications/push-subscribe/',
    pushUnsubscribe: '/api/notifications/push-unsubscribe/',
    sendToStudents: '/api/notifications/send-to-students/',
    sendToCourse: '/api/notifications/send-to-course/',
    templates: '/api/notifications/templates/',
  },

  // ===============================
  // نقاط نهاية المراجعة المتباعدة - zaki alkholy
  // ===============================
  spacedRepetition: {
    schedules: '/api/review-schedules/',
    sessions: '/api/review-sessions/',
    recommendations: '/api/student/review/recommendations/',
    autoScheduler: '/api/student/review/auto-schedule/',
    stats: '/api/student/review/stats/',
    daily: '/api/student/review/daily/',
  },

  // ===============================
  // نقاط نهاية المدفوعات - zaki alkholy
  // ===============================
  payments: {
    createIntent: '/api/create-payment-intent/',
    processPayment: '/api/process-payment/',
    webhook: '/api/paymob-webhook/',
    payout: '/api/process-payout/',
    orders: '/api/orders/',
    digitalProducts: '/api/products/',
  },

  // ===============================
  // نقاط نهاية الملفات والوسائط - zaki alkholy
  // ===============================
  media: {
    upload: '/api/upload/',
    lessonResource: '/api/lessons/',
    videoPlayer: '/lesson/',
    thumbnails: '/media/course_thumbnails/',
    resources: '/media/lesson_resources/',
  },

  // ===============================
  // نقاط نهاية المواعيد والجلسات - zaki alkholy
  // ===============================
  appointments: {
    list: '/api/availabilities/',
    book: '/api/appointments/book/',
    cancel: '/api/appointments/cancel/',
    reschedule: '/api/appointments/reschedule/',
  },
};

// ===============================
// إعدادات إضافية للـ API - zaki alkholy
// ===============================

// أنواع المحتوى المدعومة
export const SUPPORTED_FILE_TYPES = {
  images: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  videos: ['mp4', 'webm', 'ogg', 'avi', 'mov'],
  documents: ['pdf', 'doc', 'docx', 'txt', 'rtf'],
  presentations: ['ppt', 'pptx'],
  spreadsheets: ['xls', 'xlsx', 'csv'],
  archives: ['zip', 'rar', '7z'],
};

// أحجام الملفات القصوى (بالبايت)
export const MAX_FILE_SIZES = {
  image: 5 * 1024 * 1024, // 5MB
  video: 100 * 1024 * 1024, // 100MB
  document: 10 * 1024 * 1024, // 10MB
  general: 50 * 1024 * 1024, // 50MB
};

// إعدادات التخزين المؤقت
export const CACHE_CONFIG = {
  defaultTTL: 5 * 60 * 1000, // 5 دقائق
  userDataTTL: 15 * 60 * 1000, // 15 دقيقة
  coursesDataTTL: 30 * 60 * 1000, // 30 دقيقة
  staticDataTTL: 60 * 60 * 1000, // ساعة واحدة
};

// إعدادات إعادة المحاولة
export const RETRY_CONFIG = {
  maxRetries: 3,
  retryDelay: 1000, // 1 ثانية
  retryDelayMultiplier: 2, // مضاعف التأخير
};

// رسائل الخطأ المخصصة
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'خطأ في الاتصال بالشبكة',
  UNAUTHORIZED: 'غير مصرح لك بالوصول',
  FORBIDDEN: 'ممنوع الوصول',
  NOT_FOUND: 'المورد غير موجود',
  SERVER_ERROR: 'خطأ في الخادم',
  TIMEOUT: 'انتهت مهلة الطلب',
  VALIDATION_ERROR: 'خطأ في التحقق من البيانات',
  FILE_TOO_LARGE: 'حجم الملف كبير جداً',
  UNSUPPORTED_FILE_TYPE: 'نوع الملف غير مدعوم',
};

// إعدادات الأمان
export const SECURITY_CONFIG = {
  tokenRefreshThreshold: 5 * 60 * 1000, // 5 دقائق قبل انتهاء الصلاحية
  maxLoginAttempts: 5,
  lockoutDuration: 15 * 60 * 1000, // 15 دقيقة
};

// إعدادات الإشعارات الفورية
export const PUSH_NOTIFICATION_CONFIG = {
  vapidPublicKey: process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY,
  swPath: '/sw.js',
};

// إعدادات التحليلات
export const ANALYTICS_CONFIG = {
  trackingEnabled: process.env.NODE_ENV === 'production',
  sessionTimeout: 30 * 60 * 1000, // 30 دقيقة
  batchSize: 10, // عدد الأحداث في الدفعة الواحدة
};

// دالة مساعدة لبناء URL كامل - zaki alkholy
export const buildApiUrl = (endpoint, params = {}) => {
  const url = new URL(endpoint, API_BASE_URL);
  Object.keys(params).forEach(key => {
    if (params[key] !== null && params[key] !== undefined) {
      url.searchParams.append(key, params[key]);
    }
  });
  return url.toString();
};

// دالة مساعدة للتحقق من نوع الملف - zaki alkholy
export const isFileTypeSupported = (fileName, category = 'general') => {
  const extension = fileName.split('.').pop().toLowerCase();
  const supportedTypes = SUPPORTED_FILE_TYPES[category] || [];
  return supportedTypes.includes(extension);
};

// دالة مساعدة للتحقق من حجم الملف - zaki alkholy
export const isFileSizeValid = (fileSize, category = 'general') => {
  const maxSize = MAX_FILE_SIZES[category] || MAX_FILE_SIZES.general;
  return fileSize <= maxSize;
};

// دالة مساعدة لتنسيق رسائل الخطأ - zaki alkholy
export const formatErrorMessage = (error) => {
  if (error.response) {
    const status = error.response.status;
    switch (status) {
      case 401:
        return ERROR_MESSAGES.UNAUTHORIZED;
      case 403:
        return ERROR_MESSAGES.FORBIDDEN;
      case 404:
        return ERROR_MESSAGES.NOT_FOUND;
      case 422:
        return ERROR_MESSAGES.VALIDATION_ERROR;
      case 500:
        return ERROR_MESSAGES.SERVER_ERROR;
      default:
        return error.response.data?.message || ERROR_MESSAGES.SERVER_ERROR;
    }
  } else if (error.request) {
    return ERROR_MESSAGES.NETWORK_ERROR;
  } else {
    return error.message || 'حدث خطأ غير متوقع';
  }
};