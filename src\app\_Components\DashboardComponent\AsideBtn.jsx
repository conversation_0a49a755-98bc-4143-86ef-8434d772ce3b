import React from "react";
import { PanelLeftOpen, PanelLeftClose } from "lucide-react";

export default function AsideBtn({ handleToggleExpand, isExpanded }) {
  return (
    <div>
      <button
        onClick={handleToggleExpand}
        className="md:hidden fixed top-20 right-4  z-50 bg-white dark:bg-gray-800 p-3 rounded-full shadow-lg border border-gray-200 dark:border-gray-700 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:shadow-xl transition-all duration-300 hover:scale-105"
        aria-label={isExpanded ? "إغلاق القائمة" : "فتح القائمة"}
      >
        {isExpanded ? (
          <PanelLeftClose className="w-6 h-6" />
        ) : (
          <PanelLeftOpen className="w-6 h-6" />
        )}
      </button>
    </div>
  );
}
