"use client";
import React, { useState, useEffect, useRef } from "react";
import axios from "axios";
import {
  DollarSign,
  User,
  Calendar,
  CreditCard,
  AlertCircle,
  CheckCircle,
  Clock,
  Eye,
  Send,
  Upload,
  X,
  Image as ImageIcon
} from "lucide-react";

export default function PendingPayouts() {
  const [pendingPayouts, setPendingPayouts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [processingPayout, setProcessingPayout] = useState(null);

  // حالات مودال رفع الوصل
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [selectedInstructor, setSelectedInstructor] = useState(null);
  const [uploadingFile, setUploadingFile] = useState(null);
  const [previewImage, setPreviewImage] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef(null);

  // حالات مودال عرض التفاصيل
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [instructorDetails, setInstructorDetails] = useState(null);
  const [loadingDetails, setLoadingDetails] = useState(false);

  useEffect(() => {
    fetchPendingPayouts();

    // إضافة event listener للتحديث عند الإرجاع من صفحة التحويلات المكتملة
    const handlePayoutReversed = () => {
      console.log('Payout reversed, refreshing pending payouts...');
      fetchPendingPayouts();
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('payoutReversed', handlePayoutReversed);
    }

    // تنظيف event listener عند إلغاء تحميل المكون
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('payoutReversed', handlePayoutReversed);
      }
    };
  }, []);

  const fetchPendingPayouts = async () => {
    try {
      const token = localStorage.getItem("admin_token");
      const response = await axios.get("http://127.0.0.1:8000/api/admin-dashboard/pending-payouts/", {
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      setPendingPayouts(response.data.pending_instructors || []);
    } catch (error) {
      console.error("Error fetching pending payouts:", error);
      setError("حدث خطأ في تحميل البيانات");
    } finally {
      setLoading(false);
    }
  };

  const handleProcessPayout = async (instructorData) => {
    // فتح مودال رفع الوصل بدلاً من التحويل المباشر
    setSelectedInstructor(instructorData);
    setShowUploadModal(true);
  };

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // التحقق من نوع الملف
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      alert('نوع الملف غير مدعوم. يُسمح بـ JPEG, PNG, WebP فقط');
      return;
    }

    // التحقق من حجم الملف (5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('حجم الملف كبير جداً. الحد الأقصى 5MB');
      return;
    }

    setUploadingFile(file);

    // إنشاء preview للصورة
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewImage(e.target.result);
    };
    reader.readAsDataURL(file);
  };

  const handleUploadAndTransfer = async () => {
    if (!uploadingFile || !selectedInstructor) return;

    setProcessingPayout(selectedInstructor.instructor?.id);

    try {
      const token = localStorage.getItem("admin_token");

      // أولاً: إنشاء الـ payout
      const createPayoutResponse = await fetch(`http://127.0.0.1:8000/api/admin-dashboard/process-payout/${selectedInstructor.instructor.id}/`, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          amount: selectedInstructor.instructor_earnings,
          notes: "تحويل من Admin Dashboard مع رفع الوصل"
        }),
      });

      if (!createPayoutResponse.ok) {
        const errorData = await createPayoutResponse.json();
        throw new Error(errorData.error || 'فشل في إنشاء التحويل');
      }

      const payoutData = await createPayoutResponse.json();
      const payoutId = payoutData.payout_id;

      // ثانياً: رفع الوصل
      const formData = new FormData();
      formData.append('receipt_image', uploadingFile);

      const xhr = new XMLHttpRequest();

      // تتبع التقدم
      xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
          const progress = (e.loaded / e.total) * 100;
          setUploadProgress(progress);
        }
      });

      xhr.open('POST', `http://127.0.0.1:8000/api/admin-dashboard/upload-receipt/${payoutId}/`);
      xhr.setRequestHeader('Authorization', `Bearer ${token}`);

      xhr.onload = function() {
        if (xhr.status === 200) {
          alert(`تم إنشاء التحويل ورفع الوصل بنجاح!\nالمبلغ: ${selectedInstructor.instructor_earnings} جنيه\nوسيلة الدفع: ${selectedInstructor.instructor?.payment_method}\nرقم المحفظة: ${selectedInstructor.instructor?.wallet_number}`);

          // إعادة تحميل البيانات وإغلاق المودال
          fetchPendingPayouts();
          closeUploadModal();

          // إشعار صفحة التحويلات المكتملة بالتحديث
          if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('payoutCompleted', {
              detail: { payoutId: payoutId }
            }));
          }
        } else {
          try {
            const errorResponse = JSON.parse(xhr.responseText);
            alert(`فشل في رفع الوصل: ${errorResponse.error || 'خطأ غير معروف'}`);
          } catch (e) {
            alert('فشل في رفع الوصل: خطأ في الخادم');
          }
          closeUploadModal();
        }
      };

      xhr.onerror = function() {
        alert('حدث خطأ في رفع الملف');
        closeUploadModal();
      };

      xhr.send(formData);

    } catch (error) {
      console.error("Error processing payout:", error);
      alert(`حدث خطأ: ${error.message}`);
      closeUploadModal();
    } finally {
      setProcessingPayout(null);
    }
  };

  const closeUploadModal = () => {
    setShowUploadModal(false);
    setSelectedInstructor(null);
    setUploadingFile(null);
    setPreviewImage(null);
    setUploadProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleViewDetails = async (instructorId) => {
    setLoadingDetails(true);
    setShowDetailsModal(true);

    try {
      const token = localStorage.getItem("admin_token");
      const response = await axios.get(`http://127.0.0.1:8000/api/admin-dashboard/instructor-earnings/${instructorId}/`, {
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      setInstructorDetails(response.data);
    } catch (error) {
      console.error("Error fetching instructor details:", error);
      alert("حدث خطأ في جلب تفاصيل المعلم");
      setShowDetailsModal(false);
    } finally {
      setLoadingDetails(false);
    }
  };

  const closeDetailsModal = () => {
    setShowDetailsModal(false);
    setInstructorDetails(null);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2 space-x-reverse">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="text-gray-600 dark:text-gray-400">جاري تحميل المبيعات المعلقة...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div className="flex items-center">
          <AlertCircle className="w-5 h-5 text-red-500 ml-2" />
          <span className="text-red-700 dark:text-red-400">{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              المبيعات المعلقة
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              قائمة المعلمين الذين لديهم مبيعات معلقة تحتاج لتحويل
            </p>
          </div>
          <div className="flex items-center space-x-2 space-x-reverse">
            <Clock className="w-5 h-5 text-orange-500" />
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {pendingPayouts.length} معلم معلق
            </span>
          </div>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي المعلمين</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{pendingPayouts.length}</p>
            </div>
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/20">
              <User className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي المبلغ المعلق</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {pendingPayouts.reduce((sum, instructorData) => sum + (instructorData.instructor_earnings || 0), 0).toFixed(2)} جنيه
              </p>
            </div>
            <div className="p-3 rounded-full bg-orange-100 dark:bg-orange-900/20">
              <DollarSign className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الطلبات</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {pendingPayouts.reduce((sum, instructorData) => sum + (instructorData.orders?.length || 0), 0)}
              </p>
            </div>
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/20">
              <CreditCard className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Upload Receipt Modal */}
      {showUploadModal && selectedInstructor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Modal Header */}
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  رفع وصل التحويل - {selectedInstructor.instructor?.username || 'مستخدم غير معروف'}
                </h3>
                <button
                  onClick={closeUploadModal}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              {/* Instructor Info */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">المبلغ المحول</p>
                    <p className="text-lg font-semibold text-gray-900 dark:text-white">
                      {(selectedInstructor.instructor_earnings || 0).toFixed(2)} جنيه
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">وسيلة الدفع</p>
                    <p className="text-lg font-semibold text-gray-900 dark:text-white">
                      {selectedInstructor.instructor?.payment_method || 'غير محدد'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">رقم المحفظة</p>
                    <p className="text-lg font-semibold text-gray-900 dark:text-white">
                      {selectedInstructor.instructor?.wallet_number || 'غير محدد'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">عدد الطلبات</p>
                    <p className="text-lg font-semibold text-gray-900 dark:text-white">
                      {selectedInstructor.orders?.length || 0} طلب
                    </p>
                  </div>
                </div>
              </div>

              {/* File Upload */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                  رفع وصل التحويل (مطلوب)
                </label>
                <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6">
                  {!previewImage ? (
                    <div className="text-center">
                      <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <div className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                        اختر صورة وصل التحويل
                      </div>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleFileSelect}
                        className="hidden"
                        id="receipt-upload"
                      />
                      <label
                        htmlFor="receipt-upload"
                        className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg cursor-pointer transition-colors"
                      >
                        <Upload className="w-4 h-4 ml-2" />
                        اختيار ملف
                      </label>
                    </div>
                  ) : (
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                        معاينة الوصل
                      </p>
                      <img
                        src={previewImage}
                        alt="Receipt preview"
                        className="max-w-full h-auto max-h-64 mx-auto rounded-lg mb-4"
                      />
                      <div className="text-center">
                        <button
                          onClick={() => {
                            setUploadingFile(null);
                            setPreviewImage(null);
                            if (fileInputRef.current) {
                              fileInputRef.current.value = '';
                            }
                          }}
                          className="text-red-600 hover:text-red-800 text-sm"
                        >
                          إزالة الصورة
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Upload Progress */}
              {uploadProgress > 0 && uploadProgress < 100 && (
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600 dark:text-gray-400">جاري التحويل والرفع...</span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">{Math.round(uploadProgress)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3 space-x-reverse">
                <button
                  onClick={closeUploadModal}
                  disabled={processingPayout === selectedInstructor.instructor?.id}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors disabled:opacity-50"
                >
                  إلغاء
                </button>
                <button
                  onClick={handleUploadAndTransfer}
                  disabled={!uploadingFile || processingPayout === selectedInstructor.instructor?.id}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg transition-colors flex items-center"
                >
                  {processingPayout === selectedInstructor.instructor?.id ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                      جاري التحويل...
                    </>
                  ) : (
                    <>
                      <Send className="w-4 h-4 ml-2" />
                      رفع الوصل
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Instructor Details Modal */}
      {showDetailsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Modal Header */}
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  تفاصيل أرباح المعلم
                </h3>
                <button
                  onClick={closeDetailsModal}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              {loadingDetails ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                  <span className="mr-3 text-gray-600 dark:text-gray-400">جاري تحميل التفاصيل...</span>
                </div>
              ) : instructorDetails ? (
                <div className="space-y-6">
                  {/* Instructor Info */}
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">معلومات المعلم</h4>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">اسم المستخدم</p>
                        <p className="font-semibold text-gray-900 dark:text-white">{instructorDetails.instructor?.username}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">البريد الإلكتروني</p>
                        <p className="font-semibold text-gray-900 dark:text-white">{instructorDetails.instructor?.email}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">الاسم الكامل</p>
                        <p className="font-semibold text-gray-900 dark:text-white">
                          {instructorDetails.instructor?.first_name} {instructorDetails.instructor?.last_name}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">وسيلة الدفع</p>
                        <p className="font-semibold text-gray-900 dark:text-white">{instructorDetails.instructor?.payment_method || 'غير محدد'}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">رقم المحفظة</p>
                        <p className="font-semibold text-gray-900 dark:text-white">{instructorDetails.instructor?.wallet_number || 'غير محدد'}</p>
                      </div>
                    </div>
                  </div>

                  {/* Summary Stats */}
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">ملخص الأرباح</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                          {instructorDetails.summary?.total_courses || 0}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي الكورسات</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                          {instructorDetails.summary?.total_orders || 0}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي الطلبات</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                          {(instructorDetails.summary?.total_instructor_earnings || 0).toFixed(2)} جنيه
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي الأرباح</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                          {(instructorDetails.summary?.pending_earnings || 0).toFixed(2)} جنيه
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">الأرباح المعلقة</p>
                      </div>
                    </div>
                  </div>

                  {/* Courses Details */}
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">تفاصيل الكورسات</h4>
                    <div className="space-y-4">
                      {instructorDetails.courses?.map((course, index) => (
                        <div key={index} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                          <div className="flex justify-between items-start mb-3">
                            <h5 className="font-semibold text-gray-900 dark:text-white">{course.course_title}</h5>
                            <span className="text-sm bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded">
                              {course.orders_count} طلب
                            </span>
                          </div>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <p className="text-gray-600 dark:text-gray-400">إجمالي المبيعات</p>
                              <p className="font-semibold text-gray-900 dark:text-white">{course.total_base_amount.toFixed(2)} جنيه</p>
                            </div>
                            <div>
                              <p className="text-gray-600 dark:text-gray-400">عمولة المنصة</p>
                              <p className="font-semibold text-gray-900 dark:text-white">{course.total_platform_fee.toFixed(2)} جنيه</p>
                            </div>
                            <div>
                              <p className="text-gray-600 dark:text-gray-400">أرباح المعلم</p>
                              <p className="font-semibold text-green-600 dark:text-green-400">{course.instructor_earnings.toFixed(2)} جنيه</p>
                            </div>
                            <div>
                              <p className="text-gray-600 dark:text-gray-400">الحالة</p>
                              <p className="font-semibold text-gray-900 dark:text-white">
                                {course.has_pending_orders ? 'معلق' : 'مكتمل'}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-gray-600 dark:text-gray-400">لا توجد تفاصيل متاحة</p>
                </div>
              )}

              {/* Close Button */}
              <div className="flex justify-end mt-6">
                <button
                  onClick={closeDetailsModal}
                  className="px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Pending Payouts List */}
      {pendingPayouts.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12 text-center">
          <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            لا توجد مبيعات معلقة
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            جميع المعلمين تم تحويل مستحقاتهم
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {pendingPayouts.map((instructorData, index) => (
            <div
              key={instructorData.instructor?.id || index}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  {/* Instructor Info */}
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-semibold text-lg">
                        {(instructorData.instructor?.username || 'U').charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div className="mr-4">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {instructorData.instructor?.username || 'مستخدم غير معروف'}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {instructorData.instructor?.email || 'بريد غير متوفر'}
                      </p>
                    </div>
                  </div>

                  {/* Payment Info */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي المبيعات</p>
                      <p className="text-lg font-semibold text-gray-900 dark:text-white">
                        {(instructorData.total_amount || 0).toFixed(2)} جنيه
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">عمولة المنصة (5%)</p>
                      <p className="text-lg font-semibold text-red-600">
                        {(instructorData.platform_fee || 0).toFixed(2)} جنيه
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">مستحقات المعلم</p>
                      <p className="text-lg font-semibold text-green-600">
                        {(instructorData.instructor_earnings || 0).toFixed(2)} جنيه
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">عدد الطلبات</p>
                      <p className="text-lg font-semibold text-blue-600">
                        {instructorData.orders?.length || 0} طلب
                      </p>
                    </div>
                  </div>

                  {/* Payment Method */}
                  <div className="mb-4">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">وسيلة الدفع</p>
                    <div className="flex items-center">
                      <CreditCard className="w-4 h-4 text-gray-500 ml-2" />
                      <span className="text-sm text-gray-900 dark:text-white">
                        {instructorData.instructor?.payment_method || "غير محدد"} - {instructorData.instructor?.wallet_number || "غير محدد"}
                      </span>
                    </div>
                  </div>

                  {/* Recent Orders */}
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                    <p className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                      آخر الطلبات ({(instructorData.orders || []).slice(0, 3).length} من {instructorData.orders?.length || 0})
                    </p>
                    <div className="space-y-2">
                      {(instructorData.orders || []).slice(0, 3).map((order, orderIndex) => (
                        <div key={order.id || orderIndex} className="flex items-center justify-between text-sm">
                          <span className="text-gray-600 dark:text-gray-400">
                            {order.course_title || 'كورس غير معروف'}
                          </span>
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <span className="text-gray-900 dark:text-white font-medium">
                              {parseFloat(order.amount || 0).toFixed(2)} جنيه
                            </span>
                            <span className="text-gray-500">
                              {order.created_at ? new Date(order.created_at).toLocaleDateString('ar-EG') : 'تاريخ غير متوفر'}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col space-y-2 mr-4">
                  <button
                    onClick={() => handleProcessPayout(instructorData)}
                    disabled={processingPayout === instructorData.instructor?.id}
                    className="flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
                  >
                    {processingPayout === instructorData.instructor?.id ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                        جاري التحويل...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4 ml-2" />
                        رفع وصل التحويل
                      </>
                    )}
                  </button>
                  
                  <button
                    onClick={() => handleViewDetails(instructorData.instructor?.id)}
                    className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                  >
                    <Eye className="w-4 h-4 ml-2" />
                    عرض التفاصيل
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
