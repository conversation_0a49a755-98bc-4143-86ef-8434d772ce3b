"use client";
// ================== Imports: External Libraries ==================
import React, {
  useEffect,
  useState,
  useRef,
  useCallback,
  useMemo,
} from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import Link from "next/link";
import Cookies from "js-cookie";
import Hls from "hls.js";
import axios from "axios";
import ReactCrop from "react-image-crop";
import "react-image-crop/dist/ReactCrop.css";

// ================== Imports: Project Components ==================
import EditCourseModal from "../../../../_Components/course/EditCourseModal";
import FileUploadModal from "@/app/_Components/course/FileUploadModal";
import QuestionFormModal from "@/app/_Components/course/QuestionFormModal";
import QuizFormModal from "@/app/_Components/course/QuizFormModal";
import QuizEditModal from "../../../../_Components/course/QuizEditModal";
import QuizPDFGenerator, {
  generateQuizPDF,
} from "@/app/_Components/pdf/QuizPDFGenerator";

// ================== Imports: Services & Utils ==================
import {
  fetchInstructorCourse,
  fetchInstructorLessons,
  deleteInstructorLessons,
  fetchCourseVideo,
  approveReview,
  deleteReview,
  replyToReview,
  fetchReviewComments,
  addReviewComment,
  updateCourse,
} from "../../../../../services/instructor";
// TODO: Move image helpers and error/loader components to utils/components folders

// ================== Imports: UI Helpers (to be created) ==================
// import ErrorAlert from "@/components/common/ErrorAlert";
// import Loader from "@/components/common/Loader";
import { selectCurrentUser } from "@/store/authSlice";
import {
  handleImageSelect,
  getCroppedImg,
  removeImage,
} from "@/utils/imageHelpers";
import useCourseDetails from "@/hooks/useCourseDetails";
import { useQuizManagement } from "@/hooks/useQuizManagement";
import { useFileUpload } from "@/hooks/useFileUpload";
import { useImageCropper } from "@/hooks/useImageCropper";
import { useReviewManagement } from "@/hooks/useReviewManagement";
import ErrorAlert from "@/components/common/ErrorAlert";
import Loader from "@/components/common/Loader";
import LessonList from "@/components/instructor/LessonList";
import ReviewList from "@/components/instructor/ReviewList";
import StudentList from "@/components/instructor/StudentList";
import CourseHeader from "@/components/instructor/CourseHeader";
import CourseInfo from "@/components/instructor/CourseInfo";
import { CourseProvider } from "@/contexts/CourseContext";
export default function CourseDetails() {
  // ================== المتغيرات الرئيسية ==================
  const params = useParams();
  const user = useSelector(selectCurrentUser); // ✅ جوه component مش جوه event أو دالة برا

  const router = useRouter();
  const courseId = params.id;
  // Remove setLoading from destructure, let the hook handle loading state
  const {
    courseData,
    setCourseData,
    lessons,
    setLessons,
    loading,
    error,
    setError,
  } = useCourseDetails(courseId);
  // ================== State Variables المحلية ==================
  const [videoState, setVideoState] = useState({
    url: null,
    token: null,
  });
  const [isClient, setIsClient] = useState(false);
  const [publishing, setPublishing] = useState(false);
  const [expandedLesson, setExpandedLesson] = useState(null);
  const [expandedQuizzes, setExpandedQuizzes] = useState({}); // حالة جديدة لإدارة الامتحانات المفتوحة
  const [showQuizForm, setShowQuizForm] = useState({});
  const [quizForm, setQuizForm] = useState({});
  const [showFileForm, setShowFileForm] = useState({});
  const [fileForm, setFileForm] = useState({});
  const fileInputRefs = useRef({});
  const [deletingResource, setDeletingResource] = useState({});
  const [quizEditForm, setQuizEditForm] = useState(null);
  const [questionForm, setQuestionForm] = useState({}); // لإضافة سؤال جديد
  const [showQuestionForm, setShowQuestionForm] = useState({}); // إظهار فورم إضافة سؤال
  const [questionEditForm, setQuestionEditForm] = useState(null); // لتعديل سؤال
  // questionLoading متوفر من useQuizManagement hook
  // ================== إضافة state لإجابات السؤال ==================
  const [answersForm, setAnswersForm] = useState({}); // { [quizId]: [ { text, is_correct }, ... ] }
  // ================== Image states متوفرة من useImageCropper hook ==================
  // ================== إضافة state لمودال تعديل الكورس ==================
  const [editModalState, setEditModalState] = useState({
    show: false,
    form: {},
    loading: false,
    error: null,
    success: false,
  });
  const editThumbnailRef = useRef();
  const editVideoRef = useRef();
  // ================== نهاية إضافة state لمودال تعديل الكورس ==================

  // ================== استخدام Custom Hooks ==================
  const {
    isUploading,
    uploadProgress,
    uploadSuccess,
    uploadError,
    uploadedFile,
    handleAddFile: fileUploadHandleAddFile,
    handleDeleteResource: fileUploadHandleDeleteResource,
  } = useFileUpload(courseId, setLessons, setError);

  const {
    croppedImages,
    setCroppedImages,
    questionImages,
    setQuestionImages,
    imagePreview,
    setImagePreview,
    showImageCropper,
    setShowImageCropper,
    handleQuestionImageSelect,
    handleCropComplete,
    removeQuestionImage,
  } = useImageCropper();

  const {
    questionLoading: quizManagementQuestionLoading,
    quizLoading: quizManagementQuizLoading,
    handleAddQuiz: quizManagementHandleAddQuiz,
    handleDeleteQuiz: quizManagementHandleDeleteQuiz,
    handleEditQuiz: quizManagementHandleEditQuiz,
    handleAddQuestion: quizManagementHandleAddQuestion,
    handleDeleteQuestion: quizManagementHandleDeleteQuestion,
  } = useQuizManagement(courseId, lessons, croppedImages, setLessons, setError);

  const {
    approving,
    reviewComments,
    setReviewComments,
    commentText,
    setCommentText,
    commentLoading,
    replyText,
    setReplyText,
    replying,
    showReplyForm,
    expandedComments,
    handleApproveReview,
    handleDeleteReview,
    handleReplyToReview,
    handleFetchComments,
    handleAddComment,
    handleToggleReplyForm,
    handleToggleExpand,
    handleDeleteComment,
  } = useReviewManagement(setError);
  const [showQuizModal, setShowQuizModal] = useState({});
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Remove duplicate data fetching here, let useCourseDetails handle it
  // useEffect(() => {
  //   ...
  // }, [courseId]);

  // التنقل لصفحة الدرس
  const handleSeeLesson = useCallback(
    (lessonId) => {
      router.push(`/instructor/dashboard/${courseId}/lesson/${lessonId}`);
    },
    [router, courseId]
  );

  // ✅ handleApproveReview متوفرة من useReviewManagement hook

  // ✅ handleDeleteReview و handleReplyToReview متوفرين من useReviewManagement hook

  // تحميل تعليقات التقييمات
  useEffect(() => {
    if (!courseData || !courseData.reviews) return;
    const token = Cookies.get("authToken");
    courseData.reviews.forEach(async (review) => {
      try {
        const comments = await fetchReviewComments(review.id, token);
        setReviewComments((prev) => ({ ...prev, [review.id]: comments }));
      } catch {
        setReviewComments((prev) => ({ ...prev, [review.id]: [] }));
      }
    });
  }, [courseData]);

  // ✅ handleAddComment متوفرة من useReviewManagement hook

  // حذف الدرس
  const handleDeleteLesson = useCallback(
    async (lessonId) => {
      const token = Cookies.get("authToken");
      if (!token) {
        setError("يرجى تسجيل الدخول أولاً");
        return;
      }
      if (!confirm("هل أنت متأكد من حذف هذا الدرس؟")) {
        return;
      }
      // Hosasm deleteLessonById(courseId, lessonId, token);
      try {
        await deleteInstructorLessons(courseId, lessonId, token);
        const lessonsList = await fetchInstructorLessons(courseId, token);
        setLessons(lessonsList);
      } catch {
        setError("فشل في حذف الدرس");
      }
    },
    [courseId, setLessons, setError]
  );

  // نشر/إلغاء نشر الكورس
  const togglePublish = useCallback(async () => {
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }

    // التحقق من رقم المحفظة قبل النشر - سيتم التحقق من الـ backend
    // لا نحتاج للتحقق المسبق هنا، سنترك الـ backend يتولى الأمر

    setPublishing(true);
    try {
      const formData = new FormData();
      formData.append("is_published", !courseData.is_published);
      await axios.patch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/courses/${courseId}/`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            // لا تضع Content-Type هنا، axios يضبطه تلقائيًا مع formData
          },
        }
      );
      // إعادة تحميل بيانات الكورس
      const course = await fetchInstructorCourse(courseId, token);
      setCourseData(course);
    } catch (error) {

      // التحقق من نوع الخطأ
      if (error.response && error.response.data) {
        const errorData = error.response.data;
        if (errorData.error && errorData.error.includes("محفظة")) {
          setError("برجاء ادخال رقم محفظة اولا");
        } else if (errorData.error && errorData.error.includes("سعر")) {
          setError("سعر الكورس يجب أن يكون 100 جنيه على الأقل.");
        } else {
          setError(errorData.error || "فشل في تحديث حالة النشر");
        }
      } else {
        setError("فشل في تحديث حالة النشر");
      }
    } finally {
      setPublishing(false);
    }
  }, [courseData?.is_published, courseId, setCourseData, setError]);

  // تهيئة مشغل الفيديو
  // useEffect(() => {
  //   ...
  // }, [videoUrl, videoToken, isClient]);

  // إضافة اختبار أو واجب - استخدام الـ hook المحسن
  const handleAddQuiz = useCallback(
    async (lessonId, quizType) => {
      const formData = quizForm[lessonId] || {};

      await quizManagementHandleAddQuiz(lessonId, quizType, formData);
      // إغلاق المودال بعد النجاح
      setShowQuizForm((prev) => ({ ...prev, [lessonId]: false }));
      setQuizForm((prev) => ({ ...prev, [lessonId]: {} }));
      setShowQuizModal({});
    },
    [quizManagementHandleAddQuiz, quizForm]
  );

  // رفع ملف resource - استخدام الـ hook المحسن
  const handleAddFile = useCallback(
    async (lessonId) => {
      await fileUploadHandleAddFile(lessonId, fileInputRefs);
      setShowFileForm({});
    },
    [fileUploadHandleAddFile]
  );

  // حذف ملف resource - استخدام الـ hook المحسن
  const handleDeleteResource = useCallback(
    async (lessonId, resourceUrl) => {
      setDeletingResource((prev) => ({ ...prev, [lessonId]: true }));
      try {
        await fileUploadHandleDeleteResource(lessonId, resourceUrl);
        setShowFileForm((prev) => ({ ...prev, [lessonId]: true }));
      } catch (error) {
        // حذف الملف فشل
      } finally {
        setDeletingResource((prev) => ({ ...prev, [lessonId]: false }));
      }
    },
    [fileUploadHandleDeleteResource]
  );

  // حذف كويز أو واجب - استخدام الـ hook المحسن
  const handleDeleteQuiz = useCallback(
    async (lessonId, quizId) => {
      await quizManagementHandleDeleteQuiz(lessonId, quizId);
      setQuizEditForm(null);
    },
    [quizManagementHandleDeleteQuiz]
  );

  // تعديل كويز أو واجب - استخدام الـ hook المحسن
  const handleEditQuiz = useCallback(
    async (lessonId, quizId) => {
      if (!quizEditForm?.quiz) {
        setError("بيانات الامتحان/الواجب غير متوفرة");
        return;
      }

      const quizData = {
        title: quizEditForm.quiz.title,
        description: quizEditForm.quiz.description,
        max_score: parseInt(quizEditForm.quiz.max_score) || 100, // النظام الجديد البسيط - zaki alkholy
        time_limit: parseInt(quizEditForm.quiz.time_limit) || 0,
      };

      await quizManagementHandleEditQuiz(lessonId, quizId, quizData);
      setQuizEditForm(null);
    },
    [quizManagementHandleEditQuiz, quizEditForm, setError]
  );

  // ================== تعديل فورم إضافة سؤال ==================
  // ================== دوال التعامل مع الصور - زكي الخولي ==================
  const handleImageSelect = useCallback(
    (quizId, file) => {
      if (file && file.type.startsWith("image/")) {
        setQuestionImages((prev) => ({ ...prev, [quizId]: file }));
        const reader = new FileReader();
        reader.onload = (e) => {
          setImagePreview((prev) => ({ ...prev, [quizId]: e.target.result }));
          setShowImageCropper((prev) => ({ ...prev, [quizId]: true }));
        };
        reader.readAsDataURL(file);
      }
    },
    [setQuestionImages, setImagePreview, setShowImageCropper]
  );

  // دوال react-image-crop المحسنة - زكي الخولي
  const getCroppedImg = useCallback((image, crop) => {
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    const scaleX = image.naturalWidth / image.width;
    const scaleY = image.naturalHeight / image.height;

    canvas.width = crop.width;
    canvas.height = crop.height;

    ctx.drawImage(
      image,
      crop.x * scaleX,
      crop.y * scaleY,
      crop.width * scaleX,
      crop.height * scaleY,
      0,
      0,
      crop.width,
      crop.height
    );

    return new Promise((resolve) => {
      canvas.toBlob(resolve, "image/jpeg", 0.8);
    });
  }, []);

  // ✅ removeQuestionImage متوفرة من useImageCropper hook

  // ================== منطق إضافة سؤال مع الإجابات - استخدام الـ hook المحسن ==================
  const handleAddQuestion = useCallback(
    async (quizId, { answers, image }) => {
      // استخدام الـ hook للمنطق الأساسي مع questionForm من الـ state
      await quizManagementHandleAddQuestion(
        quizId,
        answers,
        image,
        questionForm[quizId]
      );

      // UI cleanup - إغلاق المودال وتنظيف الفورم
      setShowQuestionForm((prev) => ({ ...prev, [quizId]: false }));
      setQuestionForm((prev) => ({ ...prev, [quizId]: {} }));
      setAnswersForm((prev) => ({ ...prev, [quizId]: [] }));
      // تنظيف بيانات الصور بعد إضافة السؤال
      removeQuestionImage(quizId);
    },
    [quizManagementHandleAddQuestion, questionForm]
  );

  // حذف سؤال - استخدام الـ hook المحسن
  const handleDeleteQuestion = useCallback(
    async (quizId, questionId) => {
      await quizManagementHandleDeleteQuestion(quizId, questionId);
      setQuestionEditForm(null);
    },
    [quizManagementHandleDeleteQuestion]
  );

  // تعديل سؤال مع تعديل الإجابات - تحديث لدعم الصور - زكي الخولي
  const handleEditQuestion = useCallback(
    async (quizId, questionId, { answers, image, questionData }) => {
      const token = Cookies.get("authToken");
      if (!token) {
        setError("يرجى تسجيل الدخول أولاً");
        return;
      }

      // Loading state handled by useQuizManagement hook
      try {
        // تجهيز نوع السؤال
        let qType =
          questionData?.question_type ||
          questionEditForm.question.question_type;
        if (qType === "mcq") qType = "multiple_choice";

        // إنشاء FormData لدعم رفع الصور في التعديل - زكي الخولي
        const formData = new FormData();
        formData.append("quiz", quizId);
        formData.append(
          "text",
          questionData?.text || questionEditForm.question.text || ""
        );
        formData.append("question_type", qType);
        formData.append(
          "points",
          Number(questionData?.points || questionEditForm.question.points) || 1
        );
        formData.append(
          "order",
          Number(questionData?.order || questionEditForm.question.order) || 1
        );

        // إضافة الصورة الجديدة إذا كانت موجودة - زكي الخولي
        if (image) {
          formData.append("image", image, "question-image.jpg");
        } else if (croppedImages[quizId]) {
          formData.append("image", croppedImages[quizId], "question-image.jpg");
        }
        // 1. عدل السؤال نفسه مع الصورة - زكي الخولي
        await axios.patch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/questions/${questionId}/`,
          formData,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "multipart/form-data",
            },
          }
        );
        // 2. لو السؤال MCQ أو صح/خطأ: عدل الإجابات
        if (
          (qType === "multiple_choice" || qType === "true_false") &&
          answers &&
          answers.length > 0
        ) {
          // احذف الإجابات القديمة غير الموجودة في answers الجديدة
          // حذف الإجابات القديمة غير الموجودة في الجديدة
          const oldAnswers = questionEditForm.question.answers || [];
          const newAnswers = answers;
          const oldIds = oldAnswers.map((a) => a.id);
          const newIds = newAnswers.filter((a) => a.id).map((a) => a.id);
          const toDelete = oldIds.filter((id) => !newIds.includes(id));
          for (const id of toDelete) {
            await axios.delete(
              `${process.env.NEXT_PUBLIC_API_URL}/api/answers/${id}/`,
              { headers: { Authorization: `Bearer ${token}` } }
            );
          }
          // عدل أو أضف الإجابات الجديدة
          for (const ans of newAnswers) {
            if (ans.id) {
              await axios.patch(
                `${process.env.NEXT_PUBLIC_API_URL}/api/answers/${ans.id}/`,
                {
                  text: ans.text,
                  is_correct: ans.is_correct,
                  question: questionId,
                },
                { headers: { Authorization: `Bearer ${token}` } }
              );
            } else {
              await axios.post(
                `${process.env.NEXT_PUBLIC_API_URL}/api/answers/`,
                {
                  text: ans.text,
                  is_correct: ans.is_correct,
                  question: questionId,
                },
                { headers: { Authorization: `Bearer ${token}` } }
              );
            }
          }
        }
        setQuestionEditForm(null);
        setAnswersForm({}); // امسح الإجابات المؤقتة عند غلق المودال
        // تنظيف بيانات الصور بعد تعديل السؤال - زكي الخولي
        if (questionEditForm && questionEditForm.question) {
          removeQuestionImage(`edit_${questionEditForm.question.id}`);
        }
        // تحديث الدروس
        const lessonsList = await fetchInstructorLessons(courseId, token);
        setLessons(lessonsList);

        // التحقق من درجات الامتحان بعد التعديل
        const updatedQuiz = lessonsList
          .flatMap((l) => l.quizzes || [])
          .find((q) => q.id === quizId);

        if (updatedQuiz && updatedQuiz.is_published) {
          const totalPoints = updatedQuiz.questions.reduce(
            (sum, q) => sum + Number(q.points || 0),
            0
          );

          // النظام الجديد: تحديث max_score تلقائياً - zaki alkholy
          if (totalPoints !== updatedQuiz.max_score) {
            // تحديث max_score في الباك إند
            await axios.patch(
              `${process.env.NEXT_PUBLIC_API_URL}/api/quizzes/${quizId}/`,
              { max_score: totalPoints },
              { headers: { Authorization: `Bearer ${token}` } }
            );

            // تحديث الدروس بعد التحديث
            const refreshedLessons = await fetchInstructorLessons(
              courseId,
              token
            );
            setLessons(refreshedLessons);
          }
        }
      } catch (err) {
        setError("فشل في تعديل السؤال أو الإجابات");
      } finally {
        // Loading state handled by useQuizManagement hook
      }
    },
    [questionEditForm, setError, setLessons, courseId, setQuestionEditForm]
  );

  // ================== مكون Image Cropper مبسط وآمن - زكي الخولي ==================
  const ImageCropperModal = ({ quizId, imageUrl, onClose }) => {
    const [localCrop, setLocalCrop] = useState({
      unit: "%",
      width: 50,
      height: 50,
      x: 25,
      y: 25,
    });
    const [localCompletedCrop, setLocalCompletedCrop] = useState(null);
    const localImgRef = useRef(null);

    const handleLocalCrop = async () => {
      if (localCompletedCrop && localImgRef.current) {
        const croppedBlob = await getCroppedImg(
          localImgRef.current,
          localCompletedCrop
        );
        setCroppedImages((prev) => ({ ...prev, [quizId]: croppedBlob }));
        setShowImageCropper((prev) => ({ ...prev, [quizId]: false }));
      }
    };

    return (
      <div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
        onClick={(e) => {
          if (e.target === e.currentTarget) {
            onClose();
          }
        }}
      >
        <div
          className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-auto"
          onClick={(e) => e.stopPropagation()}
        >
          <h3 className="text-lg font-bold mb-4 text-center">
            تعديل الصورة - اسحب لتحديد المنطقة المطلوبة
          </h3>

          <div className="mb-4 flex justify-center">
            <ReactCrop
              crop={localCrop}
              onChange={setLocalCrop}
              onComplete={setLocalCompletedCrop}
              aspect={undefined}
              minWidth={50}
              minHeight={50}
              keepSelection={true}
              style={{ maxWidth: "100%", maxHeight: "60vh" }}
            >
              <img
                ref={localImgRef}
                src={imageUrl}
                alt="للتعديل"
                style={{
                  maxWidth: "100%",
                  maxHeight: "60vh",
                  display: "block",
                }}
              />
            </ReactCrop>
          </div>

          <div className="flex gap-2 justify-center">
            <button
              onClick={handleLocalCrop}
              className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 transition-colors"
              disabled={!localCompletedCrop}
            >
              تطبيق القص
            </button>
            <button
              onClick={onClose}
              className="bg-gray-300 px-6 py-2 rounded hover:bg-gray-400 transition-colors"
            >
              إلغاء
            </button>
          </div>

          <div className="mt-3 text-center text-sm text-gray-600">
            💡 نصيحة: اسحب الزوايا لتغيير حجم المنطقة، واسحب المنطقة لتحريكها
          </div>
        </div>
      </div>
    );
  };

  // ================== إعداد Context Value ==================
  const courseContextValue = useMemo(
    () => ({
      // Course Data
      courseData,
      courseId,
      user,

      // Lessons
      lessons,
      setLessons,
      expandedLesson,
      setExpandedLesson,
      expandedQuizzes,
      setExpandedQuizzes,

      // Navigation & Actions
      handleSeeLesson,
      handleDeleteLesson,

      // File Upload
      handleAddFile,
      handleDeleteResource,
      deletingResource,
      showFileForm,
      setShowFileForm,
      fileInputRefs,
      isUploading,
      uploadProgress,
      uploadSuccess,
      uploadError,
      uploadedFile,

      // Quiz Management
      showQuizModal,
      setShowQuizModal,
      quizForm,
      setQuizForm,
      handleAddQuiz,
      handleDeleteQuiz,
      handleEditQuiz,
      quizEditForm,
      setQuizEditForm,

      // Question Management
      showQuestionForm,
      setShowQuestionForm,
      questionForm,
      setQuestionForm,
      answersForm,
      setAnswersForm,
      questionLoading: quizManagementQuestionLoading,
      handleAddQuestion,
      handleDeleteQuestion,
      handleEditQuestion,
      questionEditForm,
      setQuestionEditForm,

      // Image Management
      croppedImages,
      setCroppedImages,
      questionImages,
      setQuestionImages,
      imagePreview,
      setImagePreview,
      showImageCropper,
      setShowImageCropper,
      removeQuestionImage,
      getCroppedImg,

      // PDF Generation
      generateQuizPDF,

      // Components
      QuestionFormModal,
      QuizFormModal,
      FileUploadModal,
    }),
    [
      courseData,
      courseId,
      user,
      lessons,
      setLessons,
      expandedLesson,
      setExpandedLesson,
      expandedQuizzes,
      setExpandedQuizzes,
      handleSeeLesson,
      handleDeleteLesson,
      handleAddFile,
      handleDeleteResource,
      deletingResource,
      showFileForm,
      setShowFileForm,
      fileInputRefs,
      isUploading,
      uploadProgress,
      uploadSuccess,
      uploadError,
      uploadedFile,
      showQuizModal,
      setShowQuizModal,
      quizForm,
      setQuizForm,
      handleAddQuiz,
      handleDeleteQuiz,
      handleEditQuiz,
      quizEditForm,
      setQuizEditForm,
      showQuestionForm,
      setShowQuestionForm,
      questionForm,
      setQuestionForm,
      answersForm,
      setAnswersForm,
      quizManagementQuestionLoading,
      handleAddQuestion,
      handleDeleteQuestion,
      handleEditQuestion,
      questionEditForm,
      setQuestionEditForm,
      croppedImages,
      setCroppedImages,
      questionImages,
      setQuestionImages,
      imagePreview,
      setImagePreview,
      showImageCropper,
      setShowImageCropper,
      removeQuestionImage,
      getCroppedImg,
      generateQuizPDF,
      QuestionFormModal,
      QuizFormModal,
      FileUploadModal,
    ]
  );

  // ================== واجهة المستخدم الرئيسية ==================
  if (loading) return <Loader />;
  if (error) return <ErrorAlert message={error} />;
  if (!courseData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <p className="text-yellow-600">لم يتم العثور على بيانات الكورس</p>
        </div>
      </div>
    );
  }
  return (
    <div className="min-h-screen bg-gradient-to-b  from-white via-gray-50 to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 transition-colors duration-300">
      <div className="container mx-auto px-4 py-8">
        {/* Hero Section - معلومات الكورس الرئيسية */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden mb-8 animate-fade-in">
          {/* Course Header with Gradient Background */}
          <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 p-8 text-white relative overflow-hidden">
            <div className="absolute inset-0 bg-black/10"></div>
            <div className="relative z-10">
              <CourseHeader
                courseId={courseId}
                courseData={courseData}
                publishing={publishing}
                togglePublish={togglePublish}
                setShowEditModal={(show) =>
                  setEditModalState((prev) => ({ ...prev, show }))
                }
              />
            </div>
            {/* Decorative Elements */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
            <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
          </div>

          {/* Course Information */}
          <div className="p-8">
            <CourseInfo courseData={courseData} />
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
          {/* Main Content Area */}
          <div className="xl:col-span-3 space-y-8">
            {/* Lessons Section */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 animate-slide-up">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <svg
                    className="w-5 h-5 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                    />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
                  محتوى الكورس
                </h2>
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium">
                  {lessons?.length || 0} درس
                </span>
              </div>
              <CourseProvider value={courseContextValue}>
                <LessonList />
              </CourseProvider>
            </div>

            {/* Reviews Section */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 animate-slide-up">
              <ReviewList
                reviews={courseData.reviews}
                commentText={commentText}
                setCommentText={setCommentText}
                commentLoading={commentLoading}
                handleAddComment={handleAddComment}
                approving={approving}
                handleApproveReview={handleApproveReview}
                courseData={courseData}
                // إضافة props للتعليقات الشجرية - zaki alkholy
                reviewComments={reviewComments}
                replyText={replyText}
                setReplyText={setReplyText}
                showReplyForm={showReplyForm}
                onToggleReplyForm={handleToggleReplyForm}
                expandedComments={expandedComments}
                onToggleExpand={handleToggleExpand}
                currentUser={user}
                handleDeleteComment={handleDeleteComment}
                handleDeleteReview={handleDeleteReview}
              />
            </div>
          </div>

          {/* Sidebar */}
          <div className="xl:col-span-1">
            <div className="sticky top-8 space-y-6">
              {/* Course Stats Card */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 animate-slide-in-right">
                <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-4 flex items-center gap-2">
                  <svg
                    className="w-5 h-5 text-indigo-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                  إحصائيات الكورس
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-xl">
                    <span className="text-gray-600 dark:text-gray-300">
                      عدد الطلاب
                    </span>
                    <span className="font-bold text-indigo-600 dark:text-indigo-400">
                      {courseData.students_count}
                    </span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-xl">
                    <span className="text-gray-600 dark:text-gray-300">
                      التقييم
                    </span>
                    <div className="flex items-center gap-1">
                      <span className="font-bold text-yellow-500">
                        {courseData.rating}
                      </span>
                      <svg
                        className="w-4 h-4 text-yellow-500"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    </div>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-xl">
                    <span className="text-gray-600 dark:text-gray-300">
                      عدد الدروس
                    </span>
                    <span className="font-bold text-green-600 dark:text-green-400">
                      {lessons?.length || 0}
                    </span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-xl">
                    <span className="text-gray-600 dark:text-gray-300">
                      السعر
                    </span>
                    <span className="font-bold text-purple-600 dark:text-purple-400">
                      {courseData.discount_price || courseData.price}{" "}
                      {courseData.currency}
                    </span>
                  </div>
                </div>
              </div>

              {/* Students List Card */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 animate-slide-in-right">
                <StudentList
                  students={courseData.students}
                  studentsCount={courseData.students_count}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* مودال Image Cropper - زكي الخولي */}
      {Object.keys(showImageCropper).map((quizId) =>
        showImageCropper[quizId] && imagePreview[quizId] ? (
          <ImageCropperModal
            key={quizId}
            quizId={quizId}
            imageUrl={imagePreview[quizId]}
            onClose={() =>
              setShowImageCropper((prev) => ({ ...prev, [quizId]: false }))
            }
          />
        ) : null
      )}
      {editModalState.show && (
        <EditCourseModal
          courseData={courseData}
          onClose={() =>
            setEditModalState((prev) => ({ ...prev, show: false }))
          }
          // ... أي props أخرى
        />
      )}
    </div>
  );
}
