// src/app/_Components/course/QuizFormModal.jsx
import React from "react";
import Modal from "../Modal";

export default function QuizFormModal({
  isOpen,
  onClose,
  onSubmit,
  formState,
  setFormState,
  loading,
  isExam,
}) {
  return (
    <Modal isOpen={isOpen} onClose={onClose} width="max-w-2xl">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <div
            className={`w-16 h-16 ${
              isExam
                ? "bg-gradient-to-r from-red-500 to-orange-600"
                : "bg-gradient-to-r from-green-500 to-emerald-600"
            } rounded-2xl flex items-center justify-center mx-auto mb-4`}
          >
            <svg
              className="w-8 h-8 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              {isExam ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
                />
              )}
            </svg>
          </div>
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            إنشاء {isExam ? "امتحان" : "واجب"} جديد
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {isExam ? "أنشئ امتحان لتقييم الطلاب" : "أنشئ واجب لممارسة الطلاب"}
          </p>
        </div>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            console.log("Submitting quiz with formState:", formState);
            onSubmit();
          }}
          className="space-y-6"
        >
          {/* Title Input */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              العنوان
            </label>
            <input
              type="text"
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              placeholder={`عنوان ${isExam ? "الامتحان" : "الواجب"}`}
              value={formState.title || ""}
              onChange={(e) =>
                setFormState((f) => ({ ...f, title: e.target.value }))
              }
              required
            />
          </div>

          {/* Description Input */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              الوصف (اختياري)
            </label>
            <textarea
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
              placeholder={`وصف ${isExam ? "الامتحان" : "الواجب"}`}
              value={formState.description || ""}
              onChange={(e) =>
                setFormState((f) => ({ ...f, description: e.target.value }))
              }
              rows={3}
            />
          </div>

          {/* Assignment Note */}
          {!isExam && (
            <div className="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
                  <svg
                    className="w-3 h-3 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <div>
                  <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-1">
                    ملاحظة حول الواجبات
                  </h4>
                  <p className="text-sm text-blue-700 dark:text-blue-400">
                    الواجبات لا تحتاج لدرجة نجاح أو وقت محدد. كل سؤال يحصل على
                    درجة واحدة، والدرجة النهائية تساوي عدد الإجابات الصحيحة.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Exam Settings */}
          {isExam && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  الدرجة النهائية
                </label>
                <input
                  type="number"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  placeholder="100"
                  value={formState.max_score || ""}
                  onChange={(e) =>
                    setFormState((f) => ({ ...f, max_score: e.target.value }))
                  }
                />
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  الدرجة الافتراضية: 100
                </p>
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  الوقت المحدد (بالدقائق)
                </label>
                <input
                  type="number"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  placeholder="0"
                  value={formState.time_limit || ""}
                  onChange={(e) =>
                    setFormState((f) => ({ ...f, time_limit: e.target.value }))
                  }
                />
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  0 = بدون حد زمني
                </p>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="submit"
              className={`flex-1 ${
                isExam
                  ? "bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700"
                  : "bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
              } text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2`}
              disabled={loading}
            >
              {loading ? (
                <>
                  <svg
                    className="w-5 h-5 animate-spin"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    />
                  </svg>
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  إنشاء {isExam ? "الامتحان" : "الواجب"}
                </>
              )}
            </button>
            <button
              type="button"
              className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-xl font-medium hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200"
              onClick={onClose}
            >
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </Modal>
  );
}
