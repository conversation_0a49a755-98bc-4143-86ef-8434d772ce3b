import React, { useState, useEffect } from "react";
import axios from "axios";
import Cookies from "js-cookie";
import { toast } from "react-hot-toast";
import { getMainCategories } from "../../../services/categories";
import { API_BASE_URL } from "../../../config/api";

export default function EditCourseModal({ courseData, onClose }) {
  const theUrl = "https://res.cloudinary.com/djwhgnwp5/";
  const [form, setForm] = useState({
    title: courseData.title || "",
    category: courseData?.category?.id || courseData?.category || "",
    short_description: courseData.short_description || "",
    description: courseData.description || "",
    price: courseData.price || "",
    discount_price: courseData.discount_price || "",
    currency: courseData.currency || "EGP",
    level: courseData.level || "beginner",
    is_published: courseData.is_published || false,
    language: courseData.language || "Arabic",
  });
  const [thumbnail, setThumbnail] = useState(null); // للملف الجديد فقط
  const [promoVideo, setPromoVideo] = useState(null); // للملف الجديد فقط
  const [currentThumbnail, setCurrentThumbnail] = useState(
    courseData?.thumbnail || null
  ); // الصورة الحالية
  const [currentPromoVideo, setCurrentPromoVideo] = useState(
    courseData?.promo_video || null
  ); // الفيديو الحالي
  const [mainCategories, setMainCategories] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedMainCategory, setSelectedMainCategory] = useState("");
  const [loading, setLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState(null);


  useEffect(() => {
    // جلب الفئات الرئيسية
    const fetchMainCategories = async () => {
      try {
        const token = Cookies.get("authToken");
        if (!token) return;

        const response = await getMainCategories(token);
        console.log("Main categories fetched:", response.data);

        if (Array.isArray(response.data)) {
          setMainCategories(response.data);

          // إذا كان الكورس له فئة، ابحث عن الفئة الرئيسية المناسبة
          if (courseData?.category) {
            const categoryId = courseData.category?.id || courseData.category;
            for (const mainCat of response.data) {
              const foundSubcat = mainCat.subcategories?.find(
                (sub) => sub.id === categoryId
              );
              if (foundSubcat) {
                setSelectedMainCategory(mainCat.id);
                setCategories(mainCat.subcategories || []);
                break;
              }
            }
          }
        } else {
          console.error("Unexpected response format:", response.data);
          setMainCategories([]);
          toast.error("حدث خطأ في تنسيق البيانات المستلمة");
        }
      } catch (error) {
        console.error("Error fetching main categories:", error);
        toast.error("حدث خطأ أثناء جلب التصنيفات الرئيسية");
        setMainCategories([]);
      }
    };
    fetchMainCategories();
  }, [courseData]);

  // تحديث الفئات الفرعية عند تغيير الفئة الرئيسية
  useEffect(() => {
    if (selectedMainCategory) {
      const selected = mainCategories.find(
        (cat) => cat.id === selectedMainCategory
      );
      if (selected) {
        setCategories(selected.subcategories || []);
      }
    } else {
      setCategories([]);
    }
  }, [selectedMainCategory, mainCategories]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setForm((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleFileChange = (e, type) => {
    const file = e.target.files[0];
    if (type === "thumbnail") setThumbnail(file);
    else if (type === "promoVideo") setPromoVideo(file);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setUploadProgress(0);

    try {
      const formDataToSubmit = { ...form };

      // تحقق من صحة الأسعار
      const price = Number(formDataToSubmit.price);
      const discountPrice = formDataToSubmit.discount_price ? Number(formDataToSubmit.discount_price) : null;

      // تحقق من السعر الأساسي
      if (price < 100) {
        toast.error("السعر يجب أن يكون 100 جنيه على الأقل");
        setLoading(false);
        return;
      }

      // تحقق من السعر بعد الخصم
      if (discountPrice !== null) {
        if (discountPrice < 100) {
          toast.error("سعر الخصم يجب أن يكون 100 جنيه على الأقل");
          setLoading(false);
          return;
        }
        if (discountPrice >= price) {
          toast.error("السعر بعد الخصم يجب أن يكون أقل من السعر الأصلي");
          setLoading(false);
          return;
        }
      }

      const formData = new FormData();

      // إضافة البيانات النصية فقط للحقول المطلوبة
      const allowedFields = [
        "title",
        "short_description",
        "description",
        "price",
        "discount_price",
        "currency",
        "level",
        "is_published",
        "language",
      ];

      allowedFields.forEach((key) => {
        const value = formDataToSubmit[key];
        if (value !== null && value !== undefined && value !== "") {
          // تحويل القيم المنطقية إلى نص
          if (typeof value === "boolean") {
            formData.append(key, value.toString());
          } else {
            formData.append(key, value);
          }
        }
      });

      // إضافة category إذا كان موجود ومش "اخرى"
      if (formDataToSubmit.category && formDataToSubmit.category !== "اخرى") {
        formData.append("category", formDataToSubmit.category);
      }

      // إضافة الملفات الجديدة فقط إذا تم اختيارها
      if (thumbnail) {
        formData.append("thumbnail", thumbnail);
      }
      if (promoVideo) {
        formData.append("promo_video", promoVideo);
      }

      const token = Cookies.get("authToken");
      if (!token) {
        toast.error("يرجى تسجيل الدخول أولاً");
        setLoading(false);
        return;
      }

      // طباعة البيانات المرسلة للتشخيص
      console.log("Sending course update data:");
      for (let [key, value] of formData.entries()) {
        console.log(`${key}:`, value);
      }

      // استخدام slug بدلاً من id كما هو محدد في الباك إند
      const courseIdentifier = courseData.slug || courseData.id;
      console.log("Course data:", courseData);
      console.log("Using identifier:", courseIdentifier);
      console.log("Course slug:", courseData.slug);
      console.log("Course id:", courseData.id);

      const response = await axios.patch(
        `${
          process.env.NEXT_PUBLIC_API_URL || "http://127.0.0.1:8000"
        }/api/courses/${courseIdentifier}/`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "multipart/form-data",
          },
          onUploadProgress: (progressEvent) => {
            const percent = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            setUploadProgress(percent);
          },
        }
      );

      toast.success("تم تعديل الكورس بنجاح");
      console.log("Course updated:", response.data);
      onClose();
      window.location.reload(); // أو حدث بيانات الكورس في الصفحة بدل الريلود
    } catch (err) {
      console.error("Error updating course:", err);
      console.error("Error response:", err.response?.data);
      console.error("Error status:", err.response?.status);

      let errorMessage = "فشل في تعديل الكورس";

      // معالجة خطأ 404 (الكورس غير موجود)
      if (err.response?.status === 404) {
        errorMessage =
          "الكورس غير موجود أو تم حذفه. يرجى تحديث الصفحة والمحاولة مرة أخرى.";
        setError(errorMessage);
        toast.error(errorMessage);
        // إغلاق المودال وإعادة تحميل الصفحة
        setTimeout(() => {
          onClose();
          window.location.reload();
        }, 2000);
        return;
      }

      if (err.response?.data) {
        // إذا كان الرد HTML (خطأ Django)
        if (
          typeof err.response.data === "string" &&
          err.response.data.includes("<!DOCTYPE html>")
        ) {
          if (err.response.data.includes("NameError")) {
            errorMessage =
              "خطأ في الخادم: يرجى التواصل مع المطور لإصلاح مشكلة في كود الخادم";
          } else if (err.response.data.includes("Http404")) {
            errorMessage = "الكورس غير موجود أو تم حذفه";
          } else {
            errorMessage = "خطأ في الخادم: يرجى المحاولة مرة أخرى لاحقاً";
          }
        } else if (typeof err.response.data === "string") {
          errorMessage = err.response.data;
        } else if (err.response.data.message) {
          errorMessage = err.response.data.message;
        } else if (err.response.data.error) {
          // خطأ في المحفظة أو السعر
          if (err.response.data.error.includes('محفظة')) {
            errorMessage = "يجب إضافة رقم محفظة ووسيلة دفع قبل نشر الكورس. يرجى الذهاب إلى إعدادات الحساب أولاً.";
          } else if (err.response.data.error.includes('سعر')) {
            errorMessage = err.response.data.error;
          } else {
            errorMessage = err.response.data.error;
          }
        } else if (err.response.data.detail) {
          errorMessage = err.response.data.detail;
        } else {
          // إذا كان هناك أخطاء في الحقول
          const data = err.response.data;

          // خطأ في السعر
          if (data.price && data.price[0]) {
            errorMessage = data.price[0];
          }
          // خطأ في السعر بعد الخصم
          else if (data.discount_price && data.discount_price[0]) {
            errorMessage = data.discount_price[0];
          }
          // خطأ عام في validation
          else if (data.non_field_errors && data.non_field_errors[0]) {
            errorMessage = data.non_field_errors[0];
          }
          else {
            const fieldErrors = Object.entries(data)
              .map(
                ([field, errors]) =>
                  `${field}: ${
                    Array.isArray(errors) ? errors.join(", ") : errors
                  }`
              )
              .join("\n");
            if (fieldErrors) {
              errorMessage = `أخطاء في البيانات:\n${fieldErrors}`;
            }
          }
        }
      }

      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
      setUploadProgress(0);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-fade-in">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden border border-gray-200 dark:border-gray-700 animate-slide-up">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                  />
                </svg>
              </div>
              <h2 className="text-2xl font-bold">تعديل الكورس</h2>
            </div>
            <button
              type="button"
              onClick={onClose}
              className="w-8 h-8 flex items-center justify-center rounded-full bg-white/20 hover:bg-white/30 transition-all duration-200"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Course Title */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                عنوان الكورس
              </label>
              <input
                name="title"
                value={form.title}
                onChange={handleChange}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                placeholder="أدخل عنوان الكورس"
                required
              />
            </div>

            {/* التصنيفات */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* التصنيف الرئيسي */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  التصنيف الرئيسي
                </label>
                <select
                  value={selectedMainCategory}
                  onChange={(e) => setSelectedMainCategory(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                >
                  <option value="">اختر تصنيف رئيسي...</option>
                  {mainCategories.map((main) => (
                    <option key={main.id} value={main.id}>
                      {main.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* الفئة الفرعية */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  الفئة الفرعية
                </label>
                <select
                  name="category"
                  value={form.category}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  required
                >
                  <option value="">اختر الفئة...</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {`${category.name} (${category.description})`}
                    </option>
                  ))}
                  <option value="اخرى">أخرى</option>
                </select>
              </div>
            </div>

            {/* Short Description */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                الوصف المختصر
              </label>
              <textarea
                name="short_description"
                value={form.short_description}
                onChange={handleChange}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
                placeholder="وصف مختصر للكورس"
                rows={2}
                required
              />
            </div>

            {/* Full Description */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                الوصف الكامل
              </label>
              <textarea
                name="description"
                value={form.description}
                onChange={handleChange}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
                placeholder="وصف تفصيلي للكورس"
                rows={4}
                required
              />
            </div>

            {/* Current Media Display */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Current Thumbnail */}
              {currentThumbnail && (
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    الصورة الحالية
                  </label>
                  <div className="relative group">
                    <img
                      src={`${theUrl}${currentThumbnail}`}
                      alt="صورة الكورس الحالية"
                      className="w-full h-32 object-cover rounded-xl border border-gray-200 dark:border-gray-600"
                    />
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-xl flex items-center justify-center">
                      <span className="text-white text-sm font-medium">
                        الصورة الحالية
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {/* Current Video */}
              {currentPromoVideo && (
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    الفيديو الحالي
                  </label>
                  <video
                    src={`${theUrl}${currentPromoVideo}`}
                    controls
                    className="w-full h-32 rounded-xl border border-gray-200 dark:border-gray-600"
                  />
                </div>
              )}
            </div>

            {/* معاينة الملفات الجديدة */}
            {(thumbnail || promoVideo) && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* معاينة الصورة الجديدة */}
                {thumbnail && (
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-green-600 dark:text-green-400">
                      الصورة الجديدة المختارة
                    </label>
                    <div className="relative group">
                      <img
                        src={URL.createObjectURL(thumbnail)}
                        alt="الصورة الجديدة"
                        className="w-full h-32 object-cover rounded-xl border-2 border-green-300 dark:border-green-600"
                      />
                      <div className="absolute inset-0 bg-green-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-xl flex items-center justify-center">
                        <span className="text-green-800 dark:text-green-200 text-sm font-medium">
                          صورة جديدة
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center text-green-600 dark:text-green-400 text-sm">
                      <svg
                        className="w-4 h-4 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      تم اختيار: {thumbnail.name}
                    </div>
                  </div>
                )}

                {/* معاينة الفيديو الجديد */}
                {promoVideo && (
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-green-600 dark:text-green-400">
                      الفيديو الجديد المختار
                    </label>
                    <video
                      src={URL.createObjectURL(promoVideo)}
                      controls
                      className="w-full h-32 rounded-xl border-2 border-green-300 dark:border-green-600"
                    />
                    <div className="flex items-center text-green-600 dark:text-green-400 text-sm">
                      <svg
                        className="w-4 h-4 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      تم اختيار: {promoVideo.name}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* File Upload Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Thumbnail Upload */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  تحديث صورة الكورس
                </label>
                <div className="relative">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleFileChange(e, "thumbnail")}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  />
                  <div className="w-full px-4 py-3 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200 text-center">
                    <svg
                      className="w-8 h-8 mx-auto mb-2 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      اختر صورة جديدة
                    </p>
                  </div>
                </div>
              </div>

              {/* Video Upload */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  تحديث فيديو العرض
                </label>
                <div className="relative">
                  <input
                    type="file"
                    accept="video/*"
                    onChange={(e) => handleFileChange(e, "promoVideo")}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  />
                  <div className="w-full px-4 py-3 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200 text-center">
                    <svg
                      className="w-8 h-8 mx-auto mb-2 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                      />
                    </svg>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      اختر فيديو جديد
                    </p>
                  </div>
                </div>
              </div>
            </div>



            {/* Pricing Section */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  السعر الأساسي
                </label>
                <input
                  name="price"
                  type="number"
                  value={form.price}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  placeholder="100"
                  required
                />
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  سعر الخصم (اختياري)
                </label>
                <input
                  name="discount_price"
                  type="number"
                  value={form.discount_price}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  placeholder="السعر المخفض"
                />
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  العملة
                </label>
                <select
                  name="currency"
                  value={form.currency}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  required
                >
                  <option value="EGP">جنيه مصري (EGP)</option>
                  <option value="USD">دولار أمريكي (USD)</option>
                  <option value="EUR">يورو (EUR)</option>
                </select>
              </div>
            </div>
            {/* معلومات إضافية */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Course Level */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  مستوى الكورس
                </label>
                <select
                  name="level"
                  value={form.level}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  required
                >
                  <option value="beginner">مبتدئ</option>
                  <option value="intermediate">متوسط</option>
                  <option value="advanced">متقدم</option>
                </select>
              </div>

              {/* Language */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  لغة الكورس
                </label>
                <select
                  name="language"
                  value={form.language}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  required
                >
                  <option value="Arabic">العربية</option>
                  <option value="English">الإنجليزية</option>
                  <option value="French">الفرنسية</option>
                </select>
              </div>
            </div>

            {/* Publication Status */}
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-6 rounded-xl border border-green-200 dark:border-green-700">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full">
                    <svg
                      className="w-5 h-5 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-800 dark:text-white">
                      نشر الدورة
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      هل تريد أن تكون الدورة متاحة للطلاب؟
                    </p>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    name="is_published"
                    checked={form.is_published}
                    onChange={handleChange}
                    className="sr-only peer"
                  />
                  <div className="w-14 h-7 bg-gray-200 dark:bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-500/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-6 after:w-6 after:transition-all peer-checked:bg-gradient-to-r peer-checked:from-green-500 peer-checked:to-emerald-500"></div>
                </label>
              </div>
            </div>

            {/* Upload Progress */}
            {uploadProgress > 0 && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    جاري الرفع...
                  </span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {uploadProgress}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                  <div
                    className="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
              </div>
            )}

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4">
                <div className="flex items-center gap-3">
                  <svg
                    className="w-5 h-5 text-red-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <span className="text-red-700 dark:text-red-400">
                    {error}
                  </span>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <button
                type="submit"
                className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <svg
                      className="w-5 h-5 animate-spin"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      />
                    </svg>
                    جارٍ الحفظ...
                  </>
                ) : (
                  <>
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    حفظ التعديلات
                  </>
                )}
              </button>
              <button
                type="button"
                className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-xl font-medium hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={onClose}
                disabled={loading}
              >
                إلغاء
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
