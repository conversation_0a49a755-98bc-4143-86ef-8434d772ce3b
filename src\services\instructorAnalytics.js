// خدمات تحليلات المعلم: جميع استدعاءات API الخاصة بتحليلات المعلم - zaki alkholy
import axios from "axios";
import { API_BASE_URL } from '../config/api';

// ===============================
// خدمات التحليلات العامة للمعلم - zaki alkholy
// ===============================

/**
 * الحصول على تحليلات المعلم الشاملة - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} period - الفترة الزمنية (7, 30, 90 أيام)
 * @returns {Promise} بيانات التحليلات
 */
export async function fetchInstructorAnalytics(token, period = '30') {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/instructor/analytics/`, {
    headers,
    withCredentials: true,
    params: { period }
  });
  
  return response.data;
}

/**
 * الحصول على تحليلات دورة محددة - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} courseId - معرف الدورة
 * @returns {Promise} بيانات تحليلات الدورة
 */
export async function fetchCourseAnalytics(token, courseId) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/instructor/analytics/course/${courseId}/`, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * الحصول على أداء الطلاب في دورة - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} courseId - معرف الدورة
 * @returns {Promise} بيانات أداء الطلاب
 */
export async function fetchStudentPerformance(token, courseId) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/instructor/students/${courseId}/`, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * الحصول على تحليلات المبيعات - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} period - الفترة الزمنية
 * @returns {Promise} بيانات تحليلات المبيعات
 */
export async function fetchSalesAnalytics(token, period = '30') {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/instructor/sales/`, {
    headers,
    withCredentials: true,
    params: { period }
  });
  
  return response.data;
}

/**
 * الحصول على تحليلات درس محدد - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} lessonId - معرف الدرس
 * @returns {Promise} بيانات تحليلات الدرس
 */
export async function fetchLessonAnalytics(token, lessonId) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/instructor/lesson/${lessonId}/analytics/`, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * الحصول على إحصائيات لوحة تحكم المعلم - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @returns {Promise} إحصائيات لوحة التحكم
 */
export async function fetchInstructorDashboardStats(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/instructor/dashboard/stats/`, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * الحصول على أفضل المحتوى أداءً - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} period - الفترة الزمنية
 * @returns {Promise} بيانات أفضل المحتوى
 */
export async function fetchTopPerformingContent(token, period = '30') {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };

  try {
    const response = await axios.get(`${API_BASE_URL}/api/instructor/top-content/`, {
      headers,
      withCredentials: true,
      params: { period }
    });

    return response.data;
  } catch (error) {
    // في حالة خطأ في الخادم، إرجاع بيانات افتراضية - zaki alkholy
    console.warn('خطأ في جلب أفضل المحتوى، استخدام بيانات افتراضية:', error);
    return {
      top_selling_courses: [],
      top_performing_lessons: [],
      top_performing_quizzes: []
    };
  }
}

/**
 * الحصول على تحليلات الواجبات - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @returns {Promise} بيانات تحليلات الواجبات
 */
export async function fetchAssignmentAnalytics(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/instructor/assignments/analytics/`, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

// ===============================
// خدمات لوحة تحكم المعلم - zaki alkholy
// ===============================

/**
 * الحصول على بيانات لوحة تحكم المعلم - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @returns {Promise} بيانات لوحة التحكم
 */
export async function fetchInstructorDashboard(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/instructor/dashboard/`, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * تصدير تقرير تحليلات - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} reportType - نوع التقرير
 * @param {Object} params - معاملات التقرير
 * @returns {Promise} ملف التقرير
 */
export async function exportAnalyticsReport(token, reportType, params = {}) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/instructor/analytics/export/${reportType}/`, {
    headers,
    withCredentials: true,
    params,
    responseType: 'blob'
  });
  
  return response.data;
}
