import React, { useState, useEffect } from "react";
import CheckIcon from "@mui/icons-material/Check";
import AssignmentIcon from "@mui/icons-material/Assignment";
import Cookies from "js-cookie";
import AddTaskForm from "./PostToDoList";
import {
  fetchInstructorTasks,
  toggleInstructorTaskDone,
  deleteInstructorTask,
} from "../../../services/newInstructorApis"; // عدّل المسار حسب مكان الملف

const TodoList = () => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    getTasks();
  }, []);

  const getTasks = async () => {
    const token = Cookies.get("authToken");
    if (!token) return;
    setLoading(true);
    try {
      const data = await fetchInstructorTasks(token);
      setTasks(data);
    } catch (e) {
      setTasks([]);
    } finally {
      setLoading(false);
    }
  };

  const toggleDone = async (task) => {
    const token = Cookies.get("authToken");
    if (!token) return;

    try {
      await toggleInstructorTaskDone(task.id, !task.done, token);
      // تحديث الحالة مباشرة بدون إعادة الجلب (اختياري أفضل للأداء)
      setTasks((prev) =>
        prev.map((t) => (t.id === task.id ? { ...t, done: !t.done } : t))
      );
    } catch (e) {
      console.error("فشل تحديث المهمة");
    }
  };
  const handleDelete = async (id) => {
    const token = Cookies.get("authToken");
    if (!token) return;
    try {
      await deleteInstructorTask(id, token);
      setTasks((prev) => prev.filter((task) => task.id !== id));
    } catch (e) {
      alert("فشل في حذف المهمة");
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl dark:shadow-gray-900/20 border border-gray-100 dark:border-gray-700 p-6 transition-all duration-300 animate-slide-up">
      <header className="flex items-center gap-3 border-b border-gray-200 dark:border-gray-700 pb-4 mb-6 text-gray-700 dark:text-gray-300 font-bold text-lg">
        <div className="p-2 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg">
          <AssignmentIcon
            fontSize="medium"
            className="text-indigo-600 dark:text-indigo-400"
          />
        </div>
        <h3>قائمة المهام</h3>
      </header>
      <div className="mb-4">
        <AddTaskForm
          onTaskAdded={(newTask) => setTasks((prev) => [...prev, newTask])}
        />
      </div>
      <div className="h-[235px] overflow-y-auto py-2 custom-scrollbar">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
            <p className="text-gray-600 dark:text-gray-400 ml-3">
              جاري التحميل...
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {tasks.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8 text-gray-400 dark:text-gray-500">
                <div className="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-3">
                  <AssignmentIcon fontSize="medium" />
                </div>
                <p className="text-sm font-medium">لا توجد مهام حالياً</p>
                <p className="text-xs mt-1">أضف مهمة جديدة للبدء</p>
              </div>
            ) : (
              tasks.map((task, index) => (
                <div
                  key={task.id}
                  className={`group flex items-center justify-between gap-3 ml-2 p-3 rounded-xl hover:bg-gradient-to-r hover:from-gray-50 hover:to-indigo-50 dark:hover:from-gray-700/50 dark:hover:to-indigo-900/20 border border-gray-200 dark:border-gray-700 hover:border-indigo-200 dark:hover:border-indigo-700 transition-all duration-300 hover:shadow-md animate-slide-in-right ${
                    task.done ? "opacity-60" : ""
                  }`}
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  <div
                    className="flex items-center gap-3 cursor-pointer flex-1"
                    onClick={() => toggleDone(task)}
                  >
                    <div
                      className={`w-6 h-6 flex items-center justify-center border-2 rounded-lg transition-all duration-300 hover:scale-110 ${
                        task.done
                          ? "bg-gradient-to-r from-green-500 to-emerald-500 border-green-500 shadow-lg"
                          : "border-gray-300 dark:border-gray-600 hover:border-indigo-400 dark:hover:border-indigo-500"
                      }`}
                    >
                      {task.done && (
                        <CheckIcon
                          fontSize="small"
                          className="text-white animate-bounce-in"
                        />
                      )}
                    </div>
                    <span
                      className={`text-sm font-medium transition-all duration-300 ${
                        task.done
                          ? "text-gray-500 dark:text-gray-400 line-through"
                          : "text-gray-700 dark:text-gray-300 group-hover:text-indigo-700 dark:group-hover:text-indigo-300"
                      }`}
                    >
                      {task.title}
                    </span>
                  </div>

                  <button
                    onClick={() => handleDelete(task.id)}
                    className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 shadow-md hover:shadow-lg text-xs text-white font-bold px-3 py-2 rounded-lg transition-all duration-300 hover:scale-105"
                  >
                    حذف
                  </button>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default TodoList;
