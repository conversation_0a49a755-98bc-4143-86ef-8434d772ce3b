// store/persistMiddleware.js
export const persistMiddleware = (store) => (next) => (action) => {
    const result = next(action);
    
    if (action.type.startsWith('auth/')) {
      const { auth } = store.getState();
      if (typeof window !== 'undefined') {
        localStorage.setItem('authState', JSON.stringify(auth));
      }
    }
    
    return result;
  };
  
  // لمزيد من الأمان: يمكنك تفعيل تشفير حالة redux باستخدام redux-persist-transform-encrypt
  // مثال (يتطلب تثبيت الحزمة):
  // import { createTransform } from 'redux-persist';
  // import createEncryptor from 'redux-persist-transform-encrypt';
  // const encryptor = createEncryptor({
  //   secretKey: process.env.NEXT_PUBLIC_PERSIST_SECRET || 'my-super-secret-key',
  //   onError: function(error) { console.error(error); }
  // });
  // ثم أضف encryptor إلى transforms في إعدادات redux-persist
  
  // ثم أضفه في store.js
  export const store = configureStore({
    // ...
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware()
        .concat(authApi.middleware)
        .concat(persistMiddleware),
  });