import axios from "axios";
import { API_BASE_URL } from "../config/api";

// ======================================جلب كورسات المعلم فقط - zaki alkholy
export async function fetchInstructorCourses(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };

  const response = await axios.get(`${API_BASE_URL}/api/instructor/courses/`, {
    headers,
  });

  return response.data;
}

// تعديل نشر الكورس بال id
export async function toggleCoursePublish(courseId, isPublished, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
  };

  const data = {
    is_published: !isPublished,
  };

  const response = await axios.patch(
    `${API_BASE_URL}/api/courses/${courseId}/`,
    data,
    { headers }
  );

  return response.data;
}

// ======================================جلب الكورسات العشوائية للصفحة الرئيسية - بدون token - zaki alkholy
export async function fetchFeaturedCourses(options = {}) {
  try {
    const {
      category = null,
      level = null,
      limit = 6,
      randomize = true,
    } = options;

    // بناء query parameters
    const params = new URLSearchParams();

    if (category && category !== "الكل") {
      params.append("category", category);
    }

    if (level) {
      params.append("level", level);
    }

    // إضافة ترتيب عشوائي
    if (randomize) {
      params.append("randomize", "true");
    }

    // إضافة حد أقصى للنتائج
    if (limit) {
      params.append("limit", limit.toString());
    }

    const headers = {
      "Content-Type": "application/json",
    };

    // استخدام API العام الجديد بدون authentication
    const response = await axios.get(
      `${API_BASE_URL}/api/public-courses/?${params.toString()}`,
      {
        headers,
        timeout: 10000, // 10 ثوان timeout
        validateStatus: function (status) {
          return status >= 200 && status < 500;
        },
      }
    );

    // التحقق من حالة الاستجابة
    if (response.status >= 400) {
      throw new Error(`خطأ في الخادم: ${response.status}`);
    }

    return response.data.results || response.data || [];
  } catch (error) {
    // إعادة رمي الخطأ مع رسالة واضحة
    if (error.code === "ECONNABORTED") {
      throw new Error("انتهت مهلة الاتصال بالخادم");
    }

    if (error.message) {
      throw error;
    }

    throw new Error("حدث خطأ أثناء تحميل الكورسات");
  }
}

// ======================================جلب جميع الكورسات مع الفلترة والبحث - بدون token - zaki alkholy
export async function fetchAllCourses(options = {}) {
  try {
    const {
      category = null,
      level = null,
      search = null,
      instructor = null,
      page = 1,
      pageSize = 12,
    } = options;

    // بناء query parameters
    const params = new URLSearchParams();

    if (category && category !== "الكل") {
      params.append("category", category);
    }

    if (level && level !== "الكل") {
      params.append("level", level);
    }

    if (search) {
      params.append("search", search);
    }

    if (instructor) {
      params.append("instructor", instructor);
    }

    // إضافة pagination
    params.append("page", page.toString());
    if (pageSize) {
      params.append("page_size", pageSize.toString());
    }

    const headers = {
      "Content-Type": "application/json",
    };

    // استخدام API العام الجديد بدون authentication
    const response = await axios.get(
      `${API_BASE_URL}/api/public-courses/?${params.toString()}`,
      {
        headers,
        timeout: 15000, // 15 ثانية timeout للبحث
        validateStatus: function (status) {
          return status >= 200 && status < 500;
        },
      }
    );

    // التحقق من حالة الاستجابة
    if (response.status >= 400) {
      throw new Error(`خطأ في الخادم: ${response.status}`);
    }

    return response.data;
  } catch (error) {
    // إعادة رمي الخطأ مع رسالة واضحة
    if (error.code === "ECONNABORTED") {
      throw new Error("انتهت مهلة الاتصال بالخادم");
    }

    if (error.message) {
      throw error;
    }

    throw new Error("حدث خطأ أثناء تحميل الكورسات");
  }
}

// ======================================جلب الفئات المتاحة - بدون token - zaki alkholy
export async function fetchCategories() {
  try {
    const headers = {
      "Content-Type": "application/json",
    };

    // استخدام API العام الجديد بدون authentication
    const response = await axios.get(`${API_BASE_URL}/api/public-categories/`, {
      headers,
      timeout: 10000,
      validateStatus: function (status) {
        return status >= 200 && status < 500;
      },
    });

    // التحقق من حالة الاستجابة
    if (response.status >= 400) {
      throw new Error(`خطأ في الخادم: ${response.status}`);
    }

    return response.data.results || response.data || [];
  } catch (error) {
    // إعادة رمي الخطأ مع رسالة واضحة
    if (error.code === "ECONNABORTED") {
      throw new Error("انتهت مهلة الاتصال بالخادم");
    }

    if (error.message) {
      throw error;
    }

    throw new Error("حدث خطأ أثناء تحميل الفئات");
  }
}
