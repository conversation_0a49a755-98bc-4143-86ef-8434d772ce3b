// صفحة اختبار API للمطورين - zaki alkholy
'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useSelector } from 'react-redux';
import Cookies from 'js-cookie';
import { toast } from 'react-hot-toast';
import { 
  Play, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  RefreshCw,
  Download,
  Settings,
  Monitor,
  Database,
  Wifi
} from 'lucide-react';

// استيراد أدوات الاختبار - zaki alkholy
import {
  runComprehensiveTest,
  testBasicConnection,
  testAuthEndpoints,
  testStudentEndpoints,
  testInstructorEndpoints
} from '../../../../utils/apiTester';

import { selectCurrentUser, selectIsAuthenticated } from '../../../../store/authSlice';

// مكون لعرض نتيجة اختبار - zaki alkholy
const TestResult = ({ name, status, details }) => {
  const getStatusIcon = () => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      default:
        return <Monitor className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`p-4 rounded-lg border-2 ${getStatusColor()}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          {getStatusIcon()}
          <span className="mr-3 font-medium">{name}</span>
        </div>
        {details && (
          <span className="text-sm text-gray-600">{details}</span>
        )}
      </div>
    </motion.div>
  );
};

// مكون لعرض إحصائيات الاختبار - zaki alkholy
const TestStats = ({ results }) => {
  const calculateStats = () => {
    const allTests = {
      ...results.authEndpoints,
      ...results.studentEndpoints,
      ...results.instructorEndpoints,
    };

    const total = Object.keys(allTests).length;
    const passed = Object.values(allTests).filter(Boolean).length;
    const failed = total - passed;
    const successRate = total > 0 ? Math.round((passed / total) * 100) : 0;

    return { total, passed, failed, successRate };
  };

  const stats = calculateStats();

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div className="bg-blue-50 border-2 border-blue-200 rounded-lg p-4 text-center">
        <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
        <div className="text-sm text-blue-600">إجمالي الاختبارات</div>
      </div>
      <div className="bg-green-50 border-2 border-green-200 rounded-lg p-4 text-center">
        <div className="text-2xl font-bold text-green-600">{stats.passed}</div>
        <div className="text-sm text-green-600">نجح</div>
      </div>
      <div className="bg-red-50 border-2 border-red-200 rounded-lg p-4 text-center">
        <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
        <div className="text-sm text-red-600">فشل</div>
      </div>
      <div className="bg-purple-50 border-2 border-purple-200 rounded-lg p-4 text-center">
        <div className="text-2xl font-bold text-purple-600">{stats.successRate}%</div>
        <div className="text-sm text-purple-600">معدل النجاح</div>
      </div>
    </div>
  );
};

// الصفحة الرئيسية لاختبار API - zaki alkholy
export default function ApiTestPage() {
  const [testResults, setTestResults] = useState(null);
  const [isRunning, setIsRunning] = useState(false);
  const [selectedTest, setSelectedTest] = useState('comprehensive');

  // الحصول على بيانات المستخدم من Redux - zaki alkholy
  const user = useSelector(selectCurrentUser);
  const isAuthenticated = useSelector(selectIsAuthenticated);

  // تشغيل اختبار شامل - zaki alkholy
  const runTest = async (testType = 'comprehensive') => {
    setIsRunning(true);
    setTestResults(null);

    try {
      const token = Cookies.get('authToken') || localStorage.getItem('access_token');
      let results = {};

      switch (testType) {
        case 'basic':
          const basicResult = await testBasicConnection();
          results = { basicConnection: basicResult };
          break;
        
        case 'auth':
          const authResults = await testAuthEndpoints(token);
          results = { authEndpoints: authResults };
          break;
        
        case 'student':
          if (token) {
            const studentResults = await testStudentEndpoints(token);
            results = { studentEndpoints: studentResults };
          } else {
            toast.error('يجب تسجيل الدخول لاختبار endpoints الطلاب');
            return;
          }
          break;
        
        case 'instructor':
          if (token) {
            const instructorResults = await testInstructorEndpoints(token);
            results = { instructorEndpoints: instructorResults };
          } else {
            toast.error('يجب تسجيل الدخول لاختبار endpoints المعلمين');
            return;
          }
          break;
        
        default:
          results = await runComprehensiveTest(token);
      }

      setTestResults(results);
    } catch (error) {
      toast.error('حدث خطأ أثناء تشغيل الاختبار');
    } finally {
      setIsRunning(false);
    }
  };

  // تصدير نتائج الاختبار - zaki alkholy
  const exportResults = () => {
    if (!testResults) return;

    const dataStr = JSON.stringify(testResults, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `api-test-results-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* العنوان الرئيسي - zaki alkholy */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center">
            <Settings className="w-8 h-8 mr-3 text-blue-600" />
            اختبار API - أدوات المطورين
          </h1>
          <p className="text-gray-600">
            اختبر جميع endpoints الخاصة بالنظام للتأكد من عملها بشكل صحيح
          </p>
        </motion.div>

        {/* معلومات الحالة - zaki alkholy */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <h2 className="text-lg font-semibold mb-4 flex items-center">
            <Monitor className="w-5 h-5 mr-2 text-blue-500" />
            معلومات النظام
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center">
              <Wifi className="w-5 h-5 mr-2 text-green-500" />
              <span>الحالة: متصل</span>
            </div>
            <div className="flex items-center">
              <Database className="w-5 h-5 mr-2 text-blue-500" />
              <span>المستخدم: {isAuthenticated ? user?.username || 'مسجل دخول' : 'غير مسجل'}</span>
            </div>
            <div className="flex items-center">
              <CheckCircle className="w-5 h-5 mr-2 text-purple-500" />
              <span>البيئة: {process.env.NODE_ENV}</span>
            </div>
          </div>
        </div>

        {/* أزرار التحكم - zaki alkholy */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <h2 className="text-lg font-semibold mb-4">اختبارات متاحة</h2>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
            {[
              { id: 'comprehensive', label: 'اختبار شامل', icon: Play },
              { id: 'basic', label: 'اتصال أساسي', icon: Wifi },
              { id: 'auth', label: 'المصادقة', icon: CheckCircle },
              { id: 'student', label: 'الطلاب', icon: Monitor },
              { id: 'instructor', label: 'المعلمين', icon: Settings },
            ].map((test) => (
              <button
                key={test.id}
                onClick={() => setSelectedTest(test.id)}
                className={`p-3 rounded-lg border-2 transition-all duration-200 flex flex-col items-center ${
                  selectedTest === test.id
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <test.icon className="w-5 h-5 mb-2" />
                <span className="text-sm font-medium">{test.label}</span>
              </button>
            ))}
          </div>

          <div className="flex gap-4">
            <button
              onClick={() => runTest(selectedTest)}
              disabled={isRunning}
              className="flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isRunning ? (
                <RefreshCw className="w-5 h-5 mr-2 animate-spin" />
              ) : (
                <Play className="w-5 h-5 mr-2" />
              )}
              {isRunning ? 'جاري التشغيل...' : 'تشغيل الاختبار'}
            </button>

            {testResults && (
              <button
                onClick={exportResults}
                className="flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <Download className="w-5 h-5 mr-2" />
                تصدير النتائج
              </button>
            )}
          </div>
        </div>

        {/* نتائج الاختبار - zaki alkholy */}
        {testResults && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
          >
            <h2 className="text-lg font-semibold mb-4">نتائج الاختبار</h2>
            
            <TestStats results={testResults} />

            <div className="space-y-4">
              {/* الاتصال الأساسي */}
              {testResults.basicConnection !== undefined && (
                <TestResult
                  name="الاتصال الأساسي"
                  status={testResults.basicConnection ? 'success' : 'error'}
                  details={testResults.basicConnection ? 'متصل' : 'فشل الاتصال'}
                />
              )}

              {/* endpoints المصادقة */}
              {testResults.authEndpoints && Object.keys(testResults.authEndpoints).length > 0 && (
                <div>
                  <h3 className="font-medium mb-2">endpoints المصادقة</h3>
                  <div className="space-y-2 mr-4">
                    {Object.entries(testResults.authEndpoints).map(([key, value]) => (
                      <TestResult
                        key={key}
                        name={key}
                        status={value ? 'success' : 'error'}
                        details={value ? 'يعمل' : 'لا يعمل'}
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* endpoints الطلاب */}
              {testResults.studentEndpoints && Object.keys(testResults.studentEndpoints).length > 0 && (
                <div>
                  <h3 className="font-medium mb-2">endpoints الطلاب</h3>
                  <div className="space-y-2 mr-4">
                    {Object.entries(testResults.studentEndpoints).map(([key, value]) => (
                      <TestResult
                        key={key}
                        name={key}
                        status={value ? 'success' : 'error'}
                        details={value ? 'يعمل' : 'لا يعمل'}
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* endpoints المعلمين */}
              {testResults.instructorEndpoints && Object.keys(testResults.instructorEndpoints).length > 0 && (
                <div>
                  <h3 className="font-medium mb-2">endpoints المعلمين</h3>
                  <div className="space-y-2 mr-4">
                    {Object.entries(testResults.instructorEndpoints).map(([key, value]) => (
                      <TestResult
                        key={key}
                        name={key}
                        status={value ? 'success' : 'error'}
                        details={value ? 'يعمل' : 'لا يعمل'}
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}
