// صفحة المراجعة المتباعدة للطلاب - zaki alkholy
'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useSelector } from 'react-redux';
import Cookies from 'js-cookie';
import { toast } from 'react-hot-toast';
import {
  Brain,
  Calendar,
  Clock,
  Target,
  CheckCircle,
  AlertCircle,
  BookOpen,
  Zap,
  TrendingUp,
  RotateCcw,
  Star,
  Award
} from 'lucide-react';

// استيراد خدمات API الجديدة - zaki alkholy
import {
  fetchDailyReview,
  fetchReviewStats,
  fetchReviewRecommendations
} from '@/services/student';

import { selectCurrentUser, selectIsAuthenticated } from '@/store/authSlice';

// مكون لعرض بطاقة المراجعة - zaki alkholy
const ReviewCard = ({ review, onStartReview }) => {
  const getDifficultyColor = (level) => {
    switch (level) {
      case 1: return 'bg-green-100 text-green-600';
      case 2: return 'bg-blue-100 text-blue-600';
      case 3: return 'bg-yellow-100 text-yellow-600';
      case 4: return 'bg-orange-100 text-orange-600';
      case 5: return 'bg-red-100 text-red-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const getContentIcon = (type) => {
    switch (type) {
      case 'lesson': return '📚';
      case 'quiz': return '❓';
      case 'vocabulary': return '📝';
      case 'concept': return '💡';
      default: return '📄';
    }
  };

  const isOverdue = new Date(review.next_review_date) < new Date();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-white rounded-xl shadow-sm border-2 p-6 hover:shadow-md transition-all duration-300 ${
        isOverdue ? 'border-red-200' : 'border-gray-200'
      }`}
    >
      {/* رأس البطاقة - zaki alkholy */}
      <div className="flex justify-between items-start mb-4">
        <div className="flex items-center">
          <span className="text-2xl mr-3">{getContentIcon(review.content_type)}</span>
          <div>
            <h3 className="font-semibold text-lg">{review.content_title}</h3>
            <p className="text-sm text-gray-600">{review.course_title}</p>
          </div>
        </div>
        {isOverdue && (
          <span className="px-2 py-1 bg-red-100 text-red-600 rounded-full text-xs font-medium">
            متأخر
          </span>
        )}
      </div>

      {/* تفاصيل المراجعة - zaki alkholy */}
      <div className="space-y-3 mb-4">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">موعد المراجعة:</span>
          <span className={isOverdue ? 'text-red-600 font-medium' : 'text-gray-900'}>
            {new Date(review.next_review_date).toLocaleDateString('ar-EG')}
          </span>
        </div>

        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">عدد المراجعات:</span>
          <span className="text-gray-900">{review.review_count}</span>
        </div>

        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">معدل النجاح:</span>
          <span className="text-gray-900">
            {review.review_count > 0 ? Math.round((review.success_count / review.review_count) * 100) : 0}%
          </span>
        </div>

        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">مستوى الصعوبة:</span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(review.difficulty_level)}`}>
            {review.difficulty_level}/5
          </span>
        </div>
      </div>

      {/* ملخص المحتوى - zaki alkholy */}
      {review.content_summary && (
        <div className="bg-gray-50 rounded-lg p-3 mb-4">
          <p className="text-sm text-gray-700 line-clamp-2">{review.content_summary}</p>
        </div>
      )}

      {/* زر بدء المراجعة - zaki alkholy */}
      <button
        onClick={() => onStartReview(review)}
        className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-300 ${
          isOverdue
            ? 'bg-red-600 text-white hover:bg-red-700'
            : 'bg-blue-600 text-white hover:bg-blue-700'
        }`}
      >
        {isOverdue ? 'مراجعة متأخرة' : 'بدء المراجعة'}
      </button>
    </motion.div>
  );
};

// مكون لعرض إحصائيات المراجعة - zaki alkholy
const ReviewStats = ({ stats }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">إجمالي العناصر</p>
            <p className="text-2xl font-bold mt-1">{stats.total_items}</p>
          </div>
          <BookOpen className="w-8 h-8 text-blue-600" />
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        delay={0.1}
        className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">تم إتقانها</p>
            <p className="text-2xl font-bold mt-1 text-green-600">{stats.mastered_items}</p>
          </div>
          <Award className="w-8 h-8 text-green-600" />
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        delay={0.2}
        className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">مراجعة اليوم</p>
            <p className="text-2xl font-bold mt-1 text-orange-600">{stats.today_reviews}</p>
          </div>
          <Calendar className="w-8 h-8 text-orange-600" />
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        delay={0.3}
        className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">معدل النجاح</p>
            <p className="text-2xl font-bold mt-1 text-purple-600">{stats.success_rate}%</p>
          </div>
          <TrendingUp className="w-8 h-8 text-purple-600" />
        </div>
      </motion.div>
    </div>
  );
};

// مكون لعرض التقدم اليومي - zaki alkholy
const DailyProgress = ({ todayReviews, completedToday }) => {
  const progressPercentage = todayReviews > 0 ? (completedToday / todayReviews) * 100 : 0;

  return (
    <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl p-6 mb-8">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h2 className="text-xl font-bold mb-2">تقدم اليوم</h2>
          <p className="text-blue-100">
            {completedToday} من {todayReviews} مراجعة مكتملة
          </p>
        </div>
        <div className="text-right">
          <div className="text-3xl font-bold">{Math.round(progressPercentage)}%</div>
          <div className="text-sm text-blue-100">مكتمل</div>
        </div>
      </div>
      
      <div className="w-full bg-blue-400 bg-opacity-30 rounded-full h-3">
        <motion.div
          initial={{ width: 0 }}
          animate={{ width: `${progressPercentage}%` }}
          transition={{ duration: 1, ease: "easeOut" }}
          className="bg-white h-3 rounded-full"
        />
      </div>
    </div>
  );
};

// مكون لعرض التوصيات - zaki alkholy
const ReviewRecommendations = ({ recommendations }) => {
  const getRecommendationIcon = (type) => {
    switch (type) {
      case 'urgent': return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'overdue': return <Clock className="w-5 h-5 text-orange-500" />;
      case 'weak': return <Target className="w-5 h-5 text-yellow-500" />;
      default: return <Brain className="w-5 h-5 text-blue-500" />;
    }
  };

  // التأكد من أن recommendations هو array - zaki alkholy
  const recommendationsList = Array.isArray(recommendations) ? recommendations : [];

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
      <h3 className="text-lg font-semibold mb-4 flex items-center">
        <Brain className="w-5 h-5 mr-2 text-purple-500" />
        توصيات ذكية
      </h3>

      <div className="space-y-3">
        {recommendationsList.length > 0 ? (
          recommendationsList.map((rec, index) => (
            <div key={index} className="flex items-center p-3 bg-gray-50 rounded-lg">
              {getRecommendationIcon(rec.type)}
              <div className="mr-3 flex-1">
                <h4 className="font-medium text-sm">{rec.title}</h4>
                <p className="text-xs text-gray-600">{rec.description}</p>
              </div>
              <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                {rec.count}
              </span>
            </div>
          ))
        ) : (
          <div className="text-center text-gray-500 py-4">
            <Brain className="w-12 h-12 mx-auto mb-2 text-gray-400" />
            <p className="text-sm">لا توجد توصيات متاحة حالياً</p>
            <p className="text-xs">ابدأ المراجعة لتحصل على توصيات ذكية!</p>
          </div>
        )}
      </div>
    </div>
  );
};

// الصفحة الرئيسية للمراجعة المتباعدة - zaki alkholy
export default function StudentReviewPage() {
  const [reviewData, setReviewData] = useState(null);
  const [statsData, setStatsData] = useState(null);
  const [recommendationsData, setRecommendationsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('today');

  // الحصول على بيانات المستخدم من Redux - zaki alkholy
  const user = useSelector(selectCurrentUser);
  const isAuthenticated = useSelector(selectIsAuthenticated);

  // جلب بيانات المراجعة من API - zaki alkholy
  useEffect(() => {
    const fetchAllReviewData = async () => {
      if (!isAuthenticated || !user) {
        setLoading(false);
        return;
      }

      try {
        const token = Cookies.get('authToken') || localStorage.getItem('access_token');
        if (!token) {
          throw new Error('لا يوجد رمز مصادقة');
        }

        // جلب جميع البيانات المطلوبة بشكل متوازي - zaki alkholy
        const [
          dailyReviewData,
          statsData,
          recommendationsData
        ] = await Promise.all([
          fetchDailyReview(token),
          fetchReviewStats(token),
          fetchReviewRecommendations(token)
        ]);

        setReviewData(dailyReviewData);
        setStatsData(statsData);
        setRecommendationsData(recommendationsData);

      } catch (error) {
        console.error('خطأ في جلب بيانات المراجعة:', error);
        setError(error.message);
        toast.error('حدث خطأ في جلب بيانات المراجعة');
      } finally {
        setLoading(false);
      }
    };

    fetchAllReviewData();
  }, [isAuthenticated, user]);

  // معالجة بدء المراجعة - zaki alkholy
  const handleStartReview = (review) => {
    // التوجه لصفحة جلسة المراجعة
    window.location.href = `/student/review/session/${review.id}`;
  };

  // عرض شاشة التحميل - zaki alkholy
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // عرض رسالة خطأ إذا لم يكن المستخدم مسجل دخول - zaki alkholy
  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">يجب تسجيل الدخول أولاً</h2>
          <p className="text-gray-600">يرجى تسجيل الدخول لعرض جدول المراجعة</p>
        </div>
      </div>
    );
  }

  // عرض رسالة خطأ إذا فشل تحميل البيانات - zaki alkholy
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">حدث خطأ</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    );
  }

  // دمج البيانات الحقيقية مع البيانات الافتراضية - zaki alkholy
  const displayData = {
    daily_review: {
      today_reviews: reviewData?.today_reviews || [],
      overdue_reviews: reviewData?.overdue_reviews || []
    },
    stats: statsData || {
      total_items: 0,
      mastered_items: 0,
      today_reviews: 0,
      success_rate: 0,
    },
    recommendations: recommendationsData || []
  };

  const allReviews = [
    ...displayData.daily_review.today_reviews,
    ...displayData.daily_review.overdue_reviews
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* العنوان الرئيسي - zaki alkholy */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center">
            <Brain className="w-8 h-8 mr-3 text-purple-600" />
            المراجعة الذكية
          </h1>
          <p className="text-gray-600">
            راجع ما تعلمته بطريقة علمية لضمان عدم النسيان
          </p>
        </motion.div>

        {/* إحصائيات المراجعة - zaki alkholy */}
        <ReviewStats stats={displayData.stats} />

        {/* التقدم اليومي - zaki alkholy */}
        <DailyProgress
          todayReviews={displayData.daily_review.today_reviews.length}
          completedToday={0}
        />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* العمود الرئيسي - المراجعات - zaki alkholy */}
          <div className="lg:col-span-2">
            {/* التبويبات - zaki alkholy */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
              <div className="border-b border-gray-200">
                <nav className="flex space-x-8 px-6">
                  {[
                    { id: 'today', label: 'اليوم', count: displayData.daily_review.today_reviews.length },
                    { id: 'overdue', label: 'متأخرة', count: displayData.daily_review.overdue_reviews.length },
                    { id: 'upcoming', label: 'قادمة', count: 0 },
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                        activeTab === tab.id
                          ? 'border-purple-500 text-purple-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700'
                      }`}
                    >
                      {tab.label}
                      {tab.count > 0 && (
                        <span className="mr-2 bg-gray-200 text-gray-600 px-2 py-1 rounded-full text-xs">
                          {tab.count}
                        </span>
                      )}
                    </button>
                  ))}
                </nav>
              </div>
            </div>

            {/* قائمة المراجعات - zaki alkholy */}
            <div className="space-y-4">
              {activeTab === 'today' && (
                displayData.daily_review.today_reviews.length > 0 ? (
                  displayData.daily_review.today_reviews.map((review) => (
                    <ReviewCard
                      key={review.id}
                      review={review}
                      onStartReview={handleStartReview}
                    />
                  ))
                ) : (
                  <div className="text-center py-8">
                    <CheckCircle className="w-16 h-16 text-green-400 mx-auto mb-4" />
                    <p className="text-gray-600">ممتاز! لا توجد مراجعات مطلوبة اليوم</p>
                    <p className="text-sm text-gray-500">استمتع بيومك!</p>
                  </div>
                )
              )}

              {activeTab === 'overdue' && (
                displayData.daily_review.overdue_reviews.length > 0 ? (
                  displayData.daily_review.overdue_reviews.map((review) => (
                    <ReviewCard
                      key={review.id}
                      review={review}
                      onStartReview={handleStartReview}
                    />
                  ))
                ) : (
                  <div className="text-center py-8">
                    <CheckCircle className="w-16 h-16 text-green-400 mx-auto mb-4" />
                    <p className="text-gray-600">رائع! لا توجد مراجعات متأخرة</p>
                    <p className="text-sm text-gray-500">أنت محدث مع جدول المراجعة</p>
                  </div>
                )
              )}

              {activeTab === 'upcoming' && (
                <div className="text-center py-8">
                  <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">لا توجد مراجعات قادمة اليوم</p>
                </div>
              )}
            </div>
          </div>

          {/* العمود الجانبي - التوصيات - zaki alkholy */}
          <div className="lg:col-span-1">
            <ReviewRecommendations recommendations={displayData.recommendations} />
            
            {/* زر المراجعة التلقائية - zaki alkholy */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <Zap className="w-5 h-5 mr-2 text-yellow-500" />
                مراجعة سريعة
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                ابدأ جلسة مراجعة سريعة لأهم المواضيع
              </p>
              <button className="w-full py-3 px-4 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors font-medium">
                بدء المراجعة السريعة
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
