// Image helpers for cropping and preview

export function handleImageSelect(file, setImage, setPreview, setShowCropper) {
  if (file && file.type.startsWith('image/')) {
    setImage(file);
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreview(e.target.result);
      setShowCropper(true);
    };
    reader.readAsDataURL(file);
  }
}

export function getCroppedImg(image, crop) {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  const scaleX = image.naturalWidth / image.width;
  const scaleY = image.naturalHeight / image.height;
  canvas.width = crop.width;
  canvas.height = crop.height;
  ctx.drawImage(
    image,
    crop.x * scaleX,
    crop.y * scaleY,
    crop.width * scaleX,
    crop.height * scaleY,
    0,
    0,
    crop.width,
    crop.height
  );
  return new Promise((resolve) => {
    canvas.toBlob(resolve, 'image/jpeg', 0.8);
  });
}

export function removeImage(setImage, setPreview, setCropped, setShowCropper) {
  setImage(null);
  setPreview(null);
  setCropped(null);
  setShowCropper(false);
}
