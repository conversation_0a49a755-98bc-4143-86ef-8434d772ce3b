/**
 * Dynamic Watermark Component - زكي الخولي
 * نظام حماية متقدم باستخدام Canvas للفيديوهات المحمية
 * يعرض watermark ديناميكي يتحرك كل 10 ثواني في أماكن عشوائية
 */

import React, { useEffect, useRef, useState } from 'react';

const DynamicWatermark = ({ 
  user, 
  videoElement, 
  isVisible = true,
  className = "" 
}) => {
  const canvasRef = useRef(null);
  const animationRef = useRef(null);
  const intervalRef = useRef(null);
  const [currentPosition, setCurrentPosition] = useState({ x: 20, y: 20 });
  const [isFullscreen, setIsFullscreen] = useState(false);

  // مواضع الـ watermark المختلفة - زكي الخولي
  const positions = [
    { x: 20, y: 20 },           // أعلى يسار
    { x: -20, y: 20 },          // أعلى يمين
    { x: 20, y: -20 },          // أسفل يسار
    { x: -20, y: -20 },         // أسفل يمين
    { x: 0, y: 20 },            // أعلى وسط
    { x: 0, y: -20 },           // أسفل وسط
    { x: -100, y: 0 },          // وسط يمين
    { x: 100, y: 0 },           // وسط يسار
  ];

  // تحديث حجم Canvas ليتطابق مع الفيديو - زكي الخولي
  const updateCanvasSize = () => {
    const canvas = canvasRef.current;
    const video = videoElement?.current;

    if (!canvas || !video) return;

    // التحقق من حالة Fullscreen - زكي الخولي
    const isCurrentlyFullscreen = !!(
      document.fullscreenElement ||
      document.webkitFullscreenElement ||
      document.mozFullScreenElement ||
      document.msFullscreenElement
    );

    let rect;
    if (isCurrentlyFullscreen) {
      // في حالة Fullscreen، استخدم حجم الشاشة الكامل
      rect = {
        width: window.innerWidth,
        height: window.innerHeight
      };
    } else {
      // في الحالة العادية، استخدم حجم الفيديو
      rect = video.getBoundingClientRect();
    }

    canvas.width = rect.width;
    canvas.height = rect.height;
    canvas.style.width = `${rect.width}px`;
    canvas.style.height = `${rect.height}px`;

    console.log(`Canvas size updated - زكي الخولي: ${rect.width}x${rect.height}, Fullscreen: ${isCurrentlyFullscreen}`);
  };

  // رسم الـ watermark على Canvas - زكي الخولي
  const drawWatermark = () => {
    const canvas = canvasRef.current;
    if (!canvas || !user) {
      console.log('drawWatermark: Missing canvas or user - زكي الخولي', {
        canvas: !!canvas,
        user: !!user
      });
      return;
    }

    console.log('Drawing watermark - زكي الخولي:', {
      canvasSize: `${canvas.width}x${canvas.height}`,
      position: currentPosition,
      userEmail: user.email,
      userName: user.username
    });

    const ctx = canvas.getContext('2d');
    const { width, height } = canvas;
    
    // مسح Canvas
    ctx.clearRect(0, 0, width, height);

    // إعداد النص - زكي الخولي
    const emailText = user.email || '<EMAIL>';
    const usernameText = user.username || 'username';
    const watermarkText = `${emailText} | ${usernameText}`;

    // إعداد الخط والحجم حسب حجم الفيديو - زكي الخولي
    const fontSize = Math.max(12, Math.min(width / 40, 18));
    ctx.font = `bold ${fontSize}px Arial, sans-serif`;
    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.8)';
    ctx.lineWidth = 2;
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';

    // حساب الموضع الفعلي - زكي الخولي
    const textMetrics = ctx.measureText(watermarkText);
    const textWidth = textMetrics.width;
    const textHeight = fontSize;

    let x = currentPosition.x;
    let y = currentPosition.y;

    // تعديل الموضع حسب الإشارة السالبة - زكي الخولي
    if (x < 0) x = width + x - textWidth;
    if (y < 0) y = height + y - textHeight;
    if (x === 0) x = (width - textWidth) / 2;
    if (y === 0) y = (height - textHeight) / 2;

    // التأكد من أن النص داخل حدود Canvas - زكي الخولي
    x = Math.max(10, Math.min(x, width - textWidth - 10));
    y = Math.max(10, Math.min(y, height - textHeight - 10));

    // رسم النص مع تأثير الظل - زكي الخولي
    ctx.strokeText(watermarkText, x, y);
    ctx.fillText(watermarkText, x, y);

    // إضافة نص إضافي صغير - زكي الخولي
    const smallText = 'محمي';
    ctx.font = `${fontSize * 0.7}px Arial`;
    ctx.fillStyle = 'rgba(255, 0, 0, 0.7)';
    ctx.fillText(smallText, x, y + textHeight + 5);
  };

  // تغيير موضع الـ watermark عشوائياً - زكي الخولي
  const changePosition = () => {
    const randomIndex = Math.floor(Math.random() * positions.length);
    const newPosition = positions[randomIndex];
    console.log(`تغيير موضع الـ watermark - زكي الخولي:`, newPosition);
    setCurrentPosition(newPosition);

    // إعادة رسم فوري بعد تغيير الموضع
    setTimeout(() => {
      updateCanvasSize();
      drawWatermark();
    }, 100);
  };

  // مراقبة تغيير حالة Fullscreen - زكي الخولي
  const handleFullscreenChange = () => {
    const isCurrentlyFullscreen = !!(
      document.fullscreenElement ||
      document.webkitFullscreenElement ||
      document.mozFullScreenElement ||
      document.msFullscreenElement
    );

    console.log(`Fullscreen changed - زكي الخولي: ${isCurrentlyFullscreen}`);
    setIsFullscreen(isCurrentlyFullscreen);

    // تحديث فوري للـ Canvas عند تغيير حالة Fullscreen
    setTimeout(() => {
      updateCanvasSize();
      drawWatermark();
    }, 200);
  };

  // مراقبة تغيير حجم النافذة - زكي الخولي
  const handleResize = () => {
    console.log('Window resized - زكي الخولي');
    setTimeout(() => {
      updateCanvasSize();
      drawWatermark();
    }, 100);
  };

  // حماية ضد DevTools والتحديد - زكي الخولي
  useEffect(() => {
    if (!isVisible || !user) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    // منع فتح DevTools (محاولة أساسية)
    const detectDevTools = () => {
      const threshold = 160;
      if (window.outerHeight - window.innerHeight > threshold ||
          window.outerWidth - window.innerWidth > threshold) {
        console.clear();
        console.log('%cتحذير: محاولة الوصول لأدوات المطور محظورة - زكي الخولي',
                   'color: red; font-size: 20px; font-weight: bold;');
      }
    };

    // منع التحديد على Canvas - زكي الخولي
    const preventSelect = (e) => {
      e.preventDefault();
      return false;
    };

    // إضافة event listeners
    window.addEventListener('resize', detectDevTools);
    canvas.addEventListener('selectstart', preventSelect);
    canvas.addEventListener('mousedown', preventSelect);

    return () => {
      window.removeEventListener('resize', detectDevTools);
      canvas.removeEventListener('selectstart', preventSelect);
      canvas.removeEventListener('mousedown', preventSelect);
    };
  }, [isVisible, user]);

  useEffect(() => {
    if (!isVisible || !user) return;

    // تحديث حجم Canvas في البداية
    updateCanvasSize();

    // بدء الرسم المستمر - زكي الخولي
    const animate = () => {
      updateCanvasSize(); // تحديث مستمر للحجم
      drawWatermark();
      animationRef.current = requestAnimationFrame(animate);
    };
    animate();

    // تغيير الموضع كل 10 ثواني - زكي الخولي
    intervalRef.current = setInterval(changePosition, 10000);

    // مراقبة مستمرة للفيديو باستخدام Intersection Observer - زكي الخولي
    const videoForObserver = videoElement?.current;
    if (videoForObserver) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            setTimeout(() => {
              updateCanvasSize();
              drawWatermark();
            }, 100);
          }
        });
      }, { threshold: 0.1 });

      observer.observe(videoForObserver);

      // cleanup للـ observer
      return () => {
        observer.disconnect();
      };
    }

    // مراقبة الأحداث - زكي الخولي
    window.addEventListener('resize', handleResize);
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    // مراقبة Picture-in-Picture - زكي الخولي
    const videoForPiP = videoElement?.current;
    if (videoForPiP) {
      videoForPiP.addEventListener('enterpictureinpicture', handleResize);
      videoForPiP.addEventListener('leavepictureinpicture', handleResize);
    }

    // تحديث الحجم عند تغيير حالة الفيديو - زكي الخولي
    const videoForEvents = videoElement?.current;
    if (videoForEvents) {
      const handleVideoResize = () => {
        console.log('Video resized - زكي الخولي');
        setTimeout(() => {
          updateCanvasSize();
          drawWatermark();
        }, 100);
      };

      videoForEvents.addEventListener('loadedmetadata', handleVideoResize);
      videoForEvents.addEventListener('resize', handleVideoResize);
      videoForEvents.addEventListener('play', handleVideoResize);
      videoForEvents.addEventListener('pause', handleVideoResize);
    }

    return () => {
      // تنظيف الموارد - زكي الخولي
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      
      window.removeEventListener('resize', handleResize);
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);

      // إزالة مراقبة Picture-in-Picture - زكي الخولي
      if (videoForPiP) {
        videoForPiP.removeEventListener('enterpictureinpicture', handleResize);
        videoForPiP.removeEventListener('leavepictureinpicture', handleResize);
      }

      if (videoForEvents) {
        videoForEvents.removeEventListener('loadedmetadata', handleVideoResize);
        videoForEvents.removeEventListener('resize', handleVideoResize);
        videoForEvents.removeEventListener('play', handleVideoResize);
        videoForEvents.removeEventListener('pause', handleVideoResize);
      }
    };
  }, [isVisible, user, currentPosition, videoElement]);

  // تحديث Canvas عند تغيير الموضع أو حالة Fullscreen - زكي الخولي
  useEffect(() => {
    if (isVisible && user) {
      console.log('Position or fullscreen changed - زكي الخولي:', currentPosition, isFullscreen);
      setTimeout(() => {
        updateCanvasSize();
        drawWatermark();
      }, 50);
    }
  }, [currentPosition, isFullscreen, isVisible, user]);

  // تشخيص المشكلة - زكي الخولي
  console.log('DynamicWatermark Render - زكي الخولي:', {
    isVisible,
    user: !!user,
    userEmail: user?.email,
    userName: user?.username,
    videoElement: !!videoElement?.current
  });

  if (!isVisible || !user) {
    console.log('DynamicWatermark: Not rendering - زكي الخولي', { isVisible, user: !!user });
    return null;
  }

  return (
    <canvas
      ref={canvasRef}
      data-watermark="true"
      data-user={user?.username || 'protected'}
      className={`absolute top-0 left-0 pointer-events-none z-50 ${className}`}
      style={{
        mixBlendMode: 'normal',
        userSelect: 'none',
        WebkitUserSelect: 'none',
        MozUserSelect: 'none',
        msUserSelect: 'none',
        opacity: 1,
        visibility: 'visible',
        display: 'block',
        position: 'absolute',
        zIndex: 999999,
      }}
      onContextMenu={(e) => e.preventDefault()}
      onDragStart={(e) => e.preventDefault()}
    />
  );
};

export default DynamicWatermark;
