import React, { useState, useEffect } from "react";
import EventAvailableIcon from "@mui/icons-material/EventAvailable";
import Cookies from "js-cookie";
import { fetchSessionsByDate } from "../../../services/newInstructorApis";

export default function NearAppointments() {
  const [activeTab, setActiveTab] = useState("today");
  const [todaySessions, setTodaySessions] = useState([]);
  const [tomorrowSessions, setTomorrowSessions] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchBothDays = async () => {
      const token = Cookies.get("authToken");
      if (!token) return;

      setLoading(true);

      const todayDate = formatDate(new Date());
      const tomorrowDate = formatDate(new Date(Date.now() + 86400000)); // +1 يوم

      try {
        const todayData = await fetchSessionsByDate(todayDate, token);
        const tomorrowData = await fetchSessionsByDate(tomorrowDate, token);
        console.log("Get Today DAta", todayData);

        setTodaySessions(todayData);
        setTomorrowSessions(tomorrowData);
      } catch (e) {
        console.error("Error fetching appointments", e);
      } finally {
        setLoading(false);
      }
    };

    fetchBothDays();
  }, []);

  const formatDate = (date) => {
    return (
      date.getFullYear() +
      "-" +
      String(date.getMonth() + 1).padStart(2, "0") +
      "-" +
      String(date.getDate()).padStart(2, "0")
    );
  };
  const dayTranslations = {
    sunday: "الأحد",
    monday: "الاثنين",
    tuesday: "الثلاثاء",
    wednesday: "الأربعاء",
    thursday: "الخميس",
    friday: "الجمعة",
    saturday: "السبت",
  };
  const displayedSessions =
    activeTab === "today" ? todaySessions : tomorrowSessions;

  return (
    <div className="h-full flex flex-col">
      <header className="flex flex-col sm:flex-row sm:items-center justify-between border-b border-gray-200 dark:border-gray-700 pb-4 mb-6 gap-3 sm:gap-0">
        <div className="flex items-center gap-3 text-gray-700 dark:text-gray-300 font-bold text-lg">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
            <EventAvailableIcon
              fontSize="medium"
              className="text-blue-600 dark:text-blue-400"
            />
          </div>
          <h3>المواعيد القادمة</h3>
        </div>
        <nav className="flex gap-2 bg-gray-100 dark:bg-gray-700 p-1 rounded-xl">
          <button
            onClick={() => setActiveTab("today")}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
              activeTab === "today"
                ? "bg-white dark:bg-gray-800 text-blue-600 dark:text-blue-400 shadow-md"
                : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
            }`}
          >
            اليوم ({todaySessions.length})
          </button>
          <button
            onClick={() => setActiveTab("tomorrow")}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
              activeTab === "tomorrow"
                ? "bg-white dark:bg-gray-800 text-blue-600 dark:text-blue-400 shadow-md"
                : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
            }`}
          >
            غداً ({tomorrowSessions.length})
          </button>
        </nav>
      </header>

      <div className="flex-1 overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="text-gray-600 dark:text-gray-400 ml-3">
              جاري تحميل المواعيد...
            </p>
          </div>
        ) : displayedSessions.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-gray-400 dark:text-gray-500">
            <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
              <EventAvailableIcon
                fontSize="large"
                className="text-gray-400 dark:text-gray-500"
              />
            </div>
            <p className="text-lg font-medium">لا يوجد مواعيد</p>
            <p className="text-sm mt-1">ستظهر المواعيد الجديدة هنا</p>
          </div>
        ) : (
          <div className="space-y-3 max-h-[250px] overflow-y-auto custom-scrollbar pr-2">
            {displayedSessions.map((session, index) => (
              <div
                key={session.id}
                className="group p-4 border border-gray-200 dark:border-gray-700 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 flex flex-col gap-2 transition-all duration-300 hover:shadow-md hover:border-blue-200 dark:hover:border-blue-700 animate-slide-in-right"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="font-bold text-gray-800 dark:text-gray-200 text-sm">
                      {session.from_time} - {session.to_time}
                    </span>
                  </div>
                  <span className="text-xs text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30 px-2 py-1 rounded-full font-medium">
                    {dayTranslations[session.day.toLowerCase()] || session.day}
                  </span>
                </div>
                {session.note && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors duration-300">
                    {session.note}
                  </p>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
