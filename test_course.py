import requests

# تكوين عنوان URL والهيدرز
url = 'http://127.0.0.1:8000/api/courses/'
headers = {
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ3MjUzODE2LCJpYXQiOjE3NDcyNTAyMTYsImp0aSI6ImFhNTlkMmVhODk3NDQyNjM4YzA4ZjgwNDlmMjQwYTZhIiwidXNlcl9pZCI6IjU4ZjMwNzRlLWVmYzEtNDIxOC1hMjA2LWM0YzU0MjhlZWIzZSJ9.w-nnTFjFJcSWicuEYIbapijBoyojp27roDaGYWVgq2I'
}

# إعداد البيانات
data = {
    'title': 'Python للمبتدئين',
    'description': 'تعلم أساسيات البرمجة باستخدام Python من الصفر للاحتراف',
    'short_description': 'دورة شاملة للمبتدئين في Python',
    'price': '99.99',
    'level': 'beginner',
    'language': 'Arabic'
}

# إعداد الملفات
files = {
    'thumbnail': ('thumbnail.jpg', open('test_thumbnail.txt', 'rb'), 'image/jpeg')
}

# إرسال الطلب
response = requests.post(url, headers=headers, data=data, files=files)

# طباعة النتيجة
print(f'Status Code: {response.status_code}')
print('Response:')
print(response.text) 