"use client";

import React, { useState } from "react";

/**
 * ProfilePage Component
 * Arabic RTL profile edit page using TailwindCSS and React.
 * Responsive design and accessible form.
 */

const tabs = [
  { id: 1, label: "بيانات الصفحة" },
  { id: 2, label: "الشكل والمظهر" },
  { id: 3, label: "منصات التواصل" },
];

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState(1);
  const [language, setLanguage] = useState("ar");
  const [receiveMessages, setReceiveMessages] = useState("disabled");
  const [formData, setFormData] = useState({
    pageName: "hossam",
    profession: "لايف كوتش",
    shortBio: "",
    longBio: "",
  });

  // Handle input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  return (
    <div dir="rtl" className="min-h-screen bg-gray-50 font-sans text-gray-900">
      {/* HEADER */}

      {/* MAIN CONTENT CONTAINER */}
      <main className="mx-auto px-6 sm:px-8 py-10">
        {/* Page Title and subtitle */}
        <section className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">صفحتي الشخصية</h1>
          <p className="text-gray-600 mt-1 text-sm">حسابي - صفحتي الشخصية</p>
        </section>

        {/* Form container with white background and rounded corners */}
        <form
          className="bg-white rounded-xl shadow-md p-6 md:p-8 space-y-8"
          onSubmit={(e) => {
            e.preventDefault();
            alert("تم حفظ البيانات!");
          }}
        >
          {/* Header of form section */}
          <div className="flex justify-between items-center border-b border-gray-200 pb-4">
            <h2 className="text-sm font-semibold text-gray-900">
              Edit Your Profile
            </h2>
          </div>

          {/* Tabs */}
          <nav className="flex space-x-8 space-x-reverse border-b border-gray-200 text-xs text-gray-500">
            {tabs.map((tab) => (
              <button
                type="button"
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-3 pb-3 font-semibold ${
                  activeTab === tab.id
                    ? "border-b-2 border-indigo-600 text-indigo-600"
                    : "hover:text-indigo-600"
                } transition`}
              >
                {tab.label}
              </button>
            ))}
          </nav>

          {/* Active Tab Content */}
          {activeTab === 1 && (
            <section className="pt-6 space-y-8 text-gray-700 text-sm">
              {/* Page Name */}
              <div>
                <label
                  htmlFor="pageName"
                  className="block mb-1 font-medium text-gray-900"
                >
                  اسم الصفحة (مطلوب)
                </label>
                <input
                  type="text"
                  id="pageName"
                  name="pageName"
                  placeholder="hossam"
                  value={formData.pageName}
                  onChange={handleChange}
                  required
                  className="w-full rounded-lg border border-gray-300 bg-gray-50 px-3 py-2 placeholder-gray-400 focus:border-indigo-600 focus:ring-1 focus:ring-indigo-600 outline-none transition"
                />
                <p className="mt-1 text-gray-400 text-xs leading-tight">
                  مثال، فادي إبراهيم، د/ طارق سعيد
                </p>
              </div>

              {/* Profession */}
              <div>
                <label
                  htmlFor="profession"
                  className="block mb-1 font-medium text-gray-900"
                >
                  المهنة (مطلوب)
                </label>
                <input
                  type="text"
                  id="profession"
                  name="profession"
                  placeholder="لايف كوتش"
                  value={formData.profession}
                  onChange={handleChange}
                  required
                  className="w-full rounded-lg border border-gray-300 bg-gray-50 px-3 py-2 placeholder-gray-400 focus:border-indigo-600 focus:ring-1 focus:ring-indigo-600 outline-none transition"
                />
                <p className="mt-1 text-gray-400 text-xs leading-tight">
                  مثال، لايف كوتش، طبيب نفسي، مدرس، طبيب تغذية
                </p>
              </div>

              {/* Short Bio */}
              <div>
                <label
                  htmlFor="shortBio"
                  className="block mb-1 font-medium text-gray-900"
                >
                  نبذة مختصرة عنك
                </label>
                <textarea
                  id="shortBio"
                  name="shortBio"
                  rows={3}
                  placeholder=" "
                  value={formData.shortBio}
                  onChange={handleChange}
                  className="w-full rounded-lg border border-gray-300 bg-gray-50 px-3 py-2 placeholder-gray-400 focus:border-indigo-600 focus:ring-1 focus:ring-indigo-600 outline-none resize-y transition"
                />
                <p className="mt-1 text-gray-400 text-xs leading-tight">
                  فيما لا يزيد عن 200 حرف، أضف نبذة مختصرة عنك سوف تظهر تحت اسم
                  صفحتك
                </p>
              </div>

              {/* Long Bio */}
              <div>
                <label
                  htmlFor="longBio"
                  className="block mb-1 font-medium text-gray-900"
                >
                  نبذة مطولة عنك
                </label>
                <textarea
                  id="longBio"
                  name="longBio"
                  rows={6}
                  placeholder=" "
                  value={formData.longBio}
                  onChange={handleChange}
                  className="w-full rounded-lg border border-gray-300 bg-gray-50 px-3 py-2 placeholder-gray-400 focus:border-indigo-600 focus:ring-1 focus:ring-indigo-600 outline-none resize-y transition"
                />
                <p className="mt-1 text-gray-400 text-xs leading-tight">
                  فيما لا يقل عن 50 كلمة، شارك معلومات عنك وعن خبراتك الشخصية
                  والعملية وأي دورات التحقت بها أو شهادات حصلت عليها إن وجد
                </p>
              </div>
            </section>
          )}

          {/* Language and Visitor Messages Section */}
          <section className="space-y-8 text-gray-700 text-sm">
            {/* Language */}
            <div>
              <h3 className="text-base font-semibold mb-3 text-gray-900">
                اللغة
              </h3>
              <fieldset className="flex items-center space-x-8 space-x-reverse">
                <label className="inline-flex items-center cursor-pointer space-x-1 space-x-reverse">
                  <input
                    type="radio"
                    name="language"
                    value="ar"
                    checked={language === "ar"}
                    onChange={() => setLanguage("ar")}
                    className="appearance-none w-5 h-5 rounded-full border border-gray-400 checked:border-indigo-700 checked:bg-indigo-700 focus:outline-none cursor-pointer"
                  />
                  <span className="select-none">عربي</span>
                </label>
                <label className="inline-flex items-center cursor-pointer space-x-1 space-x-reverse">
                  <input
                    type="radio"
                    name="language"
                    value="en"
                    checked={language === "en"}
                    onChange={() => setLanguage("en")}
                    className="appearance-none w-5 h-5 rounded-full border border-gray-400 checked:border-indigo-700 checked:bg-indigo-700 focus:outline-none cursor-pointer"
                  />
                  <span className="select-none">English</span>
                </label>
              </fieldset>
              <p className="mt-2 text-xs text-gray-400">
                اختر اللغة التي ترغب في عرض صفحتك بها
              </p>
            </div>

            {/* Visitor Messages */}
            <div>
              <h3 className="text-base font-semibold mb-3 text-gray-900">
                استقبال رسائل الزوار عن طريق
              </h3>
              <fieldset className="flex flex-col gap-3 max-w-xs">
                <label className="inline-flex items-center cursor-pointer space-x-3 space-x-reverse">
                  <input
                    type="radio"
                    name="visitorMessages"
                    value="disabled"
                    checked={receiveMessages === "disabled"}
                    onChange={() => setReceiveMessages("disabled")}
                    className="appearance-none w-5 h-5 rounded-full border border-gray-400 checked:border-indigo-700 checked:bg-indigo-700 focus:outline-none cursor-pointer"
                  />
                  <span className="select-none">غير مفعل</span>
                </label>
                <label className="inline-flex items-center cursor-pointer space-x-3 space-x-reverse">
                  <input
                    type="radio"
                    name="visitorMessages"
                    value="whatsapp"
                    checked={receiveMessages === "whatsapp"}
                    onChange={() => setReceiveMessages("whatsapp")}
                    className="appearance-none w-5 h-5 rounded-full border border-gray-400 checked:border-indigo-700 checked:bg-indigo-700 focus:outline-none cursor-pointer"
                  />
                  <span className="select-none">رقم واتس أب</span>
                </label>
              </fieldset>
            </div>
          </section>

          {/* Footer actions */}
          <footer className="flex justify-between items-center pt-8 border-t border-gray-200 text-sm text-indigo-600 font-semibold">
            <button
              type="submit"
              className="rounded-lg bg-indigo-600 px-6 py-2 text-white hover:bg-indigo-700 transition focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              حفظ
            </button>
            <a href="#" className="hover:underline" tabIndex={0}>
              معاينة الرابط الشخصي
            </a>
          </footer>
        </form>
      </main>

      {/* FOOTER */}
      <footer className="mt-auto bg-white border-t border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 text-center text-gray-500 text-xs">
          © 2024 جميع الحقوق محفوظة
        </div>
      </footer>
    </div>
  );
}

/**
 * Notes:
 * - Material Icons font should be loaded in the index.html or project root:
 *   <link
 *     href="https://fonts.googleapis.com/icon?family=Material+Icons"
 *     rel="stylesheet"
 *   />
 *
 * - Tailwind CSS configured with RTL support (e.g., via "tailwindcss-rtl" plugin) or direction set on container works for layout.
 * - The UI is fully responsive and respects spacing and visual hierarchy.
 */
