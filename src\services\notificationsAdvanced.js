// خدمات الإشعارات المتقدمة: جميع استدعاءات API الخاصة بالإشعارات - zaki alkholy
import axios from "axios";
import { API_BASE_URL } from '../config/api';

// ===============================
// خدمات الإشعارات العامة - zaki alkholy
// ===============================

/**
 * الحصول على إشعارات المستخدم - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {Object} filters - فلاتر البحث
 * @returns {Promise} قائمة الإشعارات
 */
export async function fetchUserNotifications(token, filters = {}) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/notifications/`, {
    headers,
    withCredentials: true,
    params: filters
  });
  
  return response.data;
}

/**
 * تحديد إشعار كمقروء - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} notificationId - معرف الإشعار
 * @returns {Promise} الإشعار المحدث
 */
export async function markNotificationAsRead(token, notificationId) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.patch(`${API_BASE_URL}/api/notifications/${notificationId}/`, {
    is_read: true
  }, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * تحديد جميع الإشعارات كمقروءة - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @returns {Promise} نتيجة التحديث
 */
export async function markAllNotificationsAsRead(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.post(`${API_BASE_URL}/api/notifications/mark-all-read/`, {}, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * حذف إشعار - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} notificationId - معرف الإشعار
 * @returns {Promise} نتيجة الحذف
 */
export async function deleteNotification(token, notificationId) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.delete(`${API_BASE_URL}/api/notifications/${notificationId}/`, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * الحصول على عدد الإشعارات غير المقروءة - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @returns {Promise} عدد الإشعارات غير المقروءة
 */
export async function fetchUnreadNotificationsCount(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/notifications/unread-count/`, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

// ===============================
// خدمات إعدادات الإشعارات - zaki alkholy
// ===============================

/**
 * الحصول على إعدادات الإشعارات - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @returns {Promise} إعدادات الإشعارات
 */
export async function fetchNotificationSettings(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/notifications/settings/`, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * تحديث إعدادات الإشعارات - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {Object} settings - الإعدادات الجديدة
 * @returns {Promise} الإعدادات المحدثة
 */
export async function updateNotificationSettings(token, settings) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.put(`${API_BASE_URL}/api/notifications/settings/`, settings, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

// ===============================
// خدمات الإشعارات الفورية - zaki alkholy
// ===============================

/**
 * الاشتراك في الإشعارات الفورية - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {Object} subscription - بيانات الاشتراك
 * @returns {Promise} نتيجة الاشتراك
 */
export async function subscribeToPushNotifications(token, subscription) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.post(`${API_BASE_URL}/api/notifications/push-subscribe/`, subscription, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * إلغاء الاشتراك في الإشعارات الفورية - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} endpoint - نقطة النهاية للاشتراك
 * @returns {Promise} نتيجة إلغاء الاشتراك
 */
export async function unsubscribeFromPushNotifications(token, endpoint) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.post(`${API_BASE_URL}/api/notifications/push-unsubscribe/`, {
    endpoint
  }, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

// ===============================
// خدمات إشعارات المعلمين - zaki alkholy
// ===============================

/**
 * إرسال إشعار للطلاب - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {Object} notificationData - بيانات الإشعار
 * @returns {Promise} نتيجة الإرسال
 */
export async function sendNotificationToStudents(token, notificationData) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.post(`${API_BASE_URL}/api/notifications/send-to-students/`, notificationData, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * إرسال إشعار لطلاب دورة محددة - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} courseId - معرف الدورة
 * @param {Object} notificationData - بيانات الإشعار
 * @returns {Promise} نتيجة الإرسال
 */
export async function sendNotificationToCourseStudents(token, courseId, notificationData) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.post(`${API_BASE_URL}/api/notifications/send-to-course/${courseId}/`, notificationData, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

// ===============================
// خدمات الإشعارات التلقائية - zaki alkholy
// ===============================

/**
 * الحصول على قوالب الإشعارات - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @returns {Promise} قائمة القوالب
 */
export async function fetchNotificationTemplates(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/notifications/templates/`, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * إنشاء قالب إشعار جديد - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {Object} templateData - بيانات القالب
 * @returns {Promise} القالب المُنشأ
 */
export async function createNotificationTemplate(token, templateData) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.post(`${API_BASE_URL}/api/notifications/templates/`, templateData, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}
