import React, { useState, useEffect } from "react";
import { fetchCategories } from "../../services/courses";

const CourseInfo = ({ courseData }) => {
  const theUrl = "https://res.cloudinary.com/djwhgnwp5/";
  const [categories, setCategories] = useState([]);
  const [categoryName, setCategoryName] = useState("غير محدد");
  const [categorydes, setCategorydes] = useState("غير محدد");

  // جلب قائمة الفئات لمطابقة ID مع الاسم
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const categoriesData = await fetchCategories();
        setCategories(categoriesData);
      } catch (error) {
        console.error("خطأ في جلب الفئات:", error);
      }
    };
    loadCategories();
  }, []);

  // تحديد اسم الفئة بناءً على البيانات المتاحة
  useEffect(() => {
    console.log("🔍 CourseInfo: تحديد اسم الفئة...");
    console.log("📂 courseData.category:", courseData?.category);
    console.log("📚 categories:", categories);

    if (!courseData?.category) {
      console.log("⚠️ لا توجد فئة للكورس");
      setCategoryName("غير محدد");
      return;
    }

    // إذا كانت الفئة object تحتوي على name
    if (typeof courseData.category === "object" && courseData.category.name) {
      console.log("✅ الفئة object مع name:", courseData.category.name);
      setCategoryName(courseData.category.name);
      setCategorydes(courseData.category.description);
      return;
    }

    // إذا كانت الفئة مجرد ID، ابحث عن الاسم في قائمة الفئات
    if (typeof courseData.category === "string" && categories.length > 0) {
      console.log("🔍 البحث عن الفئة بـ ID:", courseData.category);
      const foundCategory = categories.find(
        (cat) => cat.id === courseData.category
      );
      console.log("🎯 الفئة الموجودة:", foundCategory);
      setCategoryName(foundCategory ? foundCategory.name : "غير محدد");
      setCategorydes(foundCategory ? foundCategory.description : "غير محدد");
      return;
    }

    // إذا كانت الفئة مجرد ID ولكن لم يتم تحميل الفئات بعد، انتظر
    if (typeof courseData.category === "string" && categories.length === 0) {
      console.log("⏳ انتظار تحميل الفئات...");
      setCategoryName("جاري التحميل...");
      setCategorydes("جاري التحميل...");
      return;
    }

    console.log("❌ لم يتم العثور على الفئة");
    setCategoryName("غير محدد");
  }, [courseData?.category, categories]);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      {/* Course Media Section */}
      <div className="lg:col-span-1">
        <div className="space-y-8">
          {/* Course Thumbnail */}
          <div className="relative group">
            <div className="aspect-video rounded-2xl overflow-hidden shadow-lg mt-5">
              <img
                src={`${theUrl}${courseData.thumbnail}`}
                alt="Course thumbnail"
                className="w-full h-full object-cover transition-transform duration-300  group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-gradient-to-t rounded-2xl from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          </div>

          {/* Promo Video */}
          {courseData.promo_video && (
            <div className="relative group">
              <div className="rounded-2xl overflow-hidden shadow-lg bg-gray-100 dark:bg-gray-800">
                {/* <div className="flex items-center gap-3 p-4 bg-gradient-to-r from-purple-500 to-pink-500">
                  <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-4 h-4 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M8 5v10l8-5-8-5z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold text-white">
                    فيديو العرض التوضيحي
                  </h3>
                </div> */}
                <div className="">
                  <video
                    controls
                    className="w-full rounded-xl shadow-md"
                    poster={`${theUrl}${courseData.thumbnail}`}
                  >
                    <source
                      src={`${theUrl}${courseData.promo_video}`}
                      type="video/mp4"
                    />
                    متصفحك لا يدعم تشغيل الفيديو.
                  </video>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Course Details Section */}
      <div className="lg:col-span-2">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="space-y-6">
            <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 p-6 rounded-2xl border border-gray-200 dark:border-gray-600">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-indigo-500 rounded-lg flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-800 dark:text-white">
                  معلومات أساسية
                </h3>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-600 last:border-b-0">
                  <span className="text-gray-600 dark:text-gray-400 font-medium">
                    الفئة
                  </span>
                  <span className="text-gray-800 dark:text-white font-semibold">
                    {categoryName} - {categorydes}
                  </span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-600 last:border-b-0">
                  <span className="text-gray-600 dark:text-gray-400 font-medium">
                    اللغة
                  </span>
                  <span className="text-gray-800 dark:text-white font-semibold">
                    {courseData.language || "العربية"}
                  </span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="text-gray-600 dark:text-gray-400 font-medium">
                    تاريخ الإنشاء
                  </span>
                  <span className="text-gray-800 dark:text-white font-semibold">
                    {courseData.created_at
                      ? new Date(courseData.created_at).toLocaleDateString(
                          "ar-EG"
                        )
                      : "غير محدد"}
                  </span>
                </div>
              </div>
            </div>

            {/* Pricing Information */}
            <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-6 rounded-2xl border border-purple-200 dark:border-purple-800">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-800 dark:text-white">
                  تفاصيل السعر
                </h3>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 dark:text-gray-400">
                    السعر الأساسي
                  </span>
                  <span
                    className={`font-bold ${
                      courseData.discount_price
                        ? "line-through text-gray-500"
                        : "text-purple-600 dark:text-purple-400"
                    }`}
                  >
                    {courseData.price} {courseData.currency}
                  </span>
                </div>
                {courseData.discount_price && (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:text-gray-400">
                      سعر الخصم
                    </span>
                    <span className="font-bold text-purple-600 dark:text-purple-400 text-lg">
                      {courseData.discount_price} {courseData.currency}
                    </span>
                  </div>
                )}
                {courseData.discount_price && (
                  <div className="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 px-3 py-2 rounded-lg text-center font-medium">
                    وفر{" "}
                    {(
                      ((courseData.price - courseData.discount_price) /
                        courseData.price) *
                      100
                    ).toFixed(0)}
                    %
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Description and Stats */}
          <div className="space-y-4">
            {/* Course Description */}
            <div className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 p-6 rounded-2xl border border-blue-200 dark:border-blue-800">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-800 dark:text-white">
                  وصف الكورس
                </h3>
              </div>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                {courseData.description || "لا يوجد وصف متاح للكورس"}
              </p>
              {courseData.short_description && (
                <div className="mt-4 p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                  <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                    الوصف المختصر:
                  </p>
                  <p className="text-gray-700 dark:text-gray-300">
                    {courseData.short_description}
                  </p>
                </div>
              )}
            </div>

            {/* Performance Stats */}
            <div className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 p-6 rounded-2xl border border-orange-200 dark:border-orange-800">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-800 dark:text-white">
                  إحصائيات الأداء
                </h3>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                    {courseData.students_count || 0}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    طالب مسجل
                  </div>
                </div>
                <div className="text-center p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                  <div className="flex items-center justify-center gap-1">
                    <span className="text-2xl font-bold text-yellow-500">
                      {courseData.rating || "0"}
                    </span>
                    <svg
                      className="w-5 h-5 text-yellow-500"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    تقييم الكورس
                  </div>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 rounded-xl border border-blue-100 dark:border-blue-800">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-5 h-5 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                      />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      السعر
                    </p>
                    <p className="font-bold text-gray-800 dark:text-white">
                      {courseData.discount_price || courseData.price}{" "}
                      {courseData.currency}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-4 rounded-xl border border-green-100 dark:border-green-800">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-5 h-5 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      المستوى
                    </p>
                    <p className="font-bold text-gray-800 dark:text-white capitalize">
                      {courseData.level === "beginner"
                        ? "مبتدئ"
                        : courseData.level === "intermediate"
                        ? "متوسط"
                        : courseData.level === "advanced"
                        ? "متقدم"
                        : courseData.level}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseInfo;
