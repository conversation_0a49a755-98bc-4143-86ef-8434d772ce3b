"use client";
import { useState } from "react";
import axios from "axios";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { API_BASE_URL } from "../../config/api";

const PaymentForm = ({ course, coursePrice }) => {
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState("vodafone_cash");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [paymentClosed, setPaymentClosed] = useState(false);
  const [paymentCompleted, setPaymentCompleted] = useState(false); // جديد: تتبع حالة اكتمال الدفع
  const router = useRouter();

  const handlePayment = async () => {
    setProcessing(true);
    setError(null);
    setPaymentClosed(false);
    setPaymentCompleted(false); // إعادة تعيين حالة اكتمال الدفع

    try {
      const token = Cookies.get("authToken");
      if (!token) {
        setError("يرجى تسجيل الدخول لإتمام عملية الدفع");
        setProcessing(false);
        return;
      }

      // استخدام course.id (UUID) بدلاً من courseId (slug) - zaki alkholy
      if (!course || !course.id) {
        setError("معرّف الكورس غير متوفر");
        setProcessing(false);
        return;
      }

      if (!phoneNumber) {
        setError("يرجى إدخال رقم الهاتف للدفع عبر المحفظة");
        setProcessing(false);
        return;
      }

      const headers = {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        Accept: "application/json",
      };

      // Create payment intent
      const createPaymentUrl = `${API_BASE_URL}/api/create-payment-intent/`;
      let response;
      try {
        response = await axios.post(
          createPaymentUrl,
          {
            course_id: course.id, // استخدام UUID الحقيقي للكورس - zaki alkholy
            payment_method: paymentMethod,
            phone_number: phoneNumber,
          },
          { headers }
        );
      } catch (err) {
        console.error("Payment API error:", err);
        if (err.response) {
          console.error("Payment API response data:", err.response.data);
          setError("خطأ من السيرفر: " + JSON.stringify(err.response.data));
        } else {
          setError("خطأ غير متوقع: " + err.message);
        }
        setProcessing(false);
        return;
      }

      const { payment_key, iframe_id } = response.data;

      if (!payment_key || !iframe_id) {
        throw new Error("لم يتم استلام بيانات الدفع");
      }

      // Open payment page
      const paymentUrl = `https://accept.paymob.com/api/acceptance/iframes/${iframe_id}?payment_token=${payment_key}`;
      const paymentWindow = window.open(
        paymentUrl,
        "_blank",
        "width=800,height=600"
      );

      if (!paymentWindow) {
        throw new Error(
          "لم يتمكن المتصفح من فتح نافذة الدفع. يرجى السماح بالنوافذ المنبثقة"
        );
      }

      // مراقبة إغلاق نافذة الدفع
      const paymentCloseInterval = setInterval(() => {
        if (paymentWindow.closed && !paymentCompleted) {
          clearInterval(paymentCloseInterval);
          setProcessing(false);
          setPaymentClosed(true);
          toast.error("لم يتم الدفع. تم إغلاق نافذة الدفع قبل الإتمام.");
        }
      }, 1000);

      // Check payment status
      const checkPaymentStatus = setInterval(async () => {
        try {
          const statusUrl = `${API_BASE_URL}/api/payment-status/${course.id}/`; // استخدام UUID - zaki alkholy
          const statusResponse = await axios.get(statusUrl, { headers });

          if (statusResponse.data.status === "completed") {
            clearInterval(checkPaymentStatus);
            clearInterval(paymentCloseInterval);
            paymentWindow.close();
            setPaymentCompleted(true); // تعيين حالة اكتمال الدفع
            toast.success("تم الدفع بنجاح!");
            router.push(`/courses/${course.slug || course.id}/success`); // استخدام slug للتنقل - zaki alkholy
          } else if (statusResponse.data.status === "failed") {
            clearInterval(checkPaymentStatus);
            clearInterval(paymentCloseInterval);
            paymentWindow.close();
            setProcessing(false);
            setPaymentCompleted(false);
            toast.error("فشلت عملية الدفع");
            router.push(`/courses/${course.slug || course.id}/failed`); // استخدام slug للتنقل - zaki alkholy
          }
        } catch (error) {
          console.error("Error checking payment status:", error);
        }
      }, 5000); // Check every 5 seconds

      // Timeout after 5 minutes
      setTimeout(() => {
        clearInterval(checkPaymentStatus);
        clearInterval(paymentCloseInterval);
        if (paymentWindow && !paymentWindow.closed) {
          paymentWindow.close();
        }
        if (!paymentCompleted) {
          setProcessing(false);
          setPaymentClosed(true);
          toast.error("انتهت مهلة الدفع");
        }
      }, 5 * 60 * 1000);
    } catch (err) {
      console.error("Payment error:", err);
      let errorMessage = "حدث خطأ أثناء معالجة الدفع";
      if (err.response?.status === 400) {
        errorMessage = err.response.data.error || "بيانات الدفع غير صحيحة";
      } else if (err.response?.status === 401) {
        errorMessage = "انتهت جلسة تسجيل الدخول، يرجى تسجيل الدخول مرة أخرى";
        Cookies.remove("authToken");
        router.push("/login");
      } else {
        errorMessage = err.response?.data?.message || err.message;
      }
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      if (!paymentCompleted) {
        setProcessing(false);
      }
    }
  };

  return (
    <div className="mt-4">
      {processing && (
        <div className="flex justify-center items-center mb-4">
          <svg
            className="animate-spin h-6 w-6 text-primary mr-2"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8v8z"
            ></path>
          </svg>
          <span>جاري التحقق من الدفع...</span>
        </div>
      )}
      {paymentClosed && !paymentCompleted && (
        <div className="text-red-500 mt-2 text-center">
          لم يتم الدفع. تم إغلاق نافذة الدفع قبل الإتمام.
        </div>
      )}
      <div className="mb-4">
        <label className="block mb-1 font-medium text-black dark:text-white">
          اختر طريقة الدفع
        </label>
        <select
          value={paymentMethod}
          onChange={(e) => setPaymentMethod(e.target.value)}
          className="w-full border rounded px-3 py-2 text-black"
        >
          <option value="vodafone_cash">فودافون كاش</option>
          <option value="orange_money">أورانج موني</option>
          <option value="we_cash">وي كاش</option>
          <option value="etisalat_cash">اتصالات كاش</option>
        </select>
      </div>
      <div className="mb-4">
        <label className="block mb-1 font-medium text-black dark:text-white">
          رقم الهاتف
        </label>
        <input
          type="text"
          value={phoneNumber}
          onChange={(e) => setPhoneNumber(e.target.value)}
          className="w-full border rounded px-3 py-2 text-black"
          placeholder="أدخل رقم الهاتف (مثال: 01012345678)"
        />
      </div>
      <button
        onClick={handlePayment}
        disabled={processing}
        className={`w-full bg-primary text-white py-4 px-6 rounded-lg font-medium text-lg transition-colors ${
          processing ? "opacity-50 cursor-not-allowed" : "hover:bg-primary/90"
        }`}
      >
        {processing ? "جاري المعالجة..." : `ادفع ${coursePrice} جنيه`}
      </button>
      {error && <div className="text-red-500 mt-2 text-center">{error}</div>}
    </div>
  );
};

export default PaymentForm;
