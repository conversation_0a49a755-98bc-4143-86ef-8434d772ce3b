"use client";
import { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useSelector } from "react-redux";
import {
  selectIsAuthenticated,
  selectCurrentUser,
} from "../../store/authSlice";

export default function AuthProvider({ children }) {
  const router = useRouter();
  const pathname = usePathname();
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const user = useSelector(selectCurrentUser);
  useEffect(() => {
    const isPublicPath = [
      "/",
      "/login",
      "/signup",
      "/forgot-password",
      "/reset-password",
      "/about",
      "/Services",
      "/PrivacyPolicy",
      "/TermsOfService",
      "/RefundPolicy",
      "/ShippingPolicy",
      "/ContactUs",
      "/courses",
      "/howItWorks",
      "/features-guide",
    ].includes(pathname);

    // Admin Dashboard paths - تجاهلها تماماً
    const isAdminPath = pathname.startsWith("/admin-secure-dashboard-2024");

    // تجاهل Admin Dashboard paths
    if (isAdminPath) {
      return;
    }

    // المستخدم داخل وبيحاول يدخل login/signup → رجّعه للرئيسية
    if (isAuthenticated && ["/login", "/signup"].includes(pathname)) {
      router.push("/");
    }

    // مش داخل وبيحاول يدخل صفحة خاصة → رجّعه للّوجين
    else if (!isAuthenticated && !isPublicPath) {
      router.push("/login");
    }

    // داخل، بس مش instructor وبيحاول يدخل صفحة instructor → رجّعه للرئيسية
    else if (
      isAuthenticated &&
      !user?.is_instructor &&
      pathname.includes("/instructor") &&
      !/^\/instructor\/[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(
        pathname
      )
    ) {
      router.push("/");
    }
  }, [isAuthenticated, pathname, router, user]);

  return <>{children}</>;
}
