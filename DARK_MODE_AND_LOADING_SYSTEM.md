# نظام الدارك مود والتحميل الموحد

## نظام الدارك مود 🌙

### المكونات الأساسية:

#### 1. ThemeContext (`src/contexts/ThemeContext.jsx`)
- **الوظيفة:** إدارة حالة الثيم في التطبيق
- **الميزات:**
  - حفظ الثيم في localStorage
  - مراقبة إعدادات النظام
  - تطبيق CSS variables تلقائياً
  - دعم الانتقال السلس بين الأوضاع

#### 2. ThemeToggle (`src/components/common/ThemeToggle.jsx`)
- **الوظيفة:** زر تبديل الثيم
- **الأحجام المتاحة:** small, medium, large
- **الأنواع:**
  - `ThemeToggle` - زر دائري مع أيقونات
  - `ThemeToggleSwitch` - مفتاح على شكل switch

### كيفية الاستخدام:

```jsx
import { useTheme } from '@/contexts/ThemeContext';
import ThemeToggle from '@/components/common/ThemeToggle';

// في أي مكون
const { isDarkMode, toggleTheme } = useTheme();

// استخدام الزر
<ThemeToggle size="medium" showLabel={true} />
```

### CSS Variables:
```css
:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #2563eb;
  --secondary: #64748b;
}

.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
  --primary: #3b82f6;
  --secondary: #94a3b8;
}
```

---

## نظام التحميل الموحد ⏳

### المكون الأساسي: UniversalLoader

#### الموقع: `src/components/common/UniversalLoader.jsx`

### الأحجام المتاحة:
- `small` - 16x16px
- `medium` - 32x32px (افتراضي)
- `large` - 48x48px
- `xlarge` - 64x64px
- `xxlarge` - 128x128px

### الأنواع المتاحة:
- `spinner` - دائرة دوارة (افتراضي)
- `dots` - ثلاث نقاط متحركة
- `pulse` - نبضة متحركة

### الألوان المتاحة:
- `primary` - اللون الأساسي (افتراضي)
- `blue` - أزرق
- `white` - أبيض
- `gray` - رمادي

### المكونات المخصصة:

#### 1. PageLoader
```jsx
import { PageLoader } from '@/components/common/UniversalLoader';
<PageLoader text="جاري تحميل الصفحة..." />
```

#### 2. SectionLoader
```jsx
import { SectionLoader } from '@/components/common/UniversalLoader';
<SectionLoader text="جاري التحميل..." />
```

#### 3. ButtonLoader
```jsx
import { ButtonLoader } from '@/components/common/UniversalLoader';
<ButtonLoader size="small" />
```

#### 4. InlineLoader
```jsx
import { InlineLoader } from '@/components/common/UniversalLoader';
<InlineLoader />
```

### الاستخدام الأساسي:

```jsx
import UniversalLoader from '@/components/common/UniversalLoader';

// استخدام بسيط
<UniversalLoader />

// مع خيارات مخصصة
<UniversalLoader 
  size="large"
  variant="dots"
  text="جاري المعالجة..."
  showText={true}
  color="primary"
/>

// شاشة كاملة
<UniversalLoader 
  fullscreen={true}
  text="جاري تحميل التطبيق..."
/>
```

---

## المكونات المحدثة للتوافق:

### 1. LoadingSpinner (`src/app/_Components/LoadingSpinner/LoadingSpinner.jsx`)
- محدث ليستخدم UniversalLoader
- محفوظ للتوافق مع الكود القديم

### 2. Loader (`src/components/common/Loader.jsx`)
- محدث ليستخدم UniversalLoader
- محفوظ للتوافق مع الكود القديم

---

## الصفحات المحدثة:

### تم تحديث جميع صفحات التحميل لتستخدم النظام الجديد:
- ✅ Student Progress Page
- ✅ Student Course Page
- ✅ Instructor Students Page
- ✅ Instructor Analytics Page
- ✅ Advanced Analytics Page
- ✅ Home Page Search
- ✅ Instructor Profile Pages
- ✅ Student Profile Pages

---

## مكان الملفات الرئيسية:

### نظام الدارك مود:
- **ThemeContext:** `manasa/src/contexts/ThemeContext.jsx`
- **ThemeToggle:** `manasa/src/components/common/ThemeToggle.jsx`
- **CSS Styles:** `manasa/src/app/globals.css`

### نظام التحميل:
- **UniversalLoader:** `manasa/src/components/common/UniversalLoader.jsx`
- **LoadingSpinner:** `manasa/src/app/_Components/LoadingSpinner/LoadingSpinner.jsx`
- **Loader:** `manasa/src/components/common/Loader.jsx`

### التطبيق:
- **Layout:** `manasa/src/app/layout.jsx` (يحتوي على ThemeProvider)
- **Navbar:** `manasa/src/app/_Components/Navbar/Navbar.jsx` (يحتوي على ThemeToggle)

---

## ملاحظات مهمة:

1. **الدارك مود يحفظ تلقائياً** في localStorage
2. **يتبع إعدادات النظام** إذا لم يختر المستخدم ثيم محدد
3. **جميع الـ loaders موحدة** وتدعم الدارك مود
4. **التوافق محفوظ** مع الكود القديم
5. **الأداء محسن** مع lazy loading للثيم

---

## للتطوير المستقبلي:

### إضافة loader جديد:
```jsx
// في أي صفحة
if (loading) {
  return <UniversalLoader size="large" text="رسالة مخصصة..." />;
}
```

### تخصيص الثيم:
```jsx
// إضافة ألوان جديدة في globals.css
.dark {
  --custom-color: #your-color;
}
```

---

**تم إنشاؤه بواسطة:** zaki alkholy  
**التاريخ:** يوليو 2025  
**الإصدار:** 1.0
