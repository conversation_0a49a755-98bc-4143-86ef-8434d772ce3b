# منصة منَصة (Manasa)

منصة تعليمية متكاملة لإدارة الدورات، الطلاب، المعلمين، والمدفوعات.

---

## المتطلبات الأساسية
- Node.js (يفضل 18 أو أحدث)
- npm أو yarn
- Python 3.8 أو أحدث
- Django + مكتبات backend (موجودة في requirements.txt)

---

## خطوات التشغيل

### 1. تثبيت التبعيات
```bash
# واجهة المستخدم (Frontend)
npm install

# الواجهة الخلفية (Backend)
cd backend/9/Mnasa/Newmnasa
pip install -r requirements.txt
```

### 2. إعداد البيئة
- أنشئ ملف `.env.local` في جذر المشروع:
```env
NEXT_PUBLIC_API_URL=http://localhost:8000/api
NEXT_PUBLIC_ENCRYPTION_KEY=your-secure-key-here
NEXT_PUBLIC_COOKIE_DOMAIN=localhost
```
- تأكد من إعداد قاعدة البيانات في `backend/9/Mnasa/Newmnasa/Newmnasa/settings.py`

### 3. تشغيل المشروع
```bash
# تشغيل الواجهة الأمامية
npm run dev

# تشغيل الواجهة الخلفية
cd backend/9/Mnasa/Newmnasa
python manage.py runserver
```

---

## هيكلة المشروع

```
manasa/
├── backend/
│   └── 9/
│       └── Mnasa/
│           └── Newmnasa/
│               ├── main/ (models, views, serializers, ...)
│               ├── media/
│               ├── static/
│               ├── templates/
│               ├── manage.py
│               └── ...
├── src/
│   ├── app/
│   │   ├── (pages)/
│   │   ├── _Components/
│   │   ├── context/
│   │   ├── layout.jsx
│   │   └── ...
│   ├── config/
│   ├── hooks/
│   ├── store/
│   └── utils/
├── public/
│   └── images/
├── package.json
├── requirements.txt
└── ...
```

---

## ملاحظات
- جميع الأكواد منظمة بين backend (Django) و frontend (Next.js/React).
- كل قسم له مجلداته الخاصة (صفحات، مكونات، دوال مساعدة).
- يمكنك تعديل أي جزء بسهولة حسب الحاجة.

---

لأي استفسار أو شرح إضافي لأي جزء من المشروع، تواصل مع المطور الرئيسي.
