// دليل الميزات الجديدة للمنصة - zaki alkholy
'use client';

import React from 'react';
import Link from 'next/link';
import { 
  BarChart3, 
  Trophy, 
  FileText, 
  Brain, 
  Star,
  Target,
  BookOpen,
  Award,
  Users,
  TrendingUp,
  Gift,
  Calendar,
  CheckCircle
} from 'lucide-react';

// مكون بطاقة الميزة - zaki alkholy
const FeatureCard = ({ icon: Icon, title, description, link, color = "blue", isNew = true }) => {
  const colorClasses = {
    blue: "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700 text-blue-600 dark:text-blue-400",
    green: "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700 text-green-600 dark:text-green-400",
    purple: "bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-700 text-purple-600 dark:text-purple-400",
    orange: "bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-700 text-orange-600 dark:text-orange-400",
    red: "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-700 text-red-600 dark:text-red-400",
  };

  return (
    <div className={`relative p-6 rounded-xl border-2 ${colorClasses[color]} hover:shadow-lg transition-all duration-300`}>
      {isNew && (
        <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">
          جديد!
        </span>
      )}

      <div className="flex items-center mb-4">
        <Icon className="w-8 h-8 mr-3" />
        <h3 className="text-xl font-bold text-foreground">{title}</h3>
      </div>

      <p className="text-gray-700 dark:text-gray-300 mb-4 leading-relaxed">{description}</p>

      <Link
        href={link}
        className="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 border border-current rounded-lg hover:bg-current hover:text-white transition-all duration-300"
      >
        جرب الآن
        <Target className="w-4 h-4 mr-2" />
      </Link>
    </div>
  );
};

// الصفحة الرئيسية لدليل الميزات - zaki alkholy
export default function FeaturesGuidePage() {
  // ميزات الطلاب - zaki alkholy
  const studentFeatures = [
    {
      icon: BarChart3,
      title: "تتبع التقدم المتقدم",
      description: "تابع تقدمك في كل درس ودورة مع إحصائيات مفصلة عن وقت المشاهدة ونسبة الإكمال والنقاط المكتسبة.",
      link: "/student/progress",
      color: "blue"
    },
    {
      icon: Star,
      title: "متجر النقاط والمكافآت",
      description: "اكسب نقاط من خلال التعلم واستبدلها بمكافآت حقيقية مثل خصومات على الدورات وشهادات تقدير.",
      link: "/student/points-store",
      color: "purple"
    },
    {
      icon: Brain,
      title: "المراجعة الذكية المتباعدة",
      description: "نظام علمي متقدم يذكرك بمراجعة ما تعلمته في الأوقات المثلى لضمان عدم النسيان.",
      link: "/student/review",
      color: "green"
    },
    {
      icon: Trophy,
      title: "نظام الإنجازات والميداليات",
      description: "احصل على إنجازات وميداليات عند إكمال المهام والوصول لأهداف معينة في رحلة التعلم.",
      link: "/student/progress",
      color: "orange"
    }
  ];

  // ميزات المعلمين - zaki alkholy
  const instructorFeatures = [
    {
      icon: BarChart3,
      title: "تحليلات الأداء الشاملة",
      description: "احصل على رؤى عميقة عن أداء طلابك مع إحصائيات مفصلة عن التقدم والمشاركة والنجاح.",
      link: "/instructor/analytics",
      color: "blue"
    },
    {
      icon: FileText,
      title: "الواجبات التفاعلية",
      description: "أنشئ واجبات متنوعة (مشاريع، تصاميم، أكواد) مع نظام تقييم متطور وتعليقات تفصيلية.",
      link: "/instructor/assignments",
      color: "green"
    },
    {
      icon: Users,
      title: "إدارة الطلاب المتقدمة",
      description: "تتبع تقدم كل طالب بالتفصيل مع إمكانية إرسال تعليقات وتوصيات شخصية.",
      link: "/instructor/analytics",
      color: "purple"
    },
    {
      icon: TrendingUp,
      title: "تقارير الأداء والمبيعات",
      description: "تقارير مفصلة عن أداء دوراتك ومبيعاتك مع اقتراحات لتحسين المحتوى.",
      link: "/instructor/analytics",
      color: "orange"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
      <div className="max-w-7xl mx-auto px-6">
        {/* العنوان الرئيسي - zaki alkholy */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            🚀 الميزات الجديدة في منصة "مُعَلِّمِيّ"
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            اكتشف الميزات المتقدمة الجديدة التي تم إضافتها لتحسين تجربة التعلم والتعليم
          </p>
          <div className="mt-6 inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400 rounded-full text-sm font-medium">
            <Award className="w-4 h-4 mr-2" />
            تطوير: zaki alkholy
          </div>
        </div>

        {/* ميزات الطلاب - zaki alkholy */}
        <section className="mb-16">
          <div className="flex items-center mb-8">
            <BookOpen className="w-8 h-8 text-blue-600 dark:text-blue-400 mr-3" />
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white">ميزات الطلاب الجديدة</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {studentFeatures.map((feature, index) => (
              <FeatureCard key={index} {...feature} />
            ))}
          </div>
        </section>

        {/* ميزات المعلمين - zaki alkholy */}
        <section className="mb-16">
          <div className="flex items-center mb-8">
            <Users className="w-8 h-8 text-green-600 dark:text-green-400 mr-3" />
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white">ميزات المعلمين الجديدة</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {instructorFeatures.map((feature, index) => (
              <FeatureCard key={index} {...feature} />
            ))}
          </div>
        </section>

        {/* كيفية الوصول للميزات - zaki alkholy */}
        <section className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-sm border border-gray-200 dark:border-gray-700">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
            <Target className="w-6 h-6 mr-3 text-purple-600 dark:text-purple-400" />
            كيفية الوصول للميزات الجديدة
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">للطلاب:</h3>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-500 dark:text-green-400 mr-3" />
                  <span className="text-foreground">ابحث عن قائمة "أدوات الطالب" في النافيجيشن العلوي</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-500 dark:text-green-400 mr-3" />
                  <span className="text-foreground">أو زر الصفحات مباشرة من الروابط أعلاه</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-500 dark:text-green-400 mr-3" />
                  <span className="text-foreground">ابدأ بصفحة "تتبع التقدم" لرؤية نقاطك</span>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">للمعلمين:</h3>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-500 dark:text-green-400 mr-3" />
                  <span className="text-foreground">ابحث عن قائمة "أدوات المعلم" في النافيجيشن العلوي</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-500 dark:text-green-400 mr-3" />
                  <span className="text-foreground">ابدأ بصفحة "تحليلات الأداء" لرؤية إحصائيات طلابك</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-500 dark:text-green-400 mr-3" />
                  <span className="text-foreground">جرب إنشاء واجب تفاعلي جديد</span>
                </li>
              </ul>
            </div>
          </div>
        </section>

        {/* رسالة ختامية - zaki alkholy */}
        <div className="text-center mt-12 p-8 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl">
          <h3 className="text-2xl font-bold mb-4">🎉 استمتع بالميزات الجديدة!</h3>
          <p className="text-lg opacity-90">
            جميع هذه الميزات تم تطويرها خصيصاً لتحسين تجربة التعلم والتعليم في منصة "مُعَلِّمِيّ"
          </p>
          <div className="mt-6">
            <Link
              href="/"
              className="inline-flex items-center px-6 py-3 bg-white text-blue-600 rounded-lg font-medium hover:bg-gray-100 transition-colors"
            >
              العودة للصفحة الرئيسية
              <Target className="w-4 h-4 mr-2" />
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
