"use client";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, Suspense } from "react";
import toast from "react-hot-toast";

function PaymentFailedContent() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const orderId = searchParams.get('order');

    useEffect(() => {
        if (window.opener) {
            window.opener.postMessage({ type: 'PAYMENT_FAILED', orderId }, '*');
            setTimeout(() => {
                window.close();
            }, 1000);
        } else {
            toast.error('فشلت عملية الدفع');
            router.push('/');
        }
    }, [orderId, router]);

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900">
            <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 text-center">
                <h1 className="text-2xl font-bold text-red-600 dark:text-red-400 mb-4">فشلت عملية الدفع</h1>
                <p className="text-gray-600 dark:text-gray-400">سيتم إغلاق هذه النافذة تلقائياً...</p>
            </div>
        </div>
    );
}

export default function PaymentFailed() {
    return (
        <Suspense fallback={<div className="text-center mt-10 text-foreground">جاري التحميل...</div>}>
            <PaymentFailedContent />
        </Suspense>
    );
}