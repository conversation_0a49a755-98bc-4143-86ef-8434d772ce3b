// إعدادات البيئة للمنصة التعليمية - zaki alkholy

// ===============================
// متغيرات البيئة - zaki alkholy
// ===============================

export const ENV = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000',
  FRONTEND_URL: process.env.NEXT_PUBLIC_FRONTEND_URL || 'http://localhost:3000',
  
  // إعدادات قاعدة البيانات (للمرجع فقط - تُستخدم في الباك إند)
  DATABASE_URL: process.env.DATABASE_URL,
  
  // إعدادات التخزين السحابي
  CLOUDINARY_CLOUD_NAME: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  CLOUDINARY_API_KEY: process.env.CLOUDINARY_API_KEY,
  CLOUDINARY_API_SECRET: process.env.CLOUDINARY_API_SECRET,
  
  // إعدادات المصادقة
  JWT_SECRET: process.env.JWT_SECRET,
  JWT_REFRESH_SECRET: process.env.JWT_REFRESH_SECRET,
  JWT_ACCESS_TOKEN_LIFETIME: process.env.JWT_ACCESS_TOKEN_LIFETIME || '15m',
  JWT_REFRESH_TOKEN_LIFETIME: process.env.JWT_REFRESH_TOKEN_LIFETIME || '7d',
  
  // إعدادات Google OAuth
  GOOGLE_CLIENT_ID: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
  GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET,
  
  // إعدادات البريد الإلكتروني
  EMAIL_HOST: process.env.EMAIL_HOST,
  EMAIL_PORT: process.env.EMAIL_PORT || 587,
  EMAIL_HOST_USER: process.env.EMAIL_HOST_USER,
  EMAIL_HOST_PASSWORD: process.env.EMAIL_HOST_PASSWORD,
  EMAIL_USE_TLS: process.env.EMAIL_USE_TLS === 'true',
  DEFAULT_FROM_EMAIL: process.env.DEFAULT_FROM_EMAIL,
  
  // إعدادات المدفوعات
  PAYMOB_API_KEY: process.env.PAYMOB_API_KEY,
  PAYMOB_INTEGRATION_ID: process.env.PAYMOB_INTEGRATION_ID,
  PAYMOB_HMAC_SECRET: process.env.PAYMOB_HMAC_SECRET,
  
  // إعدادات الإشعارات الفورية
  VAPID_PUBLIC_KEY: process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY,
  VAPID_PRIVATE_KEY: process.env.VAPID_PRIVATE_KEY,
  VAPID_SUBJECT: process.env.VAPID_SUBJECT,
  
  // إعدادات Redis (للتخزين المؤقت)
  REDIS_URL: process.env.REDIS_URL,
  
  // إعدادات Celery (للمهام الخلفية)
  CELERY_BROKER_URL: process.env.CELERY_BROKER_URL,
  CELERY_RESULT_BACKEND: process.env.CELERY_RESULT_BACKEND,
  
  // إعدادات التحليلات
  GOOGLE_ANALYTICS_ID: process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID,
  
  // إعدادات الأمان
  SECRET_KEY: process.env.SECRET_KEY,
  ALLOWED_HOSTS: process.env.ALLOWED_HOSTS?.split(',') || ['localhost', '127.0.0.1'],
  CORS_ALLOWED_ORIGINS: process.env.CORS_ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  
  // إعدادات التطوير
  DEBUG: process.env.DEBUG === 'true',
  LOGGING_LEVEL: process.env.LOGGING_LEVEL || 'INFO',
};

// ===============================
// إعدادات البيئة المختلفة - zaki alkholy
// ===============================

export const ENVIRONMENT_CONFIGS = {
  development: {
    API_TIMEOUT: 30000,
    ENABLE_LOGGING: true,
    ENABLE_MOCK_DATA: true,
    CACHE_ENABLED: false,
    COMPRESSION_ENABLED: false,
    SSL_REQUIRED: false,
    DEBUG_MODE: true,
    HOT_RELOAD: true,
    SOURCE_MAPS: true,
  },
  
  staging: {
    API_TIMEOUT: 20000,
    ENABLE_LOGGING: true,
    ENABLE_MOCK_DATA: false,
    CACHE_ENABLED: true,
    COMPRESSION_ENABLED: true,
    SSL_REQUIRED: true,
    DEBUG_MODE: false,
    HOT_RELOAD: false,
    SOURCE_MAPS: true,
  },
  
  production: {
    API_TIMEOUT: 15000,
    ENABLE_LOGGING: false,
    ENABLE_MOCK_DATA: false,
    CACHE_ENABLED: true,
    COMPRESSION_ENABLED: true,
    SSL_REQUIRED: true,
    DEBUG_MODE: false,
    HOT_RELOAD: false,
    SOURCE_MAPS: false,
  },
};

// ===============================
// الحصول على إعدادات البيئة الحالية - zaki alkholy
// ===============================

export const getCurrentEnvironmentConfig = () => {
  return ENVIRONMENT_CONFIGS[ENV.NODE_ENV] || ENVIRONMENT_CONFIGS.development;
};

// ===============================
// التحقق من صحة متغيرات البيئة - zaki alkholy
// ===============================

export const validateEnvironmentVariables = () => {
  const requiredVars = {
    development: ['NEXT_PUBLIC_API_URL'],
    staging: ['NEXT_PUBLIC_API_URL', 'JWT_SECRET', 'SECRET_KEY'],
    production: [
      'NEXT_PUBLIC_API_URL',
      'JWT_SECRET',
      'SECRET_KEY',
      'DATABASE_URL',
      'EMAIL_HOST_USER',
      'EMAIL_HOST_PASSWORD',
    ],
  };

  const currentEnvVars = requiredVars[ENV.NODE_ENV] || requiredVars.development;
  const missingVars = [];

  currentEnvVars.forEach(varName => {
    if (!ENV[varName.replace('NEXT_PUBLIC_', '')]) {
      missingVars.push(varName);
    }
  });

  if (missingVars.length > 0) {
    return false;
  }

  return true;
};

// ===============================
// إعدادات الأمان حسب البيئة - zaki alkholy
// ===============================

export const getSecurityConfig = () => {
  const baseConfig = {
    httpOnly: true,
    secure: ENV.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 أيام
  };

  if (ENV.NODE_ENV === 'production') {
    return {
      ...baseConfig,
      domain: new URL(ENV.FRONTEND_URL).hostname,
      secure: true,
    };
  }

  return baseConfig;
};

// ===============================
// إعدادات CORS حسب البيئة - zaki alkholy
// ===============================

export const getCorsConfig = () => {
  if (ENV.NODE_ENV === 'development') {
    return {
      origin: true,
      credentials: true,
    };
  }

  return {
    origin: ENV.CORS_ALLOWED_ORIGINS,
    credentials: true,
    optionsSuccessStatus: 200,
  };
};

// ===============================
// إعدادات قاعدة البيانات حسب البيئة - zaki alkholy
// ===============================

export const getDatabaseConfig = () => {
  const baseConfig = {
    ENGINE: 'django.db.backends.postgresql',
    OPTIONS: {
      'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
    },
    CONN_MAX_AGE: 600,
  };

  if (ENV.NODE_ENV === 'production') {
    return {
      ...baseConfig,
      CONN_MAX_AGE: 0, // إغلاق الاتصالات فوراً في الإنتاج
      OPTIONS: {
        ...baseConfig.OPTIONS,
        'sslmode': 'require',
      },
    };
  }

  return baseConfig;
};

// ===============================
// إعدادات التخزين المؤقت حسب البيئة - zaki alkholy
// ===============================

export const getCacheConfig = () => {
  if (ENV.NODE_ENV === 'development') {
    return {
      BACKEND: 'django.core.cache.backends.locmem.LocMemCache',
      LOCATION: 'unique-snowflake',
    };
  }

  return {
    BACKEND: 'django_redis.cache.RedisCache',
    LOCATION: ENV.REDIS_URL,
    OPTIONS: {
      CLIENT_CLASS: 'django_redis.client.DefaultClient',
    },
  };
};

// ===============================
// إعدادات السجلات حسب البيئة - zaki alkholy
// ===============================

export const getLoggingConfig = () => {
  const baseConfig = {
    version: 1,
    disable_existing_loggers: false,
    formatters: {
      verbose: {
        format: '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
        style: '{',
      },
      simple: {
        format: '{levelname} {message}',
        style: '{',
      },
    },
  };

  if (ENV.NODE_ENV === 'production') {
    return {
      ...baseConfig,
      handlers: {
        file: {
          level: 'INFO',
          class: 'logging.FileHandler',
          filename: 'logs/django.log',
          formatter: 'verbose',
        },
        console: {
          level: 'ERROR',
          class: 'logging.StreamHandler',
          formatter: 'simple',
        },
      },
      root: {
        handlers: ['file', 'console'],
      },
    };
  }

  return {
    ...baseConfig,
    handlers: {
      console: {
        level: 'DEBUG',
        class: 'logging.StreamHandler',
        formatter: 'verbose',
      },
    },
    root: {
      handlers: ['console'],
    },
  };
};

// تشغيل التحقق من متغيرات البيئة عند تحميل الملف
validateEnvironmentVariables();
