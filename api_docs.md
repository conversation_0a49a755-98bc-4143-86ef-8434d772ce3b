# وثائق API لمنصة التعلم الإلكتروني

## المصادقة والتسجيل

### تسجيل مستخدم جديد

- **URL**: `/api/auth/register/`
- **Method**: POST
- **Content-Type**: `multipart/form-data`
- **Body**:

```form-data
username: string
email: string
password: string
phone_number: string (optional)
profile_image: file (optional)
is_instructor: boolean (optional, default: false)
```

- **Response** (201 Created):

```json
{
    "id": "uuid",
    "username": "string",
    "email": "string",
    "phone_number": "string",
    "profile_image": "url",
    "is_instructor": boolean,
    "token": {
        "access": "string",
        "refresh": "string"
    }
}
```

### تسجيل الدخول

- **URL**: `/api/auth/login/`
- **Method**: POST
- **Content-Type**: `application/json`
- **Body**:

```json
{
  "username": "string",
  "password": "string"
}
```

- **Response** (200 OK):

```json
{
    "token": {
        "access": "string",
        "refresh": "string"
    },
    "user": {
        "id": "uuid",
        "username": "string",
        "email": "string",
        "is_instructor": boolean,
        "profile_image": "url"
    }
}
```

## المستخدمين

### الحصول على معلومات المستخدم

- **URL**: `/api/users/{user_id}/`
- **Method**: GET
- **Headers**: `Authorization: Bearer {token}`

- **Response** (200 OK):

```json
{
    "id": "uuid",
    "username": "string",
    "email": "string",
    "phone_number": "string",
    "profile_image": "url",
    "bio": "string",
    "date_of_birth": "date",
    "language": "string",
    "is_instructor": boolean,
    "created_at": "datetime"
}
```

### تحديث معلومات المستخدم

- **URL**: `/api/users/{user_id}/`
- **Method**: PUT
- **Headers**: `Authorization: Bearer {token}`
- **Body**:

```json
{
  "phone_number": "string",
  "profile_image": "file",
  "bio": "string",
  "date_of_birth": "date",
  "language": "string"
}
```

- **Response** (200 OK):

```json
{
    "id": "uuid",
    "username": "string",
    "email": "string",
    "phone_number": "string",
    "profile_image": "url",
    "bio": "string",
    "date_of_birth": "date",
    "language": "string",
    "is_instructor": boolean,
    "updated_at": "datetime"
}
```

## الدورات

### الحصول على قائمة الدورات

- **URL**: `/api/courses/`
- **Method**: GET
- **Query Parameters**:

  - `category`: string (optional)
  - `level`: string (optional)
  - `language`: string (optional)
  - `instructor`: uuid (optional)

- **Response** (200 OK):

```json
{
    "count": number,
    "next": "url",
    "previous": "url",
    "results": [
        {
            "id": "uuid",
            "title": "string",
            "description": "string",
            "short_description": "string",
            "category": {
                "id": "uuid",
                "name": "string"
            },
            "price": number,
            "discount_price": number,
            "currency": "string",
            "level": "string",
            "language": "string",
            "thumbnail": "url",
            "promo_video": "url",
            "instructor": {
                "id": "uuid",
                "username": "string"
            },
            "rating": number,
            "students_count": number,
            "created_at": "datetime"
        }
    ]
}
```

### إنشاء دورة جديدة

- **URL**: `/api/courses/`
- **Method**: POST
- **Headers**: 
  - `Authorization: Bearer {token}`
- **Content-Type**: `multipart/form-data`
- **Body**:

```form-data
title: string (required)
description: string (required)
short_description: string (optional)
category: uuid (required)
price: number (required)
discount_price: number (optional)
currency: string (default: "USD")
level: string (required, choices: ["beginner", "intermediate", "advanced"])
language: string (default: "Arabic")
prerequisites: string (optional)
learning_outcomes: string (optional)
max_students: number (optional)
thumbnail: file (required, formats: jpg, jpeg, png)
promo_video: file (optional, formats: mp4, mov, avi)
is_published: boolean (optional, default: false)
```

- **Response** (201 Created):

```json
{
    "id": "uuid",
    "title": "string",
    "description": "string",
    "short_description": "string",
    "category": {
        "id": "uuid",
        "name": "string"
    },
    "price": number,
    "discount_price": number,
    "currency": "string",
    "level": "string",
    "language": "string",
    "prerequisites": "string",
    "learning_outcomes": "string",
    "max_students": number,
    "thumbnail": "cloudinary_url",
    "promo_video": "cloudinary_url",
    "instructor": {
        "id": "uuid",
        "username": "string"
    },
    "created_at": "datetime"
}
```

### تحديث دورة

- **URL**: `/api/courses/{course_id}/`
- **Method**: PUT
- **Headers**: `Authorization: Bearer {token}`
- **Body**: نفس هيكل إنشاء دورة جديدة

### حذف دورة

- **URL**: `/api/courses/{course_id}/`
- **Method**: DELETE
- **Headers**: `Authorization: Bearer {token}`

## الدروس

### الحصول على دروس الدورة

- **URL**: `/api/courses/{course_id}/lessons/`
- **Method**: GET
- **Headers**: `Authorization: Bearer {token}`

- **Response** (200 OK):

```json
{
    "count": number,
    "next": "url",
    "previous": "url",
    "results": [
        {
            "id": "uuid",
            "title": "string",
            "order": number,
            "lesson_type": "string",
            "content": "string",
            "is_preview": boolean,
            "duration": number,
            "resources": ["url"],
            "created_at": "datetime"
        }
    ]
}
```

### إنشاء درس جديد

- **URL**: `/api/courses/{course_id}/lessons/`
- **Method**: POST
- **Headers**: 
  - `Authorization: Bearer {token}`
- **Content-Type**: `multipart/form-data`
- **Body**:

```form-data
title: string (required)
content: string (required)
order: number (required)
lesson_type: string (required, choices: ["video", "article", "quiz", "assignment"])
is_preview: boolean (optional, default: false)
duration: number (optional, in minutes)
resources: file (optional)
is_drm_protected: boolean (optional, default: true)
is_hls_encrypted: boolean (optional, default: true)
token_expiry_hours: number (optional, default: 24)
watermark_enabled: boolean (optional, default: true)
```

- **Response** (201 Created):

```json
{
    "id": "uuid",
    "title": "string",
    "content": "string",
    "order": number,
    "lesson_type": "string",
    "is_preview": boolean,
    "video": "cloudinary_url",
    "duration": number,
    "resources": "url",
    "is_drm_protected": boolean,
    "is_hls_encrypted": boolean,
    "token_expiry_hours": number,
    "watermark_enabled": boolean,
    "created_at": "datetime"
}
```

### الحصول على رابط الفيديو المؤمن

- **URL**: `/api/lessons/{lesson_id}/video_url/`
- **Method**: GET
- **Headers**: 
  - `Authorization: Bearer {token}`

- **Response** (200 OK):

```json
{
    "video_url": "secured_cloudinary_url",
    "token": "string",
    "expires_at": "datetime",
    "duration": number,
    "is_drm_protected": boolean,
    "is_hls_encrypted": boolean
}
```

### رفع فيديو الدرس

- **URL**: `/api/lessons/{lesson_id}/upload_video/`
- **Method**: POST
- **Headers**: 
  - `Authorization: Bearer {token}`
- **Content-Type**: `multipart/form-data`
- **Body**:

```form-data
video: file (required, formats: mp4, mov, avi)
```

- **Response** (200 OK):

```json
{
    "id": "uuid",
    "title": "string",
    "video": "cloudinary_url",
    "video_public_id": "string",
    "updated_at": "datetime"
}
```

## المنتجات الرقمية

### الحصول على قائمة المنتجات

- **URL**: `/api/digital-products/`
- **Method**: GET
- **Query Parameters**:

  - `category`: string (optional)
  - `seller`: uuid (optional)

- **Response** (200 OK):

```json
{
    "count": number,
    "next": "url",
    "previous": "url",
    "results": [
        {
            "id": "uuid",
            "title": "string",
            "description": "string",
            "price": number,
            "currency": "string",
            "category": "string",
            "thumbnail": "url",
            "seller": {
                "id": "uuid",
                "username": "string"
            },
            "created_at": "datetime"
        }
    ]
}
```

### إنشاء منتج جديد

- **URL**: `/api/products/`
- **Method**: POST
- **Headers**: 
  - `Authorization: Bearer {token}`
- **Content-Type**: `multipart/form-data`
- **Body**:

```form-data
title: string (required)
description: string (required)
price: number (required)
file: file (required)
thumbnail: file (required, formats: jpg, jpeg, png)
download_limit: number (optional, default: 3)
is_published: boolean (optional, default: false)
```

- **Response** (201 Created):

```json
{
    "id": "uuid",
    "title": "string",
    "description": "string",
    "price": number,
    "file": "secured_url",
    "thumbnail": "cloudinary_url",
    "download_limit": number,
    "is_published": boolean,
    "created_at": "datetime"
}
```

## الطلبات والمدفوعات

### إنشاء طلب جديد

- **URL**: `/api/orders/`
- **Method**: POST
- **Headers**: `Authorization: Bearer {token}`
- **Body**:

```json
{
  "items": [
    {
      "type": "course|digital_product",
      "id": "uuid"
    }
  ]
}
```

- **Response** (201 Created):

```json
{
    "id": "uuid",
    "items": [
        {
            "type": "string",
            "id": "uuid",
            "title": "string",
            "price": number
        }
    ],
    "total_amount": number,
    "currency": "string",
    "status": "string",
    "created_at": "datetime"
}
```

### معالجة الدفع

- **URL**: `/api/orders/{order_id}/process_payment/`
- **Method**: POST
- **Headers**: `Authorization: Bearer {token}`
- **Body**:

```json
{
  "payment_method": "string",
  "transaction_id": "string"
}
```

- **Response** (200 OK):

```json
{
    "id": "uuid",
    "order": {
        "id": "uuid",
        "total_amount": number,
        "currency": "string"
    },
    "payment_method": "string",
    "transaction_id": "string",
    "status": "string",
    "created_at": "datetime"
}
```

## الاختبارات

### إنشاء اختبار

- **URL**: `/api/quizzes/`
- **Method**: POST
- **Headers**: `Authorization: Bearer {token}`
- **Body**:

```json
{
    "lesson": "uuid",
    "title": "string",
    "description": "string",
    "passing_score": number,
    "time_limit": number
}
```

- **Response** (201 Created):

```json
{
    "id": "uuid",
    "lesson": {
        "id": "uuid",
        "title": "string"
    },
    "title": "string",
    "description": "string",
    "passing_score": number,
    "time_limit": number,
    "created_at": "datetime"
}
```

### بدء اختبار

- **URL**: `/api/quizzes/{quiz_id}/start/`
- **Method**: POST
- **Headers**: `Authorization: Bearer {token}`

- **Response** (200 OK):

```json
{
    "quiz_id": "uuid",
    "start_time": "datetime",
    "end_time": "datetime",
    "questions": [
        {
            "id": "uuid",
            "text": "string",
            "type": "string",
            "points": number,
            "answers": [
                {
                    "id": "uuid",
                    "text": "string"
                }
            ]
        }
    ]
}
```

### إرسال إجابات الاختبار

- **URL**: `/api/quizzes/{quiz_id}/submit/`
- **Method**: POST
- **Headers**: `Authorization: Bearer {token}`
- **Body**:

```json
{
  "answers": [
    {
      "question_id": "uuid",
      "answer_id": "uuid"
    }
  ]
}
```

- **Response** (200 OK):

```json
{
    "quiz_id": "uuid",
    "score": number,
    "total_points": number,
    "passed": boolean,
    "correct_answers": number,
    "total_questions": number,
    "completion_time": "duration"
}
```

## الإعلانات والأسئلة الشائعة

### الحصول على الإعلانات

- **URL**: `/api/announcements/`
- **Method**: GET
- **Headers**: `Authorization: Bearer {token}`

- **Response** (200 OK):

```json
{
    "count": number,
    "next": "url",
    "previous": "url",
    "results": [
        {
            "id": "uuid",
            "title": "string",
            "content": "string",
            "is_important": boolean,
            "created_at": "datetime"
        }
    ]
}
```

### إنشاء إعلان جديد

- **URL**: `/api/announcements/`
- **Method**: POST
- **Headers**: `Authorization: Bearer {token}`
- **Body**:

```json
{
    "title": "string",
    "content": "string",
    "is_important": boolean
}
```

- **Response** (201 Created):

```json
{
    "id": "uuid",
    "title": "string",
    "content": "string",
    "is_important": boolean,
    "created_at": "datetime"
}
```

### الحصول على الأسئلة الشائعة

- **URL**: `/api/faqs/`
- **Method**: GET
- **Headers**: `Authorization: Bearer {token}`

- **Response** (200 OK):

```json
{
    "count": number,
    "next": "url",
    "previous": "url",
    "results": [
        {
            "id": "uuid",
            "question": "string",
            "answer": "string",
            "category": "string",
            "created_at": "datetime"
        }
    ]
}
```

### إنشاء سؤال شائع جديد

- **URL**: `/api/faqs/`
- **Method**: POST
- **Headers**: `Authorization: Bearer {token}`
- **Body**:

```json
{
  "question": "string",
  "answer": "string",
  "category": "string"
}
```

- **Response** (201 Created):

```json
{
  "id": "uuid",
  "question": "string",
  "answer": "string",
  "category": "string",
  "created_at": "datetime"
}
```

## الشهادات

### الحصول على شهادة

- **URL**: `/api/certificates/{course_id}/`
- **Method**: GET
- **Headers**: `Authorization: Bearer {token}`

- **Response** (200 OK):

```json
{
  "id": "uuid",
  "course": {
    "id": "uuid",
    "title": "string"
  },
  "user": {
    "id": "uuid",
    "username": "string"
  },
  "certificate_url": "url",
  "issue_date": "datetime",
  "expiry_date": "datetime"
}
```

## الفئات

### الحصول على قائمة الفئات

- **URL**: `/api/categories/`
- **Method**: GET

- **Response** (200 OK):

```json
{
    "count": number,
    "next": "url",
    "previous": "url",
    "results": [
        {
            "id": "uuid",
            "name": "string",
            "description": "string",
            "created_at": "datetime"
        }
    ]
}
```

### إنشاء فئة جديدة

- **URL**: `/api/categories/`
- **Method**: POST
- **Headers**: `Authorization: Bearer {token}`
- **Body**:

```json
{
    "name": "string",
    "description": "string"
}
```

- **Response** (201 Created):

```json
{
    "id": "uuid",
    "name": "string",
    "description": "string",
    "created_at": "datetime"
}
```

### تحديث فئة

- **URL**: `/api/categories/{category_id}/`
- **Method**: PUT
- **Headers**: `Authorization: Bearer {token}`
- **Body**:

```json
{
    "name": "string",
    "description": "string"
}
```

- **Response** (200 OK):

```json
{
    "id": "uuid",
    "name": "string",
    "description": "string",
    "updated_at": "datetime"
}
```

### حذف فئة

- **URL**: `/api/categories/{category_id}/`
- **Method**: DELETE
- **Headers**: `Authorization: Bearer {token}`

## ملاحظات مهمة

### الملفات المدعومة
- **الصور**: jpg, jpeg, png, gif
- **الفيديوهات**: mp4, mov, avi, wmv
- **الحد الأقصى لحجم الملف**: 
  - الصور: 10MB
  - الفيديوهات: 100MB
  - الملفات الرقمية: 50MB

### حماية المحتوى
- جميع الفيديوهات محمية بـ DRM وتشفير HLS افتراضياً
- روابط الفيديو مؤقتة وتنتهي صلاحيتها بعد 24 ساعة
- يتم إضافة علامة مائية للفيديوهات تلقائياً
- يمكن تخصيص إعدادات الحماية لكل درس على حدة

### التخزين
- يتم تخزين جميع الملفات على Cloudinary
- يتم ضغط الصور تلقائياً مع الحفاظ على الجودة
- يتم تحويل الفيديوهات تلقائياً لدعم التشغيل التكيفي (Adaptive Streaming)
- يتم إنشاء نسخ مختلفة من الفيديو بجودات متعددة

## Users API

### Get All Users
```http
GET /api/users/list-all/
```

Returns a list of all users in the system.

#### Headers
- `Authorization`: Bearer token required

#### Response
```json
{
    "message": "تم جلب المستخدمين بنجاح",
    "count": 2,
    "users": [
        {
            "id": "uuid",
            "username": "string",
            "email": "string",
            "is_instructor": boolean,
            "is_student": boolean,
            "profile_image": "string or null",
            "phone_number": "string or null",
            "bio": "string or null",
            "date_joined": "datetime"
        }
    ]
}
```

#### Error Responses
- `401 Unauthorized`: If no valid authentication token is provided
- `500 Internal Server Error`: If there's an error retrieving users

### Register New User
```http
POST /api/auth/register/
```

Register a new user in the system.

#### Request Body
```json
{
    "username": "string",
    "email": "string",
    "password": "string",
    "is_instructor": boolean,
    "is_student": boolean,
    "phone_number": "string",
    "bio": "string"
}
```

#### Response
```json
{
    "user": {
        "id": "uuid",
        "username": "string",
        "email": "string",
        "is_instructor": boolean,
        "is_student": boolean,
        "profile_image": "string or null",
        "phone_number": "string or null",
        "bio": "string or null",
        "date_joined": "datetime"
    },
    "refresh": "string",
    "access": "string"
}
```

### Login
```http
POST /api/auth/login/
```

Authenticate a user and get access token.

#### Request Body
```json
{
    "username": "string",
    "password": "string"
}
```

#### Response
```json
{
    "user": {
        "id": "uuid",
        "username": "string",
        "email": "string",
        "is_instructor": boolean,
        "is_student": boolean,
        "profile_image": "string or null",
        "phone_number": "string or null",
        "bio": "string or null",
        "date_joined": "datetime"
    },
    "refresh": "string",
    "access": "string"
}
```

#### Error Responses
- `401 Unauthorized`: If credentials are invalid
