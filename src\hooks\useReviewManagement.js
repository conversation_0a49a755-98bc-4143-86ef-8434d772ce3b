import { useState, useCallback } from 'react';
import Cookies from 'js-cookie';
import {
  approveReview,
  deleteReview,
  replyToReview,
  fetchReviewComments,
  addReviewComment,
  deleteReviewComment
} from '../services/instructor';

export const useReviewManagement = (setError) => {
  const [approving, setApproving] = useState(false);
  const [reviewComments, setReviewComments] = useState({});
  const [commentText, setCommentText] = useState({});
  const [commentLoading, setCommentLoading] = useState({});
  const [replyText, setReplyText] = useState({});
  const [replying, setReplying] = useState({});

  // حالات جديدة للتعليقات الشجرية - zaki alkholy
  const [showReplyForm, setShowReplyForm] = useState({});
  const [expandedComments, setExpandedComments] = useState({});

  const handleApproveReview = useCallback(async (reviewId, approve = true, onSuccess) => {
    setApproving(true);
    try {
      const token = Cookies.get("authToken");
      await approveReview(reviewId, approve, token);
      // استدعاء callback لتحديث قائمة التقييمات
      if (typeof onSuccess === 'function') {
        onSuccess(reviewId);
      }
    } catch (error) {
      console.error("Error approving review:", error);
      setError("فشل في الموافقة على التقييم");
    } finally {
      setApproving(false);
    }
  }, [setError]);

  const handleDeleteReview = useCallback(async (reviewId, onSuccess) => {
    try {
      const token = Cookies.get("authToken");
      await deleteReview(reviewId, token);
      if (typeof onSuccess === 'function') {
        onSuccess(reviewId);
      }
    } catch (error) {
      console.error("Error deleting review:", error);
      setError("فشل في حذف التقييم");
    }
  }, [setError]);

  const handleReplyToReview = useCallback(async (reviewId, replyContent) => {
    setReplying((prev) => ({ ...prev, [reviewId]: true }));
    try {
      const token = Cookies.get("authToken");
      await replyToReview(reviewId, replyContent, token);
      setReplyText((prev) => ({ ...prev, [reviewId]: "" }));
      // يمكن إضافة callback لتحديث قائمة التقييمات
    } catch (error) {
      console.error("Error replying to review:", error);
      setError("فشل في الرد على التقييم");
    } finally {
      setReplying((prev) => ({ ...prev, [reviewId]: false }));
    }
  }, [setError]);

  const handleFetchComments = useCallback(async (reviewId) => {
    setCommentLoading((prev) => ({ ...prev, [reviewId]: true }));
    try {
      const token = Cookies.get("authToken");
      const comments = await fetchReviewComments(reviewId, token);
      setReviewComments((prev) => ({ ...prev, [reviewId]: comments }));
    } catch (error) {
      console.error("Error fetching comments:", error);
      setError("فشل في جلب التعليقات");
    } finally {
      setCommentLoading((prev) => ({ ...prev, [reviewId]: false }));
    }
  }, [setError]);

  const handleAddComment = useCallback(async (reviewId, commentContent, parentId = null) => {
    setCommentLoading((prev) => ({ ...prev, [reviewId]: true }));
    try {
      const token = Cookies.get("authToken");
      await addReviewComment(reviewId, commentContent, parentId, token);
      setCommentText((prev) => ({ ...prev, [reviewId]: "" }));

      // إذا كان رد، امسح نص الرد وأخفي النموذج
      if (parentId) {
        const replyKey = `${reviewId}-${parentId}`;
        setReplyText((prev) => ({ ...prev, [replyKey]: "" }));
        setShowReplyForm((prev) => ({ ...prev, [replyKey]: false }));
      }

      // إعادة جلب التعليقات
      await handleFetchComments(reviewId);
    } catch (error) {
      console.error("Error adding comment:", error);
      setError("فشل في إضافة التعليق");
    } finally {
      setCommentLoading((prev) => ({ ...prev, [reviewId]: false }));
    }
  }, [setError, handleFetchComments]);

  // دالة لتبديل عرض نموذج الرد - zaki alkholy
  const handleToggleReplyForm = useCallback((reviewId, commentId) => {
    const replyKey = `${reviewId}-${commentId}`;
    setShowReplyForm((prev) => ({ ...prev, [replyKey]: !prev[replyKey] }));
  }, []);

  // دالة لتبديل توسيع التعليقات - zaki alkholy
  const handleToggleExpand = useCallback((reviewId) => {
    setExpandedComments((prev) => ({ ...prev, [reviewId]: !prev[reviewId] }));
  }, []);

  const handleDeleteComment = useCallback(async (reviewId, commentId) => {
    try {
      const token = Cookies.get("authToken");
      await deleteReviewComment(reviewId, commentId, token);
      // إعادة جلب التعليقات بعد الحذف
      await handleFetchComments(reviewId);
    } catch (error) {
      console.error("Error deleting comment:", error);
      setError("فشل في حذف التعليق");
    }
  }, [handleFetchComments, setError]);

  return {
    approving,
    reviewComments,
    setReviewComments,
    commentText,
    setCommentText,
    commentLoading,
    replyText,
    setReplyText,
    replying,
    showReplyForm,
    expandedComments,
    handleApproveReview,
    handleDeleteReview,
    handleReplyToReview,
    handleFetchComments,
    handleAddComment,
    handleToggleReplyForm,
    handleToggleExpand,
    handleDeleteComment,
  };
};
