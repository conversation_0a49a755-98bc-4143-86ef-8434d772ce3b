// قائمة الميزات المتقدمة الجديدة في النافيجيشن - zaki alkholy
'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '../../../store/authSlice';
import { 
  ChevronDown, 
  BarChart3, 
  Trophy, 
  FileText, 
  Brain, 
  Star,
  Target,
  BookOpen,
  Award
} from 'lucide-react';

// مكون قائمة الميزات المتقدمة - zaki alkholy
export default function AdvancedFeaturesMenu() {
  const [isOpen, setIsOpen] = useState(false);
  const user = useSelector(selectCurrentUser);

  // إذا لم يكن المستخدم مسجل دخول، لا نعرض القائمة
  if (!user) return null;

  // روابط الطلاب - zaki alkholy
  const studentLinks = [
    {
      href: '/student/progress',
      label: 'تتبع التقدم',
      icon: BarChart3,
      description: 'تابع نقاطك وإنجازاتك'
    },
    {
      href: '/student/points-store',
      label: 'متجر النقاط',
      icon: Star,
      description: 'استبدل نقاطك بمكافآت'
    },
    {
      href: '/student/review',
      label: 'المراجعة الذكية',
      icon: Brain,
      description: 'راجع ما تعلمته بذكاء'
    }
  ];

  // عرض أدوات الطالب فقط في navbar - أدوات المعلم تم نقلها إلى aside - zaki alkholy
  // إذا كان المستخدم معلم، لا نعرض القائمة في navbar
  if (user.is_instructor) return null;

  // تحديد الروابط والعنوان للطلاب فقط - zaki alkholy
  const links = studentLinks;
  const menuTitle = 'أدوات الطالب';

  return (
    <div className="relative">
      {/* زر القائمة - zaki alkholy */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:text-primary transition-colors duration-200"
      >
        <Award className="w-4 h-4" />
        <span className="font-medium">{menuTitle}</span>
        <ChevronDown 
          className={`w-4 h-4 transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`} 
        />
      </button>

      {/* القائمة المنسدلة - zaki alkholy */}
      {isOpen && (
        <div className="absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
          <div className="px-4 py-2 border-b border-gray-100">
            <h3 className="font-semibold text-gray-900">{menuTitle}</h3>
            <p className="text-sm text-gray-600">
              {user.is_instructor ? 'أدوات متقدمة لإدارة دوراتك' : 'ميزات ذكية لتحسين تعلمك'}
            </p>
          </div>
          
          <div className="py-2">
            {links.map((link) => {
              const Icon = link.icon;
              return (
                <Link
                  key={link.href}
                  href={link.href}
                  onClick={() => setIsOpen(false)}
                  className="flex items-start gap-3 px-4 py-3 hover:bg-gray-50 transition-colors duration-200"
                >
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Icon className="w-4 h-4 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{link.label}</h4>
                    <p className="text-sm text-gray-600">{link.description}</p>
                  </div>
                </Link>
              );
            })}
          </div>

          {/* رابط إضافي للطلاب - zaki alkholy */}
          <div className="border-t border-gray-100 pt-2">
            <Link
              href="/student/dashboard"
              onClick={() => setIsOpen(false)}
              className="flex items-center gap-3 px-4 py-2 text-sm text-gray-600 hover:text-primary hover:bg-gray-50 transition-colors duration-200"
            >
              <Target className="w-4 h-4" />
              <span>لوحة التحكم الرئيسية</span>
            </Link>
          </div>
        </div>
      )}

      {/* خلفية شفافة لإغلاق القائمة عند النقر خارجها - zaki alkholy */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
