// أداة اختبار الاتصال مع API - zaki alkholy
import { toast } from 'react-hot-toast';
import { API_BASE_URL, ENDPOINTS, formatErrorMessage } from '../config/api';

// ===============================
// اختبار الاتصال الأساسي - zaki alkholy
// ===============================

/**
 * اختبار الاتصال الأساسي مع الخادم - zaki alkholy
 * @returns {Promise<boolean>} نتيجة الاختبار
 */
export async function testBasicConnection() {
  try {
    const response = await fetch(`${API_BASE_URL}/api/`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    if (response.ok) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    return false;
  }
}

// ===============================
// اختبار endpoints المصادقة - zaki alkholy
// ===============================

/**
 * اختبار endpoints المصادقة - zaki alkholy
 * @param {string} token - رمز المصادقة (اختياري)
 * @returns {Promise<Object>} نتائج الاختبار
 */
export async function testAuthEndpoints(token = null) {
  const results = {
    login: false,
    register: false,
    user: false,
    googleLogin: false,
  };

  // اختبار endpoint تسجيل الدخول
  try {
    const response = await fetch(`${API_BASE_URL}${ENDPOINTS.auth.login}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'testpassword'
      }),
    });

    results.login = response.status === 400 || response.status === 401; // متوقع فشل مع بيانات وهمية
  } catch (error) {
    // Handle error silently
  }

  // اختبار endpoint التسجيل
  try {
    const response = await fetch(`${API_BASE_URL}${ENDPOINTS.auth.register}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'testpassword',
        username: 'testuser'
      }),
    });

    results.register = response.status === 400 || response.status === 201; // متوقع فشل أو نجاح
  } catch (error) {
    // Handle error silently
  }

  // اختبار endpoint المستخدم (يتطلب token)
  if (token) {
    try {
      const response = await fetch(`${API_BASE_URL}${ENDPOINTS.auth.user}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      results.user = response.ok;
    } catch (error) {
      // Handle error silently
    }
  }

  return results;
}

// ===============================
// اختبار endpoints الطلاب - zaki alkholy
// ===============================

/**
 * اختبار endpoints الطلاب - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @returns {Promise<Object>} نتائج الاختبار
 */
export async function testStudentEndpoints(token) {
  if (!token) {
    return {};
  }

  const results = {
    dashboard: false,
    points: false,
    achievements: false,
    dailyReview: false,
    pointsStore: false,
  };

  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': `Bearer ${token}`,
  };

  // اختبار لوحة تحكم الطالب
  try {
    const response = await fetch(`${API_BASE_URL}${ENDPOINTS.student.dashboard}`, {
      method: 'GET',
      headers,
    });

    results.dashboard = response.ok;
  } catch (error) {
    // Handle error silently
  }

  // اختبار نقاط الطالب
  try {
    const response = await fetch(`${API_BASE_URL}${ENDPOINTS.student.points}`, {
      method: 'GET',
      headers,
    });

    results.points = response.ok;
  } catch (error) {
    // Handle error silently
  }

  // اختبار إنجازات الطالب
  try {
    const response = await fetch(`${API_BASE_URL}${ENDPOINTS.student.achievements}`, {
      method: 'GET',
      headers,
    });

    results.achievements = response.ok;
  } catch (error) {
    // Handle error silently
  }

  // اختبار المراجعة اليومية
  try {
    const response = await fetch(`${API_BASE_URL}${ENDPOINTS.student.review.daily}`, {
      method: 'GET',
      headers,
    });

    results.dailyReview = response.ok;
  } catch (error) {
    // Handle error silently
  }

  // اختبار متجر النقاط
  try {
    const response = await fetch(`${API_BASE_URL}${ENDPOINTS.gamification.pointsStore}`, {
      method: 'GET',
      headers,
    });

    results.pointsStore = response.ok;
  } catch (error) {
    // Handle error silently
  }

  return results;
}

// ===============================
// اختبار endpoints المعلمين - zaki alkholy
// ===============================

/**
 * اختبار endpoints المعلمين - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @returns {Promise<Object>} نتائج الاختبار
 */
export async function testInstructorEndpoints(token) {
  if (!token) {
    return {};
  }

  const results = {
    dashboard: false,
    analytics: false,
    courses: false,
  };

  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': `Bearer ${token}`,
  };

  // اختبار لوحة تحكم المعلم
  try {
    const response = await fetch(`${API_BASE_URL}${ENDPOINTS.instructor.dashboard}`, {
      method: 'GET',
      headers,
    });

    results.dashboard = response.ok;
  } catch (error) {
    // Handle error silently
  }

  // اختبار تحليلات المعلم
  try {
    const response = await fetch(`${API_BASE_URL}${ENDPOINTS.instructor.analytics.overview}`, {
      method: 'GET',
      headers,
    });

    results.analytics = response.ok;
  } catch (error) {
    // Handle error silently
  }

  // اختبار دورات المعلم
  try {
    const response = await fetch(`${API_BASE_URL}${ENDPOINTS.courses.list}`, {
      method: 'GET',
      headers,
    });

    results.courses = response.ok;
  } catch (error) {
    // Handle error silently
  }

  return results;
}

// ===============================
// اختبار شامل للنظام - zaki alkholy
// ===============================

/**
 * تشغيل اختبار شامل للنظام - zaki alkholy
 * @param {string} token - رمز المصادقة (اختياري)
 * @returns {Promise<Object>} تقرير شامل للاختبارات
 */
export async function runComprehensiveTest(token = null) {
  const testReport = {
    timestamp: new Date().toISOString(),
    basicConnection: false,
    authEndpoints: {},
    studentEndpoints: {},
    instructorEndpoints: {},
    overallSuccess: false,
  };

  try {
    // اختبار الاتصال الأساسي
    testReport.basicConnection = await testBasicConnection();

    // اختبار endpoints المصادقة
    testReport.authEndpoints = await testAuthEndpoints(token);

    // اختبار endpoints الطلاب (إذا كان هناك token)
    if (token) {
      testReport.studentEndpoints = await testStudentEndpoints(token);
      testReport.instructorEndpoints = await testInstructorEndpoints(token);
    }

    // حساب النجاح العام
    const successCount = Object.values({
      ...testReport.authEndpoints,
      ...testReport.studentEndpoints,
      ...testReport.instructorEndpoints,
    }).filter(Boolean).length;

    const totalTests = Object.keys({
      ...testReport.authEndpoints,
      ...testReport.studentEndpoints,
      ...testReport.instructorEndpoints,
    }).length;

    testReport.overallSuccess = testReport.basicConnection && (successCount / totalTests) > 0.5;

    // إظهار toast للمستخدم
    if (testReport.overallSuccess) {
      toast.success('✅ جميع الاختبارات نجحت! النظام يعمل بشكل صحيح');
    } else {
      toast.error('❌ بعض الاختبارات فشلت. يرجى مراجعة وحدة التحكم للتفاصيل');
    }

  } catch (error) {
    testReport.error = formatErrorMessage(error);
    toast.error('❌ حدث خطأ أثناء تشغيل الاختبارات');
  }

  return testReport;
}
