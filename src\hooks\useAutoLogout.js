// useAutoLogout: Hook لتسجيل خروج المستخدم تلقائيًا عند انتهاء صلاحية التوكن
import { useDispatch } from "react-redux";
import { useRouter } from "next/navigation";
import { logout } from "../store/authSlice";
import { useEffect } from "react";

export default function useAutoLogout(token) {
  const dispatch = useDispatch();
  const router = useRouter();

  // مدة الخمول القصوى (قابلة للتعديل من env)
  const TIMEOUT = parseInt(process.env.NEXT_PUBLIC_IDLE_TIMEOUT || "1800000"); // 30 دقيقة افتراضيًا

  useEffect(() => {
    if (!token) return;
    let exp;
    try {
      const decoded = JSON.parse(atob(token.split('.')[1]));
      exp = decoded.exp;
    } catch (e) {
      dispatch(logout());
      router.push("/login");
      return;
    }
    if (!exp) {
      dispatch(logout());
      router.push("/login");
      return;
    }
    const expiryTime = exp * 1000 - Date.now();
    // استخدم الأقل بين مدة التوكن ومدة الخمول
    const effectiveTimeout = Math.min(expiryTime, TIMEOUT);
    if (effectiveTimeout <= 0) {
      dispatch(logout());
      router.push("/login");
      return;
    }
    const timer = setTimeout(() => {
      dispatch(logout());
      router.push("/login");
    }, effectiveTimeout);
    return () => clearTimeout(timer);
  }, [token, dispatch, router, TIMEOUT]);
}
