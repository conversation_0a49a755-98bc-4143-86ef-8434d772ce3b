"use client";
import { useState, useEffect } from "react";
import axios from "axios";
import Cookies from "js-cookie";
import { useParams, useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import {
  fetchUserById,
  userDataChange,
} from "../../../../../services/anyUserDataChange";
import { updateUser } from "../../../../../store/authSlice";
import {
  User,
  Mail,
  Phone,
  Calendar,
  FileText,
  Camera,
  Lock,
  Save,
  Eye,
  EyeOff,
  UserCheck,
  Settings,
  CheckCircle,
  AlertCircle,
  Info,
  ArrowLeft,
  Upload,
  Trash2,
  Edit3,
  Shield,
  Star,
  Award,
  Clock,
  MapPin,
  Globe,
  Heart,
  Zap,
  Sparkles,
  RefreshCw,
} from "lucide-react";
export default function StudentSettings() {
  const { id } = useParams();
  const router = useRouter();
  const dispatch = useDispatch(); // إضافة dispatch لتحديث Redux store - zaki alkh<PERSON>
  const [form, setForm] = useState({
    username: "",
    email: "",
  });
  const [passwordForm, setPasswordForm] = useState({
    old_password: "",
    new_password: "",
    confirm_password: "",
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [passwordError, setPasswordError] = useState(null);
  const [passwordSuccess, setPasswordSuccess] = useState(null);
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [imagePreview, setImagePreview] = useState(null);
  const [showPasswords, setShowPasswords] = useState({
    old_password: false,
    new_password: false,
    confirm_password: false,
  });
  const [formTouched, setFormTouched] = useState({});
  const [isFormValid, setIsFormValid] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isDragOver, setIsDragOver] = useState(false);
  const [showTooltips, setShowTooltips] = useState({});
  const [fieldFocus, setFieldFocus] = useState({});
  const [autoSaveTimer, setAutoSaveTimer] = useState(null);

  // دالة للتحقق من صحة النموذج
  const validateForm = () => {
    const isValid =
      form.username && form.email && form.first_name && form.last_name;
    setIsFormValid(isValid);
    return isValid;
  };

  // دالة لإظهار التلميحات
  const showTooltip = (field) => {
    setShowTooltips((prev) => ({ ...prev, [field]: true }));
    setTimeout(() => {
      setShowTooltips((prev) => ({ ...prev, [field]: false }));
    }, 3000);
  };

  // دالة للتعامل مع السحب والإفلات
  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = e.dataTransfer.files;
    if (files[0]) {
      handleFileChange(files[0]);
    }
  };

  const handleFileChange = (file) => {
    setForm((prev) => ({ ...prev, profile_image: file }));

    // محاكاة شريط التقدم
    setUploadProgress(0);
    const interval = setInterval(() => {
      setUploadProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval);
          return 100;
        }
        return prev + 10;
      });
    }, 100);

    const reader = new FileReader();
    reader.onloadend = () => {
      setImagePreview(reader.result);
    };
    reader.readAsDataURL(file);
  };

  useEffect(() => {
    validateForm();
  }, [form]);

  useEffect(() => {
    const fetchProfile = async () => {
      setLoading(true);
      try {
        const token = Cookies.get("authToken");
        if (!token) {
          setError("يرجى تسجيل الدخول");
          return;
        }

        const data = await fetchUserById(id, token);

        setForm({
          username: data.username || "",
          email: data.email || "",
          bio: data.bio || "",
          phone_number: data.phone_number || "",
          date_of_birth: data.date_of_birth || "",
          first_name: data.first_name || "",
          last_name: data.last_name || "",
          profile_image: null,
          is_instructor: data.is_instructor || false,
        });

        setImagePreview(data.profile_image || null);
      } catch {
        setError("تعذر تحميل البيانات");
      } finally {
        setLoading(false);
      }
    };

    if (id) fetchProfile();
  }, [id]);
  // const handleChange = (e) => {
  //   setForm({ ...form, [e.target.name]: e.target.value });
  // };
  const handleChange = (e) => {
    const { name, value, type, files } = e.target;

    // تتبع الحقول المُعدلة
    setFormTouched((prev) => ({ ...prev, [name]: true }));

    if (type === "file") {
      if (files[0]) {
        handleFileChange(files[0]);
      }
    } else if (type === "checkbox") {
      setForm((prev) => ({
        ...prev,
        [name]: e.target.checked,
      }));
    } else {
      setForm((prev) => ({
        ...prev,
        [name]: value,
      }));
    }

    // إزالة رسائل الخطأ عند التعديل
    if (error) setError(null);
    if (success) setSuccess(null);
  };

  // دالة للتعامل مع focus
  const handleFocus = (fieldName) => {
    setFieldFocus((prev) => ({ ...prev, [fieldName]: true }));
  };

  const handleBlur = (fieldName) => {
    setFieldFocus((prev) => ({ ...prev, [fieldName]: false }));
  };
  const handlePasswordChange = (e) => {
    setPasswordForm({ ...passwordForm, [e.target.name]: e.target.value });
  };

  const handlePasswordSave = async (e) => {
    e.preventDefault();
    setSaving(true);
    setPasswordError(null);
    setPasswordSuccess(null);

    try {
      const token = Cookies.get("authToken");

      // التحقق من تطابق كلمة المرور الجديدة
      if (passwordForm.new_password !== passwordForm.confirm_password) {
        setPasswordError("كلمة المرور الجديدة غير متطابقة");
        return;
      }

      const response = await axios.patch(
        `${process.env.NEXT_PUBLIC_API_URL}/auth/change-password/`,
        {
          old_password: passwordForm.old_password,
          new_password: passwordForm.new_password,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      setPasswordSuccess("تم تغيير كلمة المرور بنجاح");
      setPasswordForm({
        old_password: "",
        new_password: "",
        confirm_password: "",
      });
      setShowPasswordForm(false);
    } catch (err) {
      const errorData = err.response?.data;
      setPasswordError(
        errorData?.old_password?.[0] ||
          errorData?.new_password?.[0] ||
          errorData?.message ||
          errorData?.detail ||
          err.message ||
          "فشل تغيير كلمة المرور. يرجى المحاولة مرة أخرى."
      );
    } finally {
      setSaving(false);
    }
  };

  const handleSave = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const token = Cookies.get("authToken");

      // إنشاء FormData
      const formData = new FormData();
      formData.append("username", form.username);
      formData.append("email", form.email);
      formData.append("bio", form.bio);
      formData.append("phone_number", form.phone_number);
      formData.append("date_of_birth", form.date_of_birth);
      formData.append("first_name", form.first_name);
      formData.append("last_name", form.last_name);
      formData.append("is_instructor", form.is_instructor); // Boolean accepted
      if (form.profile_image) {
        formData.append("profile_image", form.profile_image); // ملف
      }

      const updatedUser = await userDataChange(id, token, formData, true);

      // تحديث Redux store بالبيانات الجديدة - zaki alkholy
      console.log("Updated user data:", updatedUser);
      dispatch(updateUser(updatedUser));

      setSuccess("تم حفظ البيانات بنجاح");
      setTimeout(() => {
        router.push(`/student/${id}`);
      }, 1500);
    } catch (err) {
      const errorData = err.response?.data;

      setError(
        errorData?.email?.[0] ||
          errorData?.username?.[0] ||
          errorData?.message ||
          errorData?.detail ||
          err.message ||
          "فشل التعديل. يرجى المحاولة مرة أخرى."
      );
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 pt-16">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Back Button */}
        <div className="mb-6 animate-slide-in-left">
          <button
            onClick={() => router.back()}
            className="flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-primary transition-colors duration-200 group"
          >
            <ArrowLeft className="w-5 h-5 transform group-hover:-translate-x-1 transition-transform duration-200" />
            <span className="font-arabic">العودة</span>
          </button>
        </div>

        {/* Header Section */}
        <div className="text-center mb-12 animate-fade-in">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-primary to-accent rounded-full mb-6 shadow-xl animate-float">
            <Settings className="text-white text-3xl w-10 h-10" />
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent mb-4 font-arabic">
            إعدادات الحساب
          </h1>
          <p className="text-gray-600 dark:text-gray-400 text-lg max-w-2xl mx-auto">
            قم بتحديث معلوماتك الشخصية وإعدادات حسابك لتحسين تجربتك
          </p>

          {/* Quick Stats */}
          <div className="flex justify-center gap-6 mt-8">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mb-2 mx-auto">
                <User className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <span className="text-sm text-gray-600 dark:text-gray-400 font-arabic">
                الملف الشخصي
              </span>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mb-2 mx-auto">
                <Shield className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <span className="text-sm text-gray-600 dark:text-gray-400 font-arabic">
                الأمان
              </span>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mb-2 mx-auto">
                <Star className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
              <span className="text-sm text-gray-600 dark:text-gray-400 font-arabic">
                التفضيلات
              </span>
            </div>
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden animate-fade-in">
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-400 p-4 rounded-xl mb-6 flex items-center animate-slide-in-left">
              <i className="fas fa-exclamation-circle mr-3 text-lg"></i>
              {error}
            </div>
          )}
          {success && (
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 text-green-700 dark:text-green-400 p-4 rounded-xl mb-6 flex items-center animate-slide-in-left">
              <i className="fas fa-check-circle mr-3 text-lg"></i>
              {success}
            </div>
          )}
          {/* Progress Indicator */}
          <div className="bg-white dark:bg-gray-800 rounded-t-2xl p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300 font-arabic">
                اكتمال الملف الشخصي
              </span>
              <span className="text-sm font-bold text-primary">
                {Math.round(
                  (((form.username ? 1 : 0) +
                    (form.email ? 1 : 0) +
                    (form.first_name ? 1 : 0) +
                    (form.last_name ? 1 : 0) +
                    (form.phone_number ? 1 : 0) +
                    (imagePreview ? 1 : 0)) /
                    6) *
                    100
                )}
                %
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-primary to-accent h-2 rounded-full transition-all duration-500 ease-out"
                style={{
                  width: `${
                    (((form.username ? 1 : 0) +
                      (form.email ? 1 : 0) +
                      (form.first_name ? 1 : 0) +
                      (form.last_name ? 1 : 0) +
                      (form.phone_number ? 1 : 0) +
                      (imagePreview ? 1 : 0)) /
                      6) *
                    100
                  }%`,
                }}
              ></div>
            </div>
          </div>

          {/* Form Content */}
          <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-b-3xl shadow-2xl border border-gray-200/50 dark:border-gray-700/50 p-8 animate-slide-up">
            <form onSubmit={handleSave} className="space-y-8">
              {/* Personal Information Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white font-arabic flex items-center gap-2">
                  <User className="w-5 h-5 text-primary" />
                  المعلومات الشخصية
                </h3>

                {/* Username */}
                <div className="space-y-2 group">
                  <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 font-arabic">
                    اسم المستخدم
                    {form.username && (
                      <CheckCircle className="w-4 h-4 text-green-500 animate-bounce-in" />
                    )}
                    <button
                      type="button"
                      onClick={() => showTooltip("username")}
                      className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                    >
                      <Info className="w-4 h-4" />
                    </button>
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <User
                        className={`h-5 w-5 transition-colors duration-200 ${
                          fieldFocus.username ? "text-primary" : "text-gray-400"
                        }`}
                      />
                    </div>
                    <input
                      name="username"
                      value={form.username}
                      onChange={handleChange}
                      onFocus={() => handleFocus("username")}
                      onBlur={() => handleBlur("username")}
                      className={`block w-full pr-10 pl-3 py-3 border rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 transform hover:scale-[1.02] ${
                        formTouched.username && form.username
                          ? "border-green-300 dark:border-green-600 bg-green-50 dark:bg-green-900/20"
                          : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
                      }`}
                      placeholder="أدخل اسم المستخدم"
                    />
                    {showTooltips.username && (
                      <div className="absolute top-full left-0 mt-1 p-2 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg shadow-lg z-10 animate-slide-up">
                        اختر اسم مستخدم فريد وسهل التذكر
                      </div>
                    )}
                  </div>
                </div>

                {/* Email */}
                <div className="space-y-2 group">
                  <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 font-arabic">
                    البريد الإلكتروني
                    {form.email && (
                      <CheckCircle className="w-4 h-4 text-green-500 animate-bounce-in" />
                    )}
                    <button
                      type="button"
                      onClick={() => showTooltip("email")}
                      className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                    >
                      <Info className="w-4 h-4" />
                    </button>
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <Mail
                        className={`h-5 w-5 transition-colors duration-200 ${
                          fieldFocus.email ? "text-primary" : "text-gray-400"
                        }`}
                      />
                    </div>
                    <input
                      name="email"
                      type="email"
                      value={form.email}
                      onChange={handleChange}
                      onFocus={() => handleFocus("email")}
                      onBlur={() => handleBlur("email")}
                      className={`block w-full pr-10 pl-3 py-3 border rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 transform hover:scale-[1.02] ${
                        formTouched.email && form.email
                          ? "border-green-300 dark:border-green-600 bg-green-50 dark:bg-green-900/20"
                          : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
                      }`}
                      placeholder="أدخل البريد الإلكتروني"
                    />
                    {showTooltips.email && (
                      <div className="absolute top-full left-0 mt-1 p-2 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg shadow-lg z-10 animate-slide-up">
                        أدخل بريد إلكتروني صحيح للتواصل معك
                      </div>
                    )}
                  </div>
                </div>

                {/* First and Last Name */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2 group">
                    <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 font-arabic">
                      الاسم الأول
                      {form.first_name && (
                        <CheckCircle className="w-4 h-4 text-green-500 animate-bounce-in" />
                      )}
                    </label>
                    <div className="relative">
                      <input
                        name="first_name"
                        value={form.first_name}
                        onChange={handleChange}
                        onFocus={() => handleFocus("first_name")}
                        onBlur={() => handleBlur("first_name")}
                        className={`block w-full px-3 py-3 border rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 transform hover:scale-[1.02] ${
                          formTouched.first_name && form.first_name
                            ? "border-green-300 dark:border-green-600 bg-green-50 dark:bg-green-900/20"
                            : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
                        }`}
                        placeholder="أدخل الاسم الأول"
                      />
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Star
                          className={`h-4 w-4 transition-colors duration-200 ${
                            fieldFocus.first_name
                              ? "text-yellow-400"
                              : "text-gray-300"
                          }`}
                        />
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2 group">
                    <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 font-arabic">
                      الاسم الأخير
                      {form.last_name && (
                        <CheckCircle className="w-4 h-4 text-green-500 animate-bounce-in" />
                      )}
                    </label>
                    <div className="relative">
                      <input
                        name="last_name"
                        value={form.last_name}
                        onChange={handleChange}
                        onFocus={() => handleFocus("last_name")}
                        onBlur={() => handleBlur("last_name")}
                        className={`block w-full px-3 py-3 border rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 transform hover:scale-[1.02] ${
                          formTouched.last_name && form.last_name
                            ? "border-green-300 dark:border-green-600 bg-green-50 dark:bg-green-900/20"
                            : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
                        }`}
                        placeholder="أدخل الاسم الأخير"
                      />
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Star
                          className={`h-4 w-4 transition-colors duration-200 ${
                            fieldFocus.last_name
                              ? "text-yellow-400"
                              : "text-gray-300"
                          }`}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Phone Number */}
                <div className="space-y-2 group">
                  <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 font-arabic">
                    رقم الهاتف
                    {form.phone_number && (
                      <CheckCircle className="w-4 h-4 text-green-500 animate-bounce-in" />
                    )}
                    <button
                      type="button"
                      onClick={() => showTooltip("phone")}
                      className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                    >
                      <Info className="w-4 h-4" />
                    </button>
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <Phone
                        className={`h-5 w-5 transition-colors duration-200 ${
                          fieldFocus.phone_number
                            ? "text-primary"
                            : "text-gray-400"
                        }`}
                      />
                    </div>
                    <input
                      name="phone_number"
                      type="tel"
                      value={form.phone_number}
                      onChange={handleChange}
                      onFocus={() => handleFocus("phone_number")}
                      onBlur={() => handleBlur("phone_number")}
                      className={`block w-full pr-10 pl-3 py-3 border rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 transform hover:scale-[1.02] ${
                        formTouched.phone_number && form.phone_number
                          ? "border-green-300 dark:border-green-600 bg-green-50 dark:bg-green-900/20"
                          : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
                      }`}
                      placeholder="أدخل رقم الهاتف (مثال: 01012345678)"
                    />
                    {showTooltips.phone && (
                      <div className="absolute top-full left-0 mt-1 p-2 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg shadow-lg z-10 animate-slide-up">
                        أدخل رقم هاتف صحيح للتواصل الطارئ
                      </div>
                    )}
                  </div>
                </div>

                {/* Bio */}
                <div className="space-y-2 group">
                  <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 font-arabic">
                    نبذة عني
                    {form.bio && (
                      <CheckCircle className="w-4 h-4 text-green-500 animate-bounce-in" />
                    )}
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      ({form.bio ? form.bio.length : 0}/200)
                    </span>
                  </label>
                  <div className="relative">
                    <div className="absolute top-3 right-0 pr-3 flex items-start pointer-events-none">
                      <FileText
                        className={`h-5 w-5 transition-colors duration-200 ${
                          fieldFocus.bio ? "text-primary" : "text-gray-400"
                        }`}
                      />
                    </div>
                    <textarea
                      name="bio"
                      value={form.bio}
                      onChange={handleChange}
                      onFocus={() => handleFocus("bio")}
                      onBlur={() => handleBlur("bio")}
                      rows={4}
                      maxLength={200}
                      className={`block w-full pr-10 pl-3 py-3 border rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500 resize-none ${
                        formTouched.bio && form.bio
                          ? "border-green-300 dark:border-green-600 bg-green-50 dark:bg-green-900/20"
                          : "border-gray-300 dark:border-gray-600"
                      }`}
                      placeholder="اكتب نبذة مختصرة عنك، اهتماماتك، وخبراتك..."
                    />
                    <div className="absolute bottom-2 left-2 flex items-center gap-1">
                      <Sparkles className="h-3 w-3 text-yellow-400" />
                      <span className="text-xs text-gray-400">كن مبدعاً!</span>
                    </div>
                  </div>
                </div>

                {/* Date of Birth */}
                <div className="space-y-2 group">
                  <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 font-arabic">
                    تاريخ الميلاد
                    {form.date_of_birth && (
                      <CheckCircle className="w-4 h-4 text-green-500 animate-bounce-in" />
                    )}
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <Calendar
                        className={`h-5 w-5 transition-colors duration-200 ${
                          fieldFocus.date_of_birth
                            ? "text-primary"
                            : "text-gray-400"
                        }`}
                      />
                    </div>
                    <input
                      type="date"
                      name="date_of_birth"
                      value={form.date_of_birth}
                      onChange={handleChange}
                      onFocus={() => handleFocus("date_of_birth")}
                      onBlur={() => handleBlur("date_of_birth")}
                      className={`block w-full pr-10 pl-3 py-3 border rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 transform hover:scale-[1.02] ${
                        formTouched.date_of_birth && form.date_of_birth
                          ? "border-green-300 dark:border-green-600 bg-green-50 dark:bg-green-900/20"
                          : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
                      }`}
                    />
                  </div>
                </div>
              </div>
              {/* Profile Image Section */}
              <div className="space-y-6 pt-8 border-t border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white font-arabic flex items-center gap-2">
                  <Camera className="w-5 h-5 text-primary" />
                  الصورة الشخصية
                  {imagePreview && (
                    <CheckCircle className="w-4 h-4 text-green-500 animate-bounce-in" />
                  )}
                </h3>

                <div className="flex flex-col lg:flex-row items-center gap-8">
                  {/* Image Preview */}
                  <div className="flex-shrink-0">
                    {imagePreview ? (
                      <div className="relative group">
                        <img
                          src={imagePreview}
                          alt="Profile Preview"
                          className="w-32 h-32 object-cover rounded-full border-4 border-primary/20 shadow-xl group-hover:shadow-2xl transition-all duration-300 transform group-hover:scale-105"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 rounded-full transition-all duration-300 flex items-center justify-center">
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-center">
                            <Camera className="w-6 h-6 text-white mx-auto mb-1" />
                            <span className="text-xs text-white font-arabic">
                              تغيير
                            </span>
                          </div>
                        </div>
                        <button
                          type="button"
                          onClick={() => {
                            setImagePreview(null);
                            setForm((prev) => ({
                              ...prev,
                              profile_image: null,
                            }));
                          }}
                          className="absolute -top-2 -right-2 w-8 h-8 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-colors duration-200 shadow-lg"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    ) : (
                      <div className="w-32 h-32 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded-full flex items-center justify-center border-4 border-dashed border-gray-300 dark:border-gray-600">
                        <Camera className="w-12 h-12 text-gray-400" />
                      </div>
                    )}
                  </div>

                  {/* Upload Area */}
                  <div className="flex-1 w-full">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 font-arabic mb-3">
                      اختر صورة جديدة
                    </label>
                    <div
                      className={`relative transition-all duration-300 ${
                        isDragOver ? "scale-105" : ""
                      }`}
                      onDragOver={handleDragOver}
                      onDragLeave={handleDragLeave}
                      onDrop={handleDrop}
                    >
                      <input
                        type="file"
                        name="profile_image"
                        onChange={handleChange}
                        accept="image/*"
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                      />
                      <div
                        className={`flex items-center justify-center w-full px-6 py-8 border-2 border-dashed rounded-xl transition-all duration-300 cursor-pointer ${
                          isDragOver
                            ? "border-primary bg-primary/10 dark:bg-primary/20"
                            : "border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600"
                        }`}
                      >
                        <div className="text-center">
                          <div className="mb-4">
                            {isDragOver ? (
                              <Upload className="mx-auto h-12 w-12 text-primary animate-bounce" />
                            ) : (
                              <Camera className="mx-auto h-12 w-12 text-gray-400" />
                            )}
                          </div>
                          <p className="text-lg font-medium text-gray-700 dark:text-gray-300 font-arabic mb-2">
                            {isDragOver ? "اتركها هنا!" : "اسحب الصورة هنا"}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400 font-arabic">
                            أو اضغط لاختيار صورة من جهازك
                          </p>
                          <p className="text-xs text-gray-400 dark:text-gray-500 mt-2">
                            PNG, JPG, GIF حتى 10MB
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Upload Progress */}
                    {uploadProgress > 0 && uploadProgress < 100 && (
                      <div className="mt-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300 font-arabic">
                            جارٍ الرفع...
                          </span>
                          <span className="text-sm text-primary font-bold">
                            {uploadProgress}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div
                            className="bg-gradient-to-r from-primary to-accent h-2 rounded-full transition-all duration-300 ease-out"
                            style={{ width: `${uploadProgress}%` }}
                          ></div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Instructor Status Section */}
              <div className="space-y-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white font-arabic flex items-center gap-2">
                  <UserCheck className="w-5 h-5 text-primary" />
                  حالة المدرب
                </h3>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 font-arabic">
                    هل أنت مدرب؟
                  </label>
                  <button
                    type="button"
                    onClick={() =>
                      setForm((prev) => ({
                        ...prev,
                        is_instructor: !prev.is_instructor,
                      }))
                    }
                    className={`w-full px-6 py-3 rounded-xl font-semibold border-2 transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                      form.is_instructor
                        ? "bg-green-500 hover:bg-green-600 text-white border-green-500 focus:ring-green-500 shadow-lg"
                        : "bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 focus:ring-gray-500"
                    }`}
                  >
                    <div className="flex items-center justify-center gap-2">
                      {form.is_instructor ? (
                        <>
                          <UserCheck className="w-5 h-5" />
                          <span>✅ نعم، أنا مدرب</span>
                        </>
                      ) : (
                        <>
                          <User className="w-5 h-5" />
                          <span>❌ لا، لست مدربًا</span>
                        </>
                      )}
                    </div>
                  </button>
                </div>
              </div>
              {/* Error and Success Messages */}
              {error && (
                <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl animate-slide-up">
                  <div className="flex items-center gap-2">
                    <div className="w-5 h-5 text-red-500">
                      <svg fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <p className="text-red-700 dark:text-red-400 font-arabic">
                      {error}
                    </p>
                  </div>
                </div>
              )}

              {success && (
                <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl animate-slide-up">
                  <div className="flex items-center gap-2">
                    <div className="w-5 h-5 text-green-500">
                      <svg fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <p className="text-green-700 dark:text-green-400 font-arabic">
                      {success}
                    </p>
                  </div>
                </div>
              )}

              {/* Save Button */}
              <div className="pt-8 border-t border-gray-200 dark:border-gray-700">
                <div className="flex flex-col sm:flex-row gap-4">
                  <button
                    type="submit"
                    disabled={saving || !isFormValid}
                    className={`flex-1 font-semibold py-4 px-6 rounded-xl transition-all duration-300 transform focus:outline-none focus:ring-2 focus:ring-offset-2 shadow-lg font-arabic ${
                      isFormValid && !saving
                        ? "bg-gradient-to-r from-primary to-primary-light hover:from-primary-dark hover:to-primary text-white hover:scale-105 hover:shadow-xl focus:ring-primary"
                        : "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed"
                    }`}
                  >
                    <div className="flex items-center justify-center gap-3">
                      {saving ? (
                        <>
                          <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          <span>جارٍ الحفظ...</span>
                          <div className="flex gap-1">
                            <div
                              className="w-1 h-1 bg-white rounded-full animate-bounce"
                              style={{ animationDelay: "0ms" }}
                            ></div>
                            <div
                              className="w-1 h-1 bg-white rounded-full animate-bounce"
                              style={{ animationDelay: "150ms" }}
                            ></div>
                            <div
                              className="w-1 h-1 bg-white rounded-full animate-bounce"
                              style={{ animationDelay: "300ms" }}
                            ></div>
                          </div>
                        </>
                      ) : (
                        <>
                          <Save className="w-5 h-5" />
                          <span>حفظ التغييرات</span>
                          {isFormValid && (
                            <CheckCircle className="w-5 h-5 text-green-300" />
                          )}
                        </>
                      )}
                    </div>
                  </button>

                  <button
                    type="button"
                    onClick={() => {
                      setForm({
                        username: "",
                        email: "",
                        first_name: "",
                        last_name: "",
                        phone_number: "",
                        bio: "",
                        date_of_birth: "",
                        profile_image: null,
                        is_instructor: false,
                      });
                      setImagePreview(null);
                      setFormTouched({});
                    }}
                    className="px-6 py-4 border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 font-arabic"
                  >
                    <div className="flex items-center gap-2">
                      <RefreshCw className="w-5 h-5" />
                      <span>إعادة تعيين</span>
                    </div>
                  </button>
                </div>

                {/* Form Validation Status */}
                <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400 font-arabic">
                      حالة النموذج:
                    </span>
                    <div className="flex items-center gap-2">
                      {isFormValid ? (
                        <>
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span className="text-sm text-green-600 dark:text-green-400 font-arabic">
                            جاهز للحفظ
                          </span>
                        </>
                      ) : (
                        <>
                          <AlertCircle className="w-4 h-4 text-yellow-500" />
                          <span className="text-sm text-yellow-600 dark:text-yellow-400 font-arabic">
                            يرجى إكمال الحقول المطلوبة
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
        {/* Password Change Section */}
        <div className="mt-8">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden animate-fade-in">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <button
                type="button"
                onClick={() => setShowPasswordForm(!showPasswordForm)}
                className="w-full flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
              >
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <Lock className="w-5 h-5 text-primary" />
                  </div>
                  <div className="text-left">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white font-arabic">
                      تغيير كلمة المرور
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      قم بتحديث كلمة المرور الخاصة بك
                    </p>
                  </div>
                </div>
                <div
                  className={`transform transition-transform duration-300 ${
                    showPasswordForm ? "rotate-180" : ""
                  }`}
                >
                  <svg
                    className="w-5 h-5 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </div>
              </button>
            </div>
            {showPasswordForm && (
              <div className="p-6 animate-slide-up">
                <form onSubmit={handlePasswordSave} className="space-y-6">
                  {/* Old Password */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 font-arabic">
                      كلمة المرور القديمة
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <Lock className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        type={showPasswords.old_password ? "text" : "password"}
                        name="old_password"
                        value={passwordForm.old_password}
                        onChange={handlePasswordChange}
                        className="block w-full pr-10 pl-12 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500"
                        placeholder="أدخل كلمة المرور القديمة"
                      />
                      <button
                        type="button"
                        onClick={() =>
                          setShowPasswords((prev) => ({
                            ...prev,
                            old_password: !prev.old_password,
                          }))
                        }
                        className="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200"
                      >
                        {showPasswords.old_password ? (
                          <EyeOff className="h-5 w-5" />
                        ) : (
                          <Eye className="h-5 w-5" />
                        )}
                      </button>
                    </div>
                  </div>

                  {/* New Password */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 font-arabic">
                      كلمة المرور الجديدة
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <Lock className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        type={showPasswords.new_password ? "text" : "password"}
                        name="new_password"
                        value={passwordForm.new_password}
                        onChange={handlePasswordChange}
                        className="block w-full pr-10 pl-12 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500"
                        placeholder="أدخل كلمة المرور الجديدة"
                      />
                      <button
                        type="button"
                        onClick={() =>
                          setShowPasswords((prev) => ({
                            ...prev,
                            new_password: !prev.new_password,
                          }))
                        }
                        className="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200"
                      >
                        {showPasswords.new_password ? (
                          <EyeOff className="h-5 w-5" />
                        ) : (
                          <Eye className="h-5 w-5" />
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Confirm Password */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 font-arabic">
                      تأكيد كلمة المرور الجديدة
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <Lock className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        type={
                          showPasswords.confirm_password ? "text" : "password"
                        }
                        name="confirm_password"
                        value={passwordForm.confirm_password}
                        onChange={handlePasswordChange}
                        className="block w-full pr-10 pl-12 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500"
                        placeholder="أعد إدخال كلمة المرور الجديدة"
                      />
                      <button
                        type="button"
                        onClick={() =>
                          setShowPasswords((prev) => ({
                            ...prev,
                            confirm_password: !prev.confirm_password,
                          }))
                        }
                        className="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200"
                      >
                        {showPasswords.confirm_password ? (
                          <EyeOff className="h-5 w-5" />
                        ) : (
                          <Eye className="h-5 w-5" />
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Password Error and Success Messages */}
                  {passwordError && (
                    <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl animate-slide-up">
                      <div className="flex items-center gap-2">
                        <div className="w-5 h-5 text-red-500">
                          <svg fill="currentColor" viewBox="0 0 20 20">
                            <path
                              fillRule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                        <p className="text-red-700 dark:text-red-400 font-arabic">
                          {passwordError}
                        </p>
                      </div>
                    </div>
                  )}

                  {passwordSuccess && (
                    <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl animate-slide-up">
                      <div className="flex items-center gap-2">
                        <div className="w-5 h-5 text-green-500">
                          <svg fill="currentColor" viewBox="0 0 20 20">
                            <path
                              fillRule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                        <p className="text-green-700 dark:text-green-400 font-arabic">
                          {passwordSuccess}
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Change Password Button */}
                  <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                    <button
                      type="submit"
                      disabled={saving}
                      className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none shadow-lg hover:shadow-xl font-arabic"
                    >
                      <div className="flex items-center justify-center gap-2">
                        {saving ? (
                          <>
                            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                            <span>جارٍ التغيير...</span>
                          </>
                        ) : (
                          <>
                            <Lock className="w-5 h-5" />
                            <span>تغيير كلمة المرور</span>
                          </>
                        )}
                      </div>
                    </button>
                  </div>
                </form>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
