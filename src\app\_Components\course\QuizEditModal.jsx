import React from "react";
import Modal from "../Modal";

export default function QuizEditModal({
  isOpen,
  onClose,
  onSubmit,
  formState,
  setFormState,
  loading,
}) {
  console.log("QuizEditModal formState:", formState); // للتأكد من البيانات

  const isExam = formState.quiz_type === "exam";

  return (
    <Modal isOpen={isOpen} onClose={onClose} width="max-w-2xl">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <div
            className={`w-16 h-16 ${
              isExam
                ? "bg-gradient-to-r from-red-500 to-orange-600"
                : "bg-gradient-to-r from-green-500 to-emerald-600"
            } rounded-2xl flex items-center justify-center mx-auto mb-4`}
          >
            <svg
              className="w-8 h-8 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              {isExam ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                />
              )}
            </svg>
          </div>
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            تعديل {isExam ? "الامتحان" : "الواجب"}
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            قم بتعديل بيانات {isExam ? "الامتحان" : "الواجب"}
          </p>
        </div>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            console.log("Submitting quiz edit with formState:", formState); // للتأكد من البيانات عند الإرسال
            onSubmit();
          }}
          className="space-y-6"
        >
          {/* Title Input */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              العنوان
            </label>
            <input
              type="text"
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              placeholder={`عنوان ${isExam ? "الامتحان" : "الواجب"}`}
              value={formState.title || ""}
              onChange={(e) =>
                setFormState((f) => ({ ...f, title: e.target.value }))
              }
              required
            />
          </div>

          {/* Description Input */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              الوصف (اختياري)
            </label>
            <textarea
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
              placeholder={`وصف ${isExam ? "الامتحان" : "الواجب"}`}
              value={formState.description || ""}
              onChange={(e) =>
                setFormState((f) => ({ ...f, description: e.target.value }))
              }
              rows={3}
            />
          </div>

          {/* Exam Settings */}
          {isExam && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  الدرجة النهائية
                </label>
                <input
                  type="number"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  placeholder="100"
                  value={formState.max_score || ""}
                  onChange={(e) =>
                    setFormState((f) => ({ ...f, max_score: e.target.value }))
                  }
                  min="0"
                />
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  الوقت المحدد (بالدقائق)
                </label>
                <input
                  type="number"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  placeholder="0"
                  value={formState.time_limit || ""}
                  onChange={(e) =>
                    setFormState((f) => ({ ...f, time_limit: e.target.value }))
                  }
                  min="0"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  0 = بدون حد زمني
                </p>
              </div>
            </div>
          )}

          {/* Assignment Note */}
          {!isExam && (
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800 rounded-xl p-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <div>
                  <h6 className="font-medium text-green-800 dark:text-green-300">
                    ملاحظة حول الواجبات
                  </h6>
                  <p className="text-sm text-green-700 dark:text-green-400">
                    الواجبات لا تحتاج لدرجة نجاح أو وقت محدد. كل سؤال يحصل على
                    درجة واحدة تلقائياً.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="submit"
              className={`flex-1 ${
                isExam
                  ? "bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700"
                  : "bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
              } text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2`}
              disabled={loading}
            >
              {loading ? (
                <>
                  <svg
                    className="w-5 h-5 animate-spin"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    />
                  </svg>
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  حفظ التعديلات
                </>
              )}
            </button>
            <button
              type="button"
              className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-xl font-medium hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200"
              onClick={onClose}
            >
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </Modal>
  );
}
