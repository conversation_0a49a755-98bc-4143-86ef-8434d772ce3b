"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { ChevronLeft, Play, BookOpen, Users, Award } from "lucide-react";

export default function HeroSection() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <section className="relative min-h-screen py-10 flex items-center justify-center overflow-hidden bg-gradient-to-br from-blue-50 via-purple-50 to-indigo-100 dark:from-gray-900 dark:via-purple-900 dark:to-indigo-900">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float"></div>
        <div
          className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float"
          style={{ animationDelay: "2s" }}
        ></div>
        <div
          className="absolute top-40 left-1/2 w-60 h-60 bg-indigo-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float"
          style={{ animationDelay: "4s" }}
        ></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Text Content */}
          <div
            className={`text-center lg:text-right space-y-8 ${
              isVisible ? "animate-slide-in-right" : "opacity-0"
            }`}
          >
            <div className="space-y-4">
              <h1 className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-gray-900 dark:text-white leading-tight mt-5 md:mt-0">
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent ">
                  منصتك لتعلّم
                </span>
                <br />
                <span className="text-gray-800 dark:text-gray-200">أي شيء</span>
              </h1>
              <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto lg:mx-0">
                مع أفضل المدرّسين في الوطن العربي
              </p>
              <p className="text-lg text-gray-500 dark:text-gray-400 max-w-xl mx-auto lg:mx-0">
                انضم إلى آلاف الطلاب والمدرسين في رحلة التعلم والتعليم الرقمي
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Link
                href="/courses"
                className="group relative inline-flex items-center px-8 py-4 text-lg font-bold text-white bg-gradient-to-r from-blue-600 to-purple-600 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-blue-300"
              >
                <BookOpen className="w-6 h-6 ml-3" />
                <span>استكشف الكورسات</span>
                <ChevronLeft className="w-5 h-5 mr-3 transform group-hover:-translate-x-1 transition-transform duration-300" />
              </Link>

              <Link
                href="/areYouInstructor"
                className="group relative inline-flex items-center px-8 py-4 text-lg font-bold text-blue-600 dark:text-blue-400 bg-white dark:bg-gray-800 border-2 border-blue-600 dark:border-blue-400 rounded-full shadow-lg hover:shadow-xl hover:bg-blue-50 dark:hover:bg-gray-700 transform hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-blue-300"
              >
                <Users className="w-6 h-6 ml-3" />
                <span>انضم كمدرّس</span>
                <ChevronLeft className="w-5 h-5 mr-3 transform group-hover:-translate-x-1 transition-transform duration-300" />
              </Link>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-6 pt-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                  1000+
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  طالب نشط
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 dark:text-purple-400">
                  200+
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  كورس متاح
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-indigo-600 dark:text-indigo-400">
                  50+
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  مدرس خبير
                </div>
              </div>
            </div>
          </div>

          {/* Hero Image/Illustration */}
          <div
            className={`relative ${
              isVisible ? "animate-slide-in-left" : "opacity-0"
            }`}
            style={{ animationDelay: "0.3s" }}
          >
            <div className="relative">
              {/* Main illustration container */}
              <div className="relative bg-white dark:bg-gray-800 rounded-3xl shadow-2xl p-8 transform rotate-3 hover:rotate-0 transition-transform duration-500">
                <div className="aspect-square bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-2xl flex items-center justify-center">
                  {/* Placeholder for illustration - you can replace with actual image */}
                  <div className="text-center space-y-4">
                    <div className="w-32 h-32 mx-auto bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                      <Play className="w-16 h-16 text-white" />
                    </div>
                    <div className="space-y-2">
                      <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mx-auto"></div>
                      <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2 mx-auto"></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating elements */}
              <div
                className="absolute -top-4 -right-4 w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg animate-bounce-in"
                style={{ animationDelay: "1s" }}
              >
                <Award className="w-8 h-8 text-white" />
              </div>

              <div
                className="absolute -bottom-4 -left-4 w-20 h-20 bg-green-400 rounded-full flex items-center justify-center shadow-lg animate-bounce-in"
                style={{ animationDelay: "1.5s" }}
              >
                <BookOpen className="w-10 h-10 text-white" />
              </div>

              <div
                className="absolute top-1/2 -left-8 w-12 h-12 bg-red-400 rounded-full flex items-center justify-center shadow-lg animate-bounce-in"
                style={{ animationDelay: "2s" }}
              >
                <Users className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-gray-400 dark:border-gray-600 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-gray-400 dark:bg-gray-600 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
}
