"use client";

import { useSearchParams, useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { use } from 'react';

export default function PaymentSuccessPage({ params }) {
    const courseId = use(Promise.resolve(params.id));
    const searchParams = useSearchParams();
    const router = useRouter();
    const shouldClose = searchParams.get('close') === 'true';

    useEffect(() => {
        if (!courseId) return;
        
        // إرسال رسالة نجاح الدفع للصفحة الرئيسية
        if (window.opener) {
            window.opener.postMessage({ type: 'PAYMENT_SUCCESS', courseId }, '*');
            if (shouldClose) {
                setTimeout(() => {
                    window.close();
                }, 1000);
            }
        } else {
            toast.success('تم الدفع بنجاح!');
            router.push(`/course/${courseId}`);
        }
    }, [courseId, shouldClose, router]);

    if (!courseId) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
                <div className="text-center">
                    <p className="text-gray-600 dark:text-gray-400">جاري التحميل...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
            <div className="max-w-md w-full space-y-8 p-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
                <div className="text-center">
                    <h2 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
                        تم الدفع بنجاح!
                    </h2>
                    <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                        {shouldClose ? 'سيتم إغلاق هذه النافذة تلقائياً...' : 'سيتم توجيهك إلى صفحة الدورة...'}
                    </p>
                </div>
            </div>
        </div>
    );
} 