/*
حماية الفيديوهات المحمية - زكي الخولي
CSS لمنع التلاعب بالـ watermark والحماية
ملاحظة: هذا الملف يجب أن يكون في public/styles/
*/

/* منع التحديد والنسخ */
.video-protected {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

/* منع السحب والإفلات */
.video-protected * {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
  pointer-events: none;
}

/* السماح بالتحكم في المشغل فقط */
.video-protected .plyr__controls {
  pointer-events: auto;
}

.video-protected .plyr__control {
  pointer-events: auto;
}

/* حماية الـ watermark من التعديل */
.video-protected [class*="watermark"],
.video-protected [class*="absolute"] {
  pointer-events: none !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  position: absolute !important;
  z-index: 9999 !important;
}

/* منع inspect element على الفيديو */
.video-protected video {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* منع screenshot على بعض المتصفحات */
.video-protected {
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}

/* حماية إضافية للـ watermark */
.watermark-protection {
  position: absolute !important;
  pointer-events: none !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  z-index: 999999 !important;
  font-family: Arial, sans-serif !important;
  font-weight: bold !important;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.8) !important;
}

/* منع التلاعب بـ CSS */
.watermark-protection::before,
.watermark-protection::after {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  pointer-events: none !important;
  z-index: 1 !important;
}

/* حماية من DevTools */
@media screen {
  .video-protected {
    -webkit-user-modify: read-only !important;
  }
}

/* منع Print Screen على بعض المتصفحات */
@media print {
  .video-protected {
    display: none !important;
  }
}

/* حماية إضافية للفيديو */
.video-protected video {
  object-fit: cover;
  background: #000;
}

/* منع التحديد بالماوس */
.video-protected::selection {
  background: transparent;
}

.video-protected::-moz-selection {
  background: transparent;
}

/* حماية من copy/paste */
.video-protected {
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
}

/* منع التحكم بالكيبورد في بعض الحالات */
.video-protected video:focus {
  outline: none;
}

/* حماية الـ iframe للفيديوهات المحمية */
.video-protected iframe {
  pointer-events: auto;
  border: none;
  background: #000;
}

/* Watermark styles */
.dynamic-watermark {
  position: absolute !important;
  pointer-events: none !important;
  user-select: none !important;
  z-index: 999999 !important;
  font-family: 'Arial Black', Arial, sans-serif !important;
  font-weight: 900 !important;
  text-shadow: 
    2px 2px 4px rgba(0,0,0,0.8),
    -1px -1px 2px rgba(0,0,0,0.8),
    1px -1px 2px rgba(0,0,0,0.8),
    -1px 1px 2px rgba(0,0,0,0.8) !important;
  letter-spacing: 1px !important;
}

/* Multiple watermark positions */
.watermark-top-right {
  top: 20px !important;
  right: 20px !important;
}

.watermark-bottom-left {
  bottom: 60px !important;
  left: 20px !important;
}

.watermark-center {
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) rotate(15deg) !important;
}

/* Anti-tamper protection */
.video-protected * {
  -webkit-user-modify: read-only !important;
}

/* Disable context menu completely */
.video-protected,
.video-protected * {
  -webkit-context-menu: none !important;
  context-menu: none !important;
}

/* Additional security for mobile */
@media (max-width: 768px) {
  .video-protected {
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    -webkit-tap-highlight-color: transparent !important;
  }
}

/* Prevent video download */
.video-protected video::-webkit-media-controls-download-button {
  display: none !important;
}

.video-protected video::-webkit-media-controls-fullscreen-button {
  display: none !important;
}

/* Hide video controls that allow download */
.video-protected video[controls]::-webkit-media-controls-download-button {
  display: none !important;
}

/* Final protection layer */
.video-protection-overlay {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  pointer-events: none !important;
  z-index: 999998 !important;
  background: transparent !important;
}
