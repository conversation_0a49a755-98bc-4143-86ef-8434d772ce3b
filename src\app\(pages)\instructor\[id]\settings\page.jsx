"use client";
import React, { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import Cookies from "js-cookie";
import { fetchInstructorProfile } from "../../../../../services/instructor";
import { useSelector, useDispatch } from "react-redux";
import { selectCurrentUser, updateUser } from "../../../../../store/authSlice";
import {
  userDataChange,
  changeInstructorPassword,
} from "../../../../../services/anyUserDataChange";
import InstructorPayment from "@/app/_Components/InstructorPayment/InstructorPayment";

export default function InstructorSettings() {
  const { id } = useParams();
  const router = useRouter();
  const dispatch = useDispatch(); // إضافة dispatch لتحديث Redux store - zaki alkholy
  const user = useSelector(selectCurrentUser);
  const [form, setForm] = useState({
    username: "",
    email: "",
    bio: "",
    date_of_birth: "",
    first_name: "",
    last_name: "",
    phone_number: "",
    profile_image: null,
  });
  const [passwordForm, setPasswordForm] = useState({
    old_password: "",
    new_password: "",
    confirm_password: "",
  });
  const [isInstructor, setIsInstructor] = useState(false);
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [passwordError, setPasswordError] = useState(null);
  const [passwordSuccess, setPasswordSuccess] = useState(null);
  const [instructorProfileId, setInstructorProfileId] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  useEffect(() => {
    if (user && user.id && String(user.id) !== String(id)) {
      router.replace("/Notfound");
      return;
    }
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      const token = Cookies.get("authToken");
      if (!token) {
        setError("يرجى تسجيل الدخول");
        setLoading(false);
        return;
      }
      try {
        const res = await fetchInstructorProfile(id, token);
        console.log("Fetched instructor profile:", res);
        setForm({
          username: res.username || "",
          email: res.email || "",
          bio: res.bio || "",
          phone_number: res.phone_number || "",
          date_of_birth: res.date_of_birth || "",
          first_name: res.first_name || "",
          last_name: res.last_name || "",
          profile_image: null, // Initialize as null for file input
        });
        setImagePreview(res.profile_image || null); // Set preview to fetched image URL
        setIsInstructor(res.is_instructor || false);

        setInstructorProfileId(res.instructor_profile_id || null);
      } catch (err) {
        setError("حدث خطأ أثناء جلب البيانات");
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [id, user]);

  const handleChange = (e) => {
    const { name, value, files } = e.target;
    if (name === "profile_image" && files && files[0]) {
      setForm({ ...form, profile_image: files[0] });
      setImagePreview(URL.createObjectURL(files[0]));
    } else {
      setForm({ ...form, [name]: value });
    }
  };

  const handlePasswordChange = (e) => {
    setPasswordForm({ ...passwordForm, [e.target.name]: e.target.value });
  };

  const handlePasswordSubmit = async (e) => {
    e.preventDefault();
    setPasswordError(null);
    setPasswordSuccess(null);
    if (passwordForm.new_password !== passwordForm.confirm_password) {
      setPasswordError("كلمتا المرور الجديدتان غير متطابقتين");
      return;
    }
    if (passwordForm.new_password.length < 8) {
      setPasswordError("يجب أن تكون كلمة المرور الجديدة 8 أحرف على الأقل");
      return;
    }
    const token = Cookies.get("authToken");
    if (!token) {
      setPasswordError("يرجى تسجيل الدخول");
      return;
    }
    try {
      await changeInstructorPassword(
        token,
        passwordForm.old_password,
        passwordForm.new_password
      );
      setPasswordSuccess("تم تغيير كلمة المرور بنجاح");
      setPasswordForm({
        old_password: "",
        new_password: "",
        confirm_password: "",
      });
      setShowPasswordForm(false);
    } catch (err) {
      setPasswordError("حدث خطأ أثناء تغيير كلمة المرور");
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError(null);
    setSuccess(null);
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول");
      setSaving(false);
      return;
    }

    try {
      const formData = new FormData();
      formData.append("username", form.username);
      formData.append("email", form.email);
      formData.append("bio", form.bio);
      formData.append("date_of_birth", form.date_of_birth);
      formData.append("first_name", form.first_name);
      formData.append("last_name", form.last_name);
      formData.append("phone_number", form.phone_number);
      if (form.profile_image) {
        formData.append("profile_image", form.profile_image);
      }

      const updatedUser = await userDataChange(id, token, formData, true);

      // تحديث Redux store بالبيانات الجديدة - zaki alkholy
      console.log("Updated user data:", updatedUser);
      dispatch(updateUser(updatedUser));

      setSuccess("تم حفظ التعديلات بنجاح");
      setTimeout(() => {
        router.push(`/instructor/dashboard/`);
      }, 2000);
    } catch (err) {
      const errorData = err.response?.data;

      setError(
        errorData?.email?.[0] ||
          errorData?.username?.[0] ||
          errorData?.message ||
          errorData?.detail ||
          err.message ||
          "فشل التعديل. يرجى المحاولة مرة أخرى."
      );
    } finally {
      setSaving(false);
    }
  };
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="text-center animate-pulse">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-primary to-accent rounded-full mb-4 shadow-lg">
            <i className="fas fa-spinner fa-spin text-white text-2xl"></i>
          </div>
          <p className="text-lg font-medium text-gray-600 dark:text-gray-400 font-arabic">
            جاري التحميل...
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 pt-16">
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          {/* Header Section */}
          <div className="text-center mb-12 animate-fade-in">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-primary to-accent rounded-full mb-6 shadow-lg">
              <i className="fas fa-user-cog text-white text-2xl"></i>
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent mb-4 font-arabic">
              إعدادات الحساب
            </h1>
            <p className="text-gray-600 dark:text-gray-400 text-lg max-w-2xl mx-auto">
              قم بتحديث معلوماتك الشخصية وإعدادات حسابك
            </p>
          </div>
          {/* Alert Messages */}
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-400 p-4 rounded-xl mb-6 flex items-center animate-slide-in-left">
              <i className="fas fa-exclamation-circle mr-3 text-lg"></i>
              {error}
            </div>
          )}
          {success && (
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 text-green-700 dark:text-green-400 p-4 rounded-xl mb-6 flex items-center animate-slide-in-left">
              <i className="fas fa-check-circle mr-3 text-lg"></i>
              {success}
            </div>
          )}

          {/* Main Form Container */}
          <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-gray-200/50 dark:border-gray-700/50 p-8 animate-slide-up">
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* معلومات الحساب الأساسية */}
              <div className="space-y-6">
                <div className="flex items-center space-x-4 space-x-reverse mb-6">
                  <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-primary to-accent rounded-full">
                    <i className="fas fa-user text-white"></i>
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 dark:text-white font-arabic">
                    المعلومات الأساسية
                  </h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* اسم المستخدم */}
                  <div className="space-y-2 animate-slide-in-right">
                    <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                      <i className="fas fa-at text-primary mr-3 text-lg ml-2"></i>
                      اسم المستخدم
                    </label>
                    <div className="relative group">
                      <input
                        type="text"
                        name="username"
                        value={form.username}
                        onChange={handleChange}
                        placeholder="أدخل اسم المستخدم"
                        className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md font-arabic"
                      />
                      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300">
                        <i className="fas fa-user-circle"></i>
                      </div>
                    </div>
                  </div>

                  {/* البريد الإلكتروني */}
                  <div className="space-y-2 animate-slide-in-left">
                    <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                      <i className="fas fa-envelope text-primary mr-3 text-lg ml-2"></i>
                      البريد الإلكتروني
                    </label>
                    <div className="relative group">
                      <input
                        type="email"
                        name="email"
                        value={form.email}
                        onChange={handleChange}
                        placeholder="أدخل البريد الإلكتروني"
                        className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md font-arabic"
                      />
                      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300">
                        <i className="fas fa-mail-bulk"></i>
                      </div>
                    </div>
                  </div>

                  {/* الاسم الأول */}
                  <div className="space-y-2 animate-slide-in-right">
                    <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                      <i className="fas fa-id-card text-primary mr-3 text-lg ml-2"></i>
                      الاسم الأول
                    </label>
                    <div className="relative group">
                      <input
                        type="text"
                        name="first_name"
                        value={form.first_name}
                        onChange={handleChange}
                        placeholder="أدخل الاسم الأول"
                        className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md font-arabic"
                      />
                      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300">
                        <i className="fas fa-user"></i>
                      </div>
                    </div>
                  </div>

                  {/* الاسم الثاني */}
                  <div className="space-y-2 animate-slide-in-left">
                    <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                      <i className="fas fa-id-card text-primary mr-3 text-lg ml-2"></i>
                      الاسم الثاني
                    </label>
                    <div className="relative group">
                      <input
                        type="text"
                        name="last_name"
                        value={form.last_name}
                        onChange={handleChange}
                        placeholder="أدخل الاسم الثاني"
                        className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md font-arabic"
                      />
                      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300">
                        <i className="fas fa-user"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {/* معلومات إضافية */}
              <div className="space-y-6">
                <div className="flex items-center space-x-4 space-x-reverse mb-6">
                  <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-accent to-primary rounded-full">
                    <i className="fas fa-info-circle text-white ml-2"></i>
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 dark:text-white font-arabic">
                    المعلومات الإضافية
                  </h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* رقم الهاتف */}
                  <div className="space-y-2 animate-slide-in-right">
                    <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                      <i className="fas fa-phone text-primary mr-3 text-lg ml-2"></i>
                      رقم الهاتف
                    </label>
                    <div className="relative group">
                      <input
                        type="tel"
                        name="phone_number"
                        value={form.phone_number}
                        onChange={handleChange}
                        placeholder="أدخل رقم الهاتف"
                        className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md font-arabic"
                      />
                      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300">
                        <i className="fas fa-mobile-alt"></i>
                      </div>
                    </div>
                  </div>

                  {/* تاريخ الميلاد */}
                  <div className="space-y-2 animate-slide-in-left">
                    <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                      <i className="fas fa-calendar-alt text-primary mr-3 text-lg ml-2"></i>
                      تاريخ الميلاد
                    </label>
                    <div className="relative group">
                      <input
                        type="date"
                        name="date_of_birth"
                        value={form.date_of_birth}
                        onChange={handleChange}
                        className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md font-arabic"
                      />
                      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300">
                        <i className="fas fa-birthday-cake"></i>
                      </div>
                    </div>
                  </div>
                </div>

                {/* النبذة التعريفية */}
                <div className="space-y-2 animate-slide-in-right">
                  <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                    <i className="fas fa-quote-left text-primary mr-3 text-lg ml-2"></i>
                    النبذة التعريفية
                  </label>
                  <div className="relative group">
                    <textarea
                      name="bio"
                      value={form.bio}
                      onChange={handleChange}
                      placeholder="اكتب نبذة تعريفية عنك وخبراتك التعليمية"
                      rows={4}
                      className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md resize-none font-arabic"
                    />
                    <div className="absolute right-4 top-4 text-gray-400 group-focus-within:text-primary transition-colors duration-300">
                      <i className="fas fa-pen"></i>
                    </div>
                  </div>
                </div>
              </div>
              {/* الصورة الشخصية */}
              <div className="space-y-6">
                <div className="flex items-center space-x-4 space-x-reverse mb-6">
                  <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full">
                    <i className="fas fa-camera text-white "></i>
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 dark:text-white font-arabic">
                    الصورة الشخصية
                  </h3>
                </div>

                <div className="space-y-4 animate-slide-in-left">
                  <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                    <i className="fas fa-image text-primary mr-3 text-lg ml-2"></i>
                    صورة الملف الشخصي
                  </label>

                  {/* معاينة الصورة */}
                  {imagePreview && (
                    <div className="flex justify-center mb-6">
                      <div className="relative group">
                        <img
                          src={imagePreview}
                          alt="Profile Preview"
                          className="w-32 h-32 object-cover rounded-full border-4 border-white dark:border-gray-700 shadow-xl group-hover:shadow-2xl transition-all duration-300"
                        />
                        <div className="absolute inset-0 bg-black/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                          <i className="fas fa-edit text-white text-xl"></i>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* رفع الصورة */}
                  <div className="relative group">
                    <input
                      type="file"
                      name="profile_image"
                      onChange={handleChange}
                      accept="image/*"
                      className="w-full px-4 py-4 bg-white dark:bg-gray-700 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-400 dark:hover:border-gray-500 shadow-sm hover:shadow-md file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-primary file:text-white hover:file:bg-primary-dark file:transition-colors file:duration-300"
                    />
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300 pointer-events-none">
                      <i className="fas fa-upload"></i>
                    </div>
                  </div>
                </div>
              </div>

              {/* زر الحفظ */}
              <div className="pt-6 animate-slide-in-right">
                <button
                  type="submit"
                  disabled={saving}
                  className="w-full bg-gradient-to-r from-primary to-accent hover:from-primary-dark hover:to-accent-light text-white py-4 px-8 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-[1.02] hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-3 space-x-reverse font-arabic shadow-lg"
                >
                  {saving ? (
                    <>
                      <i className="fas fa-spinner fa-spin text-xl"></i>
                      <span>جارٍ الحفظ...</span>
                    </>
                  ) : (
                    <>
                      <i className="fas fa-save text-xl"></i>
                      <span>حفظ التعديلات</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
          {/* قسم تغيير كلمة المرور */}
          <div className="mt-12">
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-gray-200/50 dark:border-gray-700/50 p-8 animate-slide-up">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-full">
                    <i className="fas fa-lock text-white"></i>
                  </div>
                  <h2 className="text-xl font-bold text-gray-800 dark:text-white font-arabic">
                    تغيير كلمة المرور
                  </h2>
                </div>
                <button
                  type="button"
                  onClick={() => setShowPasswordForm(!showPasswordForm)}
                  className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-primary/10 dark:bg-primary/20 text-primary hover:bg-primary/20 dark:hover:bg-primary/30 rounded-xl transition-all duration-300 font-arabic"
                >
                  <i
                    className={`fas ${
                      showPasswordForm ? "fa-times" : "fa-key"
                    } text-sm`}
                  ></i>
                  <span>
                    {showPasswordForm ? "إلغاء" : "تغيير كلمة المرور"}
                  </span>
                </button>
              </div>
              {showPasswordForm && (
                <div className="space-y-6 animate-slide-in-left">
                  {/* رسائل التنبيه */}
                  {passwordError && (
                    <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-400 p-4 rounded-xl flex items-center">
                      <i className="fas fa-exclamation-triangle mr-3 text-lg"></i>
                      {passwordError}
                    </div>
                  )}
                  {passwordSuccess && (
                    <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 text-green-700 dark:text-green-400 p-4 rounded-xl flex items-center">
                      <i className="fas fa-check-circle mr-3 text-lg"></i>
                      {passwordSuccess}
                    </div>
                  )}

                  <form onSubmit={handlePasswordSubmit} className="space-y-6">
                    {/* كلمة المرور القديمة */}
                    <div className="space-y-2">
                      <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                        <i className="fas fa-key text-primary mr-3 text-lg"></i>
                        كلمة المرور القديمة
                      </label>
                      <div className="relative group">
                        <input
                          type="password"
                          name="old_password"
                          value={passwordForm.old_password}
                          onChange={handlePasswordChange}
                          placeholder="أدخل كلمة المرور الحالية"
                          required
                          className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md font-arabic"
                        />
                        <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300">
                          <i className="fas fa-lock"></i>
                        </div>
                      </div>
                    </div>

                    {/* كلمة المرور الجديدة */}
                    <div className="space-y-2">
                      <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                        <i className="fas fa-unlock-alt text-primary mr-3 text-lg"></i>
                        كلمة المرور الجديدة
                      </label>
                      <div className="relative group">
                        <input
                          type="password"
                          name="new_password"
                          value={passwordForm.new_password}
                          onChange={handlePasswordChange}
                          placeholder="أدخل كلمة المرور الجديدة (8 أحرف على الأقل)"
                          required
                          className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md font-arabic"
                        />
                        <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300">
                          <i className="fas fa-shield-alt"></i>
                        </div>
                      </div>
                    </div>

                    {/* تأكيد كلمة المرور */}
                    <div className="space-y-2">
                      <label className="flex items-center text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 font-arabic">
                        <i className="fas fa-check-double text-primary mr-3 text-lg"></i>
                        تأكيد كلمة المرور الجديدة
                      </label>
                      <div className="relative group">
                        <input
                          type="password"
                          name="confirm_password"
                          value={passwordForm.confirm_password}
                          onChange={handlePasswordChange}
                          placeholder="أعد إدخال كلمة المرور الجديدة"
                          required
                          className="w-full px-4 py-4 pr-12 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary dark:focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md font-arabic"
                        />
                        <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary transition-colors duration-300">
                          <i className="fas fa-check-circle"></i>
                        </div>
                      </div>
                    </div>

                    {/* زر تغيير كلمة المرور */}
                    <button
                      type="submit"
                      className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white py-4 px-8 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-[1.02] hover:shadow-xl flex items-center justify-center space-x-3 space-x-reverse font-arabic shadow-lg"
                    >
                      <i className="fas fa-key text-xl"></i>
                      <span>تغيير كلمة المرور</span>
                    </button>
                  </form>
                </div>
              )}
            </div>
          </div>

          {/* قسم معلومات الدفع */}
          <div className="mt-12">
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-gray-200/50 dark:border-gray-700/50 p-8 animate-slide-up">
              <div className="flex items-center space-x-4 space-x-reverse mb-6">
                <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full">
                  <i className="fas fa-credit-card text-white"></i>
                </div>
                <h2 className="text-xl font-bold text-gray-800 dark:text-white font-arabic">
                  معلومات الدفع
                </h2>
              </div>
              <InstructorPayment />
            </div>
          </div>

          {/* ملاحظة أمان */}
          <div className="mt-8 text-center">
            <div className="inline-flex items-center space-x-2 space-x-reverse text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800/50 px-4 py-3 rounded-xl">
              <i className="fas fa-shield-alt text-primary"></i>
              <span>جميع بياناتك محمية ومشفرة بأعلى معايير الأمان</span>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
