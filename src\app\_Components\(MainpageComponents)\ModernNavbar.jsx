"use client";
import React, { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useSelector, useDispatch } from "react-redux";
import SearchForInstructor from "./SearchForInstructor";
import {
  selectCurrentUser,
  selectIsAuthenticated,
  logout,
} from "../../../store/authSlice";
import {
  Menu,
  X,
  BookOpen,
  Users,
  Info,
  LogIn,
  UserPlus,
  Search,
  Bell,
  Sun,
  Moon,
  Globe,
  ChevronDown,
  Settings,
} from "lucide-react";
import Cookies from "js-cookie";
export default function ModernNavbar() {
  const token = Cookies.get("authToken");
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isDark, setIsDark] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const router = useRouter();
  const dispatch = useDispatch();
  const user = useSelector(selectCurrentUser);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const toggleDarkMode = () => {
    setIsDark(!isDark);
    document.documentElement.classList.toggle("dark");
  };
  const handleLogout = () => {
    dispatch(logout());
    router.push("/login");
  };
  const navItems = [
    { name: "الرئيسية", href: "/", icon: null },
    isAuthenticated && user?.is_instructor
      ? { name: "لوحة التحكم", href: "/instructor/dashboard", icon: null }
      : null,
    // { name: "لوحة التحكمة", href: "/instructor/dashboard", icon: null },
    { name: "استعراض الكورسات", href: "/courses", icon: BookOpen },

    isAuthenticated && !user?.is_instructor
      ? ""
      : { name: "كيف تعمل المنصة", href: "/howItWorks", icon: Info },
    isAuthenticated
      ? ""
      : { name: "انضم كمدرّس", href: "/signup", icon: Users },
    isAuthenticated && !user?.is_instructor
      ? {
          name: "الاعدادات",
          href: `/student/${user?.id}/settings`,
          icon: Settings,
        }
      : "",
    isAuthenticated && !user?.is_instructor
      ? {
          name: "الصفحة الشخصية",
          href: `/student/${user?.id}/`,
          icon: Settings,
        }
      : "",
  ].filter(Boolean);

  return (
    <>
      <nav
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300  ${
          isScrolled
            ? "bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-lg"
            : "bg-white dark:bg-black"
        }`}
      >
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16 lg:h-20">
            {/* Logo */}
            <Link
              href="/"
              className="flex items-center space-x-3 space-x-reverse"
            >
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <BookOpen className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                مُعَلِّمِيّ
              </span>
            </Link>

            {/* Desktop Navigation */}

            <div className="hidden lg:flex items-center space-x-8 space-x-reverse">
              {navItems.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="group relative flex items-center space-x-2 space-x-reverse px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300"
                  >
                    {Icon && <Icon className="w-4 h-4" />}
                    <span className="font-medium">{item.name}</span>
                    <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
                  </Link>
                );
              })}
            </div>

            {/* Right Side Actions */}
            <div className="flex items-center space-x-4 space-x-reverse">
              {/* Search Button */}
              <button
                onClick={() => setShowSearch(true)}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300"
              >
                <Search className="w-5 h-5" />
              </button>

              {/* Dark Mode Toggle */}
              <button
                onClick={toggleDarkMode}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300"
              >
                {isDark ? (
                  <Sun className="w-5 h-5" />
                ) : (
                  <Moon className="w-5 h-5" />
                )}
              </button>

              {/* Language Selector */}
              <div className="relative group">
                <button className="flex items-center space-x-1 space-x-reverse p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300">
                  <Globe className="w-5 h-5" />
                  <ChevronDown className="w-3 h-3" />
                </button>
                <div className="absolute top-full right-0 mt-2 w-32 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                  <button className="w-full text-right px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-t-lg">
                    العربية
                  </button>
                  <button className="w-full text-right px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-b-lg">
                    English
                  </button>
                </div>
              </div>

              {/* Auth Buttons - Desktop */}
              <div className="hidden lg:flex items-center space-x-3 space-x-reverse">
                {isAuthenticated && user ? (
                  <Link
                    href="/login"
                    onClick={handleLogout}
                    className="flex items-center space-x-2 space-x-reverse px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300"
                  >
                    <LogIn className="w-4 h-4" />
                    <span>تسجيل الخروج</span>
                  </Link>
                ) : (
                  <Link
                    href="/login"
                    className="flex items-center space-x-2 space-x-reverse px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full hover:shadow-lg transform hover:scale-105 transition-all duration-300"
                  >
                    <UserPlus className="w-4 h-4" />
                    <span>التسجيل</span>
                  </Link>
                )}
              </div>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsOpen(!isOpen)}
                className="lg:hidden p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300"
              >
                {isOpen ? (
                  <X className="w-6 h-6" />
                ) : (
                  <Menu className="w-6 h-6" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <div
          className={`lg:hidden transition-all duration-300 ${
            isOpen
              ? "max-h-screen opacity-100"
              : "max-h-0 opacity-0 overflow-hidden"
          }`}
        >
          <div className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
            <div className="container mx-auto px-4 py-4">
              <div className="space-y-4">
                {navItems.map((item) => {
                  const Icon = item.icon;
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      onClick={() => setIsOpen(false)}
                      className="flex items-center space-x-3 space-x-reverse p-3 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-all duration-300"
                    >
                      {Icon && <Icon className="w-5 h-5" />}
                      <span className="font-medium">{item.name}</span>
                    </Link>
                  );
                })}

                {/* Mobile Auth Buttons */}
                <div className="pt-4 border-t border-gray-200 dark:border-gray-700 space-y-3">
                  {isAuthenticated && user ? (
                    <Link
                      href="/login"
                      onClick={handleLogout}
                      className="flex items-center space-x-3 space-x-reverse p-3 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-all duration-300"
                    >
                      <LogIn className="w-5 h-5" />
                      <span className="font-medium">تسجيل الخروج</span>
                    </Link>
                  ) : (
                    <Link
                      href="/login"
                      onClick={() => setIsOpen(false)}
                      className="flex items-center justify-center space-x-2 space-x-reverse p-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-medium"
                    >
                      <UserPlus className="w-5 h-5" />
                      <span>التسجيل</span>
                    </Link>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <SearchForInstructor
        showSearch={showSearch}
        setShowSearch={setShowSearch}
      />
    </>
  );
}
