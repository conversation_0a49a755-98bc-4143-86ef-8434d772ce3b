"use client";
import React, { useEffect, useState } from "react";
import axios from "axios";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import LoadingSpinner from "./../LoadingSpinner/LoadingSpinner";
import {
  fetchNotifications,
  markNotificationAsRead,
} from "../../../services/notifications";
export default function DashboardNotification() {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const router = useRouter();

  useEffect(() => {
    getNotifications();
  }, []);

  async function getNotifications() {
    const token = Cookies.get("authToken");
    if (!token) return setError("لا يوجد توكن");

    setLoading(true);
    try {
      const data = await fetchNotifications(token);
      console.log("Notifications", data);
      setNotifications(data);
    } catch (e) {
      setError("فشل جلب الإشعارات");
    } finally {
      setLoading(false);
    }
  }

  async function handleMarkAsRead(id, link) {
    const token = Cookies.get("authToken");
    if (!token) {
      alert("لا يوجد توكن تسجيل دخول");
      return;
    }

    try {
      const result = await markNotificationAsRead(id, token);
      if (result && result.is_read) {
        router.push(link || "/instructor/dashboard");
      } else {
        alert("لم يتم تأكيد قراءة الإشعار");
      }
    } catch (e) {
      alert("فشل تحديث حالة الإشعار");
    }
  }

  return (
    <div className="">
      {loading ? (
        <LoadingSpinner />
      ) : error ? (
        <p className="text-red-500 dark:text-red-400">{error}</p>
      ) : notifications.length === 0 ? (
        <p className="text-foreground">لا توجد إشعارات</p>
      ) : (
        <ul className="space-y-4 px-2">
          {notifications.map((n) => (
            <li
              key={n.id}
              className={`p-2 rounded shadow border flex items-center justify-between ${
                n.is_read
                  ? "bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                  : "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700"
              }`}
            >
              <span className="text-foreground text-gray-700 dark:text-gray-300">
                {n.message}
              </span>
              <button
                className={`mx-2 px-3 py-1 rounded text-sm transition-colors ${
                  n.is_read
                    ? "bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300"
                    : "bg-blue-500 hover:bg-blue-600 text-white"
                }`}
                onClick={() => handleMarkAsRead(n.id, n.link)}
                disabled={n.is_read}
              >
                {n.is_read ? "مقروء" : "عرض"}
              </button>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}
