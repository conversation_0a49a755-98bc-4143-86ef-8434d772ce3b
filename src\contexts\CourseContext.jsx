"use client";
import React, { createContext, useContext } from 'react';

// إنشاء الـ Context
const CourseContext = createContext();

// Hook لاستخدام الـ Context
export const useCourseContext = () => {
  const context = useContext(CourseContext);
  if (!context) {
    throw new Error('useCourseContext must be used within a CourseProvider');
  }
  return context;
};

// Provider Component
export const CourseProvider = ({ children, value }) => {
  return (
    <CourseContext.Provider value={value}>
      {children}
    </CourseContext.Provider>
  );
};

export default CourseContext;
