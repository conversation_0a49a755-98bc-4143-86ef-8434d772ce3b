{"name": "manasa", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@mui/styled-engine-sc": "^7.1.1", "@react-oauth/google": "^0.12.2", "@reduxjs/toolkit": "^2.0.1", "axios": "^1.6.2", "babel-plugin-styled-components": "^2.1.4", "cookies-next": "^4.0.0", "crypto-js": "^4.2.0", "formik": "^2.4.5", "framer-motion": "^12.23.0", "hls.js": "^1.6.4", "html2pdf.js": "^0.10.3", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^4.0.0", "libphonenumber-js": "^1.12.9", "lucide-react": "^0.525.0", "next": "^15.3.1", "next-auth": "^4.24.11", "plyr": "^3.7.8", "react": "^18.2.0", "react-calendar": "^6.0.0", "react-dom": "^18.2.0", "react-hook-form": "^7.56.1", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-image-crop": "^11.0.10", "react-redux": "^9.0.4", "recharts": "^2.15.3", "redux-persist": "^6.0.0", "styled-components": "^6.1.19", "universal-cookie": "^8.0.1"}, "devDependencies": {"@babel/preset-react": "^7.27.1", "@eslint/eslintrc": "^3.0.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "postcss": "^8.4.35", "tailwindcss": "^3.4.1"}}