import { useState, useEffect } from "react";
import Cookies from "js-cookie";
import {
  fetchInstructorCourse,
  fetchInstructorLessons
} from "../services/instructor";

export default function useCourseDetails(courseId) {
  const [courseData, setCourseData] = useState(null);
  const [lessons, setLessons] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setError(null);
      const token = Cookies.get("authToken");
      if (!token) {
        setError("يرجى تسجيل الدخول أولاً");
        setLoading(false);
        return;
      }
      try {
        const course = await fetchInstructorCourse(courseId, token);
        setCourseData(course);
        const lessonsList = await fetchInstructorLessons(courseId, token);
        setLessons(lessonsList);
      } catch (err) {
        setError("فشل في جلب بيانات الكورس أو الدروس");
      } finally {
        setLoading(false);
      }
    };
    loadData();
  }, [courseId]);

  return { courseData, setCourseData, lessons, setLessons, loading, error, setError };
}
