// خدمات الواجبات: جميع استدعاءات API الخاصة بالواجبات - zaki alkholy
import axios from "axios";
import { API_BASE_URL } from '../config/api';

// ===============================
// خدمات الواجبات للطلاب - zaki alkholy
// ===============================

/**
 * الحصول على واجبات الطالب - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {Object} filters - فلاتر البحث
 * @returns {Promise} قائمة الواجبات
 */
export async function fetchStudentAssignments(token, filters = {}) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/assignments/`, {
    headers,
    withCredentials: true,
    params: filters
  });
  
  return response.data;
}

/**
 * الحصول على تفاصيل واجب محدد - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} assignmentId - معرف الواجب
 * @returns {Promise} تفاصيل الواجب
 */
export async function fetchAssignmentDetails(token, assignmentId) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/assignments/${assignmentId}/`, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * تسليم واجب - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} assignmentId - معرف الواجب
 * @param {Object} submissionData - بيانات التسليم
 * @returns {Promise} نتيجة التسليم
 */
export async function submitAssignment(token, assignmentId, submissionData) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.post(`${API_BASE_URL}/api/assignment-submissions/`, {
    assignment: assignmentId,
    ...submissionData
  }, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * الحصول على تسليمات الطالب - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {Object} filters - فلاتر البحث
 * @returns {Promise} قائمة التسليمات
 */
export async function fetchStudentSubmissions(token, filters = {}) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/assignment-submissions/`, {
    headers,
    withCredentials: true,
    params: filters
  });
  
  return response.data;
}

// ===============================
// خدمات الواجبات للمعلمين - zaki alkholy
// ===============================

/**
 * إنشاء واجب جديد - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {Object} assignmentData - بيانات الواجب
 * @returns {Promise} الواجب المُنشأ
 */
export async function createAssignment(token, assignmentData) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.post(`${API_BASE_URL}/api/assignments/`, assignmentData, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * تحديث واجب - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} assignmentId - معرف الواجب
 * @param {Object} assignmentData - البيانات المحدثة
 * @returns {Promise} الواجب المحدث
 */
export async function updateAssignment(token, assignmentId, assignmentData) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.put(`${API_BASE_URL}/api/assignments/${assignmentId}/`, assignmentData, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * حذف واجب - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} assignmentId - معرف الواجب
 * @returns {Promise} نتيجة الحذف
 */
export async function deleteAssignment(token, assignmentId) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.delete(`${API_BASE_URL}/api/assignments/${assignmentId}/`, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * الحصول على تسليمات واجب للمعلم - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} assignmentId - معرف الواجب
 * @returns {Promise} قائمة التسليمات
 */
export async function fetchAssignmentSubmissions(token, assignmentId) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/assignment-submissions/`, {
    headers,
    withCredentials: true,
    params: { assignment: assignmentId }
  });
  
  return response.data;
}

/**
 * تقييم تسليم واجب - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} submissionId - معرف التسليم
 * @param {Object} gradeData - بيانات التقييم
 * @returns {Promise} التسليم المُقيم
 */
export async function gradeSubmission(token, submissionId, gradeData) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.patch(`${API_BASE_URL}/api/assignment-submissions/${submissionId}/`, gradeData, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * الحصول على تحليلات الواجبات للمعلم - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @returns {Promise} تحليلات الواجبات
 */
export async function fetchInstructorAssignmentAnalytics(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/instructor/assignments/analytics/`, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

// ===============================
// خدمات مساعدة - zaki alkholy
// ===============================

/**
 * رفع ملف للواجب - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {File} file - الملف المراد رفعه
 * @param {string} assignmentId - معرف الواجب
 * @returns {Promise} معلومات الملف المرفوع
 */
export async function uploadAssignmentFile(token, file, assignmentId) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('assignment', assignmentId);
  
  const headers = {
    Authorization: `Bearer ${token}`,
    Accept: "application/json",
  };
  
  const response = await axios.post(`${API_BASE_URL}/api/assignments/upload/`, formData, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * تحميل ملف واجب - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} fileId - معرف الملف
 * @returns {Promise} الملف
 */
export async function downloadAssignmentFile(token, fileId) {
  const headers = {
    Authorization: `Bearer ${token}`,
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/assignments/download/${fileId}/`, {
    headers,
    withCredentials: true,
    responseType: 'blob'
  });
  
  return response.data;
}
