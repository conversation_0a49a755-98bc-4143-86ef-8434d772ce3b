"use client";
import { useState } from "react";
import axios from "axios";
import { requestPasswordReset } from "../../services/anyUserDataChange";
import { ButtonLoader } from "@/components/common/UniversalLoader";
import Link from "next/link";

export default function ForgotPassword() {
  const [email, setEmail] = useState("");
  const [message, setMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      await requestPasswordReset(email);
      setMessage("تم إرسال رابط إعادة التعيين إلى بريدك.");
    } catch (err) {
      if (err.response && err.response.status === 404) {
        setMessage("هذا البريد غير مسجل.");
      } else {
        setMessage("حدث خطأ أثناء الإرسال. حاول مرة أخرى لاحقًا.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-foreground mb-2">
            إعادة تعيين كلمة المرور
          </h2>
          <p className="text-secondary">
            أدخل بريدك الإلكتروني وسنرسل لك رابط إعادة التعيين
          </p>
        </div>

        <form onSubmit={handleSubmit} className="mt-8 space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-foreground mb-2">
              البريد الإلكتروني
            </label>
            <input
              id="email"
              type="email"
              placeholder="أدخل بريدك الإلكتروني"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-foreground placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
            />
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50"
          >
            {isLoading ? <ButtonLoader size="small" /> : "إرسال رابط التعيين"}
          </button>

          {message && (
            <div className={`text-center p-3 rounded-md ${
              message.includes("تم إرسال")
                ? "bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300"
                : "bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300"
            }`}>
              {message}
            </div>
          )}

          <div className="text-center">
            <Link
              href="/login"
              className="text-sm text-primary hover:text-primary/80 font-medium"
            >
              العودة إلى تسجيل الدخول
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
}
