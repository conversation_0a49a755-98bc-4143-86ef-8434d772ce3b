// صفحة إدارة الواجبات التفاعلية للمعلمين - zaki alkholy
"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { API_BASE_URL } from "../../../../../config/api";
import {
  FileText,
  Plus,
  Calendar,
  Users,
  CheckCircle,
  Clock,
  AlertCircle,
  Edit,
  Eye,
  Download,
  Upload,
  Star,
} from "lucide-react";

// مكون لعرض بطاقة الواجب - zaki alkholy
const AssignmentCard = ({ assignment, onView, onEdit, onGrade }) => {
  const getStatusColor = (status) => {
    switch (status) {
      case "published":
        return "bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400";
      case "draft":
        return "bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400";
      case "overdue":
        return "bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400";
      default:
        return "bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400";
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case "file_upload":
        return "📁";
      case "text_submission":
        return "📝";
      case "project":
        return "🚀";
      case "presentation":
        return "📊";
      case "code_submission":
        return "💻";
      case "design":
        return "🎨";
      default:
        return "📄";
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-all duration-300"
    >
      {/* رأس البطاقة - zaki alkholy */}
      <div className="flex justify-between items-start mb-4">
        <div className="flex items-center">
          <span className="text-2xl mr-3">
            {getTypeIcon(assignment.assignment_type)}
          </span>
          <div>
            <h3 className="font-semibold text-lg text-foreground">
              {assignment.title}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {assignment.course_title}
            </p>
          </div>
        </div>
        <span
          className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(
            assignment.status
          )}`}
        >
          {assignment.is_published ? "منشور" : "مسودة"}
        </span>
      </div>

      {/* تفاصيل الواجب - zaki alkholy */}
      <div className="space-y-3 mb-4">
        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
          <Calendar className="w-4 h-4 mr-2" />
          <span>
            موعد التسليم:{" "}
            {new Date(assignment.due_date).toLocaleDateString("ar-EG")}
          </span>
        </div>

        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
          <Users className="w-4 h-4 mr-2" />
          <span>{assignment.submissions_count} تسليم</span>
        </div>

        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
          <Star className="w-4 h-4 mr-2" />
          <span>الدرجة الكاملة: {assignment.max_score}</span>
        </div>
      </div>

      {/* شريط التقدم - zaki alkholy */}
      <div className="mb-4">
        <div className="flex justify-between text-sm mb-1 text-foreground">
          <span>التسليمات</span>
          <span>{assignment.submissions_count}/50</span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className="bg-blue-500 dark:bg-blue-400 h-2 rounded-full"
            style={{ width: `${(assignment.submissions_count / 50) * 100}%` }}
          />
        </div>
      </div>

      {/* أزرار الإجراءات - zaki alkholy */}
      <div className="flex gap-2">
        <button
          onClick={() => onView(assignment)}
          className="flex-1 flex items-center justify-center py-2 px-3 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors text-sm"
        >
          <Eye className="w-4 h-4 mr-1" />
          عرض
        </button>
        <button
          onClick={() => onEdit(assignment)}
          className="flex-1 flex items-center justify-center py-2 px-3 bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors text-sm"
        >
          <Edit className="w-4 h-4 mr-1" />
          تعديل
        </button>
        <button
          onClick={() => onGrade(assignment)}
          className="flex-1 flex items-center justify-center py-2 px-3 bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors text-sm"
        >
          <CheckCircle className="w-4 h-4 mr-1" />
          تقييم
        </button>
      </div>
    </motion.div>
  );
};

// مكون لعرض إحصائيات سريعة - zaki alkholy
const QuickStats = ({ stats }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
              إجمالي الواجبات
            </p>
            <p className="text-2xl font-bold mt-1 text-foreground">
              {stats.total_assignments}
            </p>
          </div>
          <FileText className="w-8 h-8 text-blue-600 dark:text-blue-400" />
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        delay={0.1}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
              في انتظار التقييم
            </p>
            <p className="text-2xl font-bold mt-1 text-orange-600 dark:text-orange-400">
              {stats.pending_grading}
            </p>
          </div>
          <Clock className="w-8 h-8 text-orange-600 dark:text-orange-400" />
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        delay={0.2}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
              تم التقييم
            </p>
            <p className="text-2xl font-bold mt-1 text-green-600 dark:text-green-400">
              {stats.graded}
            </p>
          </div>
          <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        delay={0.3}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
              متوسط الدرجات
            </p>
            <p className="text-2xl font-bold mt-1 text-purple-600 dark:text-purple-400">
              {stats.average_score}%
            </p>
          </div>
          <Star className="w-8 h-8 text-purple-600 dark:text-purple-400" />
        </div>
      </motion.div>
    </div>
  );
};

// مكون لفلترة الواجبات - zaki alkholy
const AssignmentFilters = ({ filters, onFilterChange }) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
      <div className="flex flex-wrap gap-4 items-center">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            الحالة
          </label>
          <select
            value={filters.status}
            onChange={(e) =>
              onFilterChange({ ...filters, status: e.target.value })
            }
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-foreground rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">الكل</option>
            <option value="published">منشور</option>
            <option value="draft">مسودة</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            نوع الواجب
          </label>
          <select
            value={filters.type}
            onChange={(e) =>
              onFilterChange({ ...filters, type: e.target.value })
            }
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-foreground rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">الكل</option>
            <option value="file_upload">رفع ملف</option>
            <option value="text_submission">إرسال نص</option>
            <option value="project">مشروع</option>
            <option value="presentation">عرض تقديمي</option>
            <option value="code_submission">كود برمجي</option>
            <option value="design">تصميم</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            الدورة
          </label>
          <select
            value={filters.course}
            onChange={(e) =>
              onFilterChange({ ...filters, course: e.target.value })
            }
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-foreground rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">جميع الدورات</option>
            <option value="1">تعلم البرمجة</option>
            <option value="2">تصميم المواقع</option>
            <option value="3">قواعد البيانات</option>
          </select>
        </div>

        <div className="flex-1"></div>

        <button className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
          <Plus className="w-4 h-4 mr-2" />
          واجب جديد
        </button>
      </div>
    </div>
  );
};

// الصفحة الرئيسية لإدارة الواجبات - zaki alkholy
export default function InstructorAssignmentsPage() {
  const [assignments, setAssignments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    status: "",
    type: "",
    course: "",
  });

  // جلب بيانات الواجبات من API - zaki alkholy
  useEffect(() => {
    const fetchAssignments = async () => {
      try {
        const token = localStorage.getItem("access_token");
        const response = await fetch(`${API_BASE_URL}/api/assignments/`, {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        });

        if (response.ok) {
          const data = await response.json();
          setAssignments(data.results || data);
        }
      } catch (error) {
        console.error("خطأ في جلب بيانات الواجبات:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchAssignments();
  }, [filters]);

  // معالجة عرض الواجب - zaki alkholy
  const handleViewAssignment = (assignment) => {
    // التوجه لصفحة عرض تفاصيل الواجب
    window.location.href = `/instructor/assignments/${assignment.id}`;
  };

  // معالجة تعديل الواجب - zaki alkholy
  const handleEditAssignment = (assignment) => {
    // التوجه لصفحة تعديل الواجب
    window.location.href = `/instructor/assignments/${assignment.id}/edit`;
  };

  // معالجة تقييم الواجب - zaki alkholy
  const handleGradeAssignment = (assignment) => {
    // التوجه لصفحة تقييم التسليمات
    window.location.href = `/instructor/assignments/${assignment.id}/grade`;
  };

  // عرض شاشة التحميل - zaki alkholy
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // بيانات وهمية للعرض التوضيحي - zaki alkholy
  const mockAssignments =
    assignments.length > 0
      ? assignments
      : [
          {
            id: 1,
            title: "مشروع تطبيق ويب",
            course_title: "تعلم البرمجة",
            assignment_type: "project",
            due_date: "2024-12-25",
            max_score: 100,
            submissions_count: 23,
            is_published: true,
            status: "published",
          },
          {
            id: 2,
            title: "تصميم واجهة مستخدم",
            course_title: "تصميم المواقع",
            assignment_type: "design",
            due_date: "2024-12-20",
            max_score: 80,
            submissions_count: 18,
            is_published: true,
            status: "published",
          },
          {
            id: 3,
            title: "كتابة تقرير تقني",
            course_title: "قواعد البيانات",
            assignment_type: "text_submission",
            due_date: "2024-12-30",
            max_score: 50,
            submissions_count: 5,
            is_published: false,
            status: "draft",
          },
        ];

  const mockStats = {
    total_assignments: 12,
    pending_grading: 8,
    graded: 15,
    average_score: 78.5,
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* العنوان الرئيسي - zaki alkholy */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center">
            <FileText className="w-8 h-8 mr-3 text-blue-600 dark:text-blue-400" />
            إدارة الواجبات
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            أنشئ وأدر واجباتك التفاعلية وقيم أداء الطلاب
          </p>
        </motion.div>

        {/* الإحصائيات السريعة - zaki alkholy */}
        <QuickStats stats={mockStats} />

        {/* فلاتر البحث - zaki alkholy */}
        <AssignmentFilters filters={filters} onFilterChange={setFilters} />

        {/* شبكة الواجبات - zaki alkholy */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {mockAssignments.map((assignment) => (
            <AssignmentCard
              key={assignment.id}
              assignment={assignment}
              onView={handleViewAssignment}
              onEdit={handleEditAssignment}
              onGrade={handleGradeAssignment}
            />
          ))}
        </div>

        {/* رسالة عدم وجود واجبات - zaki alkholy */}
        {mockAssignments.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              لا توجد واجبات بعد
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              ابدأ بإنشاء واجبك الأول لطلابك
            </p>
            <button className="flex items-center mx-auto px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
              <Plus className="w-5 h-5 mr-2" />
              إنشاء واجب جديد
            </button>
          </motion.div>
        )}
      </div>
    </div>
  );
}
