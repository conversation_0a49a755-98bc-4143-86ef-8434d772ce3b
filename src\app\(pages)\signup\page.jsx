"use client";
import React, { useState } from "react";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";
import { ButtonLoader } from "@/components/common/UniversalLoader";
import { GoogleLogin } from "@react-oauth/google";
import axios from "axios";
import {
  useRegisterMutation,
  useLoginMutation,
  useGoogleLoginMutation,
} from "@/store/authApi";
import { setCredentials } from "@/store/authSlice";
import { useDispatch } from "react-redux";
import Link from "next/link";
import {
  User,
  Mail,
  Lock,
  Phone,
  Eye,
  EyeOff,
  UserPlus,
  GraduationCap,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
// import { signIn, getSession } from "next-auth/react";
export default function Signup() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();
  const dispatch = useDispatch();
  const [register] = useRegisterMutation();
  const [login] = useLoginMutation();

  const [googleLogin] = useGoogleLoginMutation();

  const initialValues = {
    username: "",
    email: "",
    first_name: "",
    last_name: "",
    password: "",
    phone_number: "",
    is_instructor: null,
  };

  const validate = (values) => {
    const errors = {};
    const regex = {
      username: /^[a-zA-Z0-9_]{3,30}$/,
      email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      phone: /^01[0-2,5]{1}[0-9]{8}$/,
      password:
        /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
    };

    if (!values.username.trim()) {
      errors.username = "اسم المستخدم مطلوب";
    } else if (!regex.username.test(values.username)) {
      errors.username = "صيغة اسم المستخدم غير صحيحة";
    }

    if (!values.email.trim()) {
      errors.email = "البريد الإلكتروني مطلوب";
    } else if (!regex.email.test(values.email)) {
      errors.email = "صيغة البريد الإلكتروني غير صحيحة";
    }

    if (!values.password) {
      errors.password = "كلمة المرور مطلوبة";
    } else if (!regex.password.test(values.password)) {
      errors.password =
        "يجب أن تحتوي كلمة المرور على 8 أحرف على الأقل، حرف كبير، حرف صغير، رقم وحرف خاص";
    }

    if (!values.phone_number) {
      errors.phone_number = "رقم الهاتف مطلوب";
    } else if (!regex.phone.test(values.phone_number)) {
      errors.phone_number = "رقم هاتف مصري غير صحيح";
    }

    return errors;
  };

  const onSubmit = async (values) => {
    // التحقق من اختيار نوع المستخدم قبل إرسال النموذج
    if (values.is_instructor === null) {
      setError("يرجى تحديد ما إذا كنت مدربًا أم لا");
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      // تسجيل المستخدم
      const response = await register(values).unwrap();

      // التحقق من الاستجابة الجديدة
      if (response.verification_required) {
        setSuccess(true);
        setError(null);
        // عرض رسالة نجاح وتوجيه المستخدم لصفحة تسجيل الدخول بعد 5 ثوان
        setTimeout(() => {
          router.push("/login");
        }, 5000);
      } else {
        // في حالة عدم الحاجة للتحقق (مثل Google login)
        if (response.token) {
          dispatch(setCredentials(response));
          router.push("/");
        }
      }
    } catch (err) {
      setError(
        err?.data?.email?.[0] || // ← دي هتلقط رسالة "البريد الإلكتروني مستخدم بالفعل."
          err?.data?.username?.[0] || // ← دي هتلقط رسالة "البريد الإلكتروني مستخدم بالفعل."
          err?.data?.message ||
          err?.data?.detail ||
          err?.message ||
          "فشل التسجيل. يرجى المحاولة مرة أخرى."
      );
      console.log(err);
    } finally {
      setIsLoading(false);
    }
  };

  const formik = useFormik({
    initialValues,
    validate,
    onSubmit,
  });

  const formFields = [
    {
      id: "first_name",
      label: "الاسم الأول",
      type: "text",
      placeholder: "أدخل الاسم الأول",
      icon: User,
    },
    {
      id: "last_name",
      label: "الاسم الأخير",
      type: "text",
      placeholder: "أدخل الاسم الأخير",
      icon: User,
    },
    {
      id: "username",
      label: "اسم المستخدم",
      type: "text",
      placeholder: "أدخل اسم المستخدم",
      icon: User,
    },
    {
      id: "email",
      label: "البريد الإلكتروني",
      type: "email",
      placeholder: "أدخل البريد الإلكتروني",
      icon: Mail,
    },
    {
      id: "password",
      label: "كلمة المرور",
      type: "password",
      placeholder: "أدخل كلمة المرور",
      icon: Lock,
      hasToggle: true,
    },
    {
      id: "phone_number",
      label: "رقم الهاتف",
      type: "tel",
      placeholder: "أدخل رقم الهاتف",
      icon: Phone,
    },
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <ButtonLoader size="large" />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-indigo-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-indigo-400/20 to-purple-400/20 rounded-full blur-3xl animate-float"></div>
        <div
          className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl animate-float"
          style={{ animationDelay: "2s" }}
        ></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-3xl animate-pulse-slow"></div>
      </div>

      <div className="w-full max-w-lg space-y-8 relative z-10">
        {/* Logo and Header */}
        <div className="text-center animate-fade-in">
          <div className="mx-auto h-16 w-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <GraduationCap className="h-8 w-8 text-white" />
          </div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            انضم إلى منصة مُعَلِّمِيّ
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            أنشئ حسابك الآن وابدأ رحلتك التعليمية
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4 animate-slide-in-left">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <AlertCircle className="h-5 w-5 text-red-500" />
              </div>
              <div className="mr-3">
                <p className="text-sm text-red-700 dark:text-red-300">
                  {error}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Success Message */}
        {success && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-4 animate-slide-in-left">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-green-500" />
              </div>
              <div className="mr-3">
                <div className="text-sm text-green-700 dark:text-green-300">
                  <p className="font-semibold mb-2">تم إنشاء حسابك بنجاح! 🎉</p>
                  <p className="mb-2">
                    تم إرسال رابط التفعيل إلى بريدك الإلكتروني.
                  </p>
                  <p className="mb-2">
                    يرجى التحقق من بريدك الإلكتروني وتفعيل حسابك قبل تسجيل
                    الدخول.
                  </p>
                  <p className="text-xs text-green-600 dark:text-green-400">
                    سيتم توجيهك إلى صفحة تسجيل الدخول خلال 5 ثوان...
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Signup Form */}
        <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-white/20 dark:border-gray-700/50 animate-slide-up">
          <form className="space-y-6" onSubmit={formik.handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {formFields.map((field) => {
                const IconComponent = field.icon;
                const isPassword = field.id === "password";
                const inputType =
                  isPassword && showPassword ? "text" : field.type;

                return (
                  <div
                    key={field.id}
                    className={
                      field.id === "email" || field.id === "username"
                        ? "md:col-span-2"
                        : ""
                    }
                  >
                    <label
                      htmlFor={field.id}
                      className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                    >
                      {field.label}
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <IconComponent className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                      </div>
                      <input
                        id={field.id}
                        required
                        name={field.id}
                        type={inputType}
                        placeholder={field.placeholder}
                        className={`block w-full pr-10 ${
                          isPassword ? "pl-12" : "pl-3"
                        } py-3 border rounded-xl placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500 ${
                          formik.errors[field.id] && formik.touched[field.id]
                            ? "border-red-500 focus:ring-red-500"
                            : "border-gray-300 dark:border-gray-600"
                        }`}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        value={formik.values[field.id]}
                      />
                      {isPassword && (
                        <button
                          type="button"
                          className="absolute inset-y-0 left-0 pl-3 flex items-center"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors" />
                          ) : (
                            <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors" />
                          )}
                        </button>
                      )}
                    </div>
                    {formik.errors[field.id] && formik.touched[field.id] && (
                      <p className="mt-2 text-sm text-red-500 flex items-center gap-1 animate-slide-in-right">
                        <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                        {formik.errors[field.id]}
                      </p>
                    )}
                  </div>
                );
              })}
            </div>

            {/* Instructor Selection */}
            <div className="md:col-span-2 space-y-4">
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                هل أنت مدرب؟ <span className="text-red-500">*</span>
              </p>

              <div className="grid grid-cols-2 gap-4">
                <label
                  htmlFor="is_instructor_yes"
                  className={`relative flex items-center justify-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:shadow-md ${
                    formik.values.is_instructor === true
                      ? "border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20 text-indigo-700 dark:text-indigo-300"
                      : "border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:border-gray-400 dark:hover:border-gray-500"
                  }`}
                >
                  <input
                    id="is_instructor_yes"
                    name="is_instructor"
                    type="radio"
                    value="true"
                    onChange={() => formik.setFieldValue("is_instructor", true)}
                    checked={formik.values.is_instructor === true}
                    className="sr-only"
                  />
                  <div className="flex items-center gap-3">
                    <GraduationCap className="h-5 w-5" />
                    <span className="font-medium">نعم، أنا مدرب</span>
                  </div>
                  {formik.values.is_instructor === true && (
                    <div className="absolute top-2 left-2">
                      <CheckCircle className="h-5 w-5 text-indigo-500" />
                    </div>
                  )}
                </label>

                <label
                  htmlFor="is_instructor_no"
                  className={`relative flex items-center justify-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:shadow-md ${
                    formik.values.is_instructor === false
                      ? "border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20 text-indigo-700 dark:text-indigo-300"
                      : "border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:border-gray-400 dark:hover:border-gray-500"
                  }`}
                >
                  <input
                    id="is_instructor_no"
                    name="is_instructor"
                    type="radio"
                    value="false"
                    onChange={() =>
                      formik.setFieldValue("is_instructor", false)
                    }
                    checked={formik.values.is_instructor === false}
                    className="sr-only"
                  />
                  <div className="flex items-center gap-3">
                    <User className="h-5 w-5" />
                    <span className="font-medium">لا، أنا طالب</span>
                  </div>
                  {formik.values.is_instructor === false && (
                    <div className="absolute top-2 left-2">
                      <CheckCircle className="h-5 w-5 text-indigo-500" />
                    </div>
                  )}
                </label>
              </div>
            </div>

            {/* Submit Button */}
            <div className="md:col-span-2">
              <button
                type="submit"
                disabled={isLoading}
                className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.02] hover:shadow-lg"
              >
                <span className="absolute left-0 inset-y-0 flex items-center pl-3">
                  {!isLoading && (
                    <UserPlus className="h-5 w-5 text-indigo-300 group-hover:text-indigo-200 transition-colors" />
                  )}
                </span>
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <ButtonLoader size="small" />
                    <span>جاري إنشاء الحساب...</span>
                  </div>
                ) : (
                  "إنشاء حساب جديد"
                )}
              </button>
            </div>
          </form>
        </div>

        {/* Divider */}
        <div
          className="relative animate-fade-in"
          style={{ animationDelay: "0.2s" }}
        >
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300 dark:border-gray-600"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-gradient-to-br from-indigo-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 text-gray-500 dark:text-gray-400">
              أو
            </span>
          </div>
        </div>

        {/* Google Login */}
        <div
          className="w-full animate-fade-in"
          style={{ animationDelay: "0.3s" }}
        >
          <GoogleLogin
            onSuccess={async (credentialResponse) => {
              try {
                const result = await googleLogin({
                  id_token: credentialResponse.credential,
                });

                console.log("Result:", result);
                console.log("Google login successful", credentialResponse);
                console.log("Google login response:", result.data.user);

                if (result.data.user.is_instructor == true) {
                  dispatch(setCredentials(result.data));
                  router.push("/instructor/dashboard");
                  return;
                } else {
                  // لو مش مدرب، روح على صفحة التسجيل كمدرب
                  router.push("/areYouInstructor");
                }
                // ✅ لو كل حاجة تمام، روح على الـ dashboard أو الرئيسية
              } catch (err) {
                console.error("Google login error:", err);
              }
            }}
            onError={() => {
              console.log("Login Failed");
            }}
            theme="outline"
            size="large"
            width="100%"
            text="signup_with"
            shape="rectangular"
          />
        </div>

        {/* Footer Links */}
        <div
          className="text-center space-y-4 animate-fade-in"
          style={{ animationDelay: "0.4s" }}
        >
          <p className="text-sm text-gray-600 dark:text-gray-400">
            لديك حساب بالفعل؟{" "}
            <button
              onClick={() => router.push("/login")}
              className="font-medium text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300 transition-colors duration-200"
            >
              تسجيل الدخول
            </button>
          </p>

          <Link
            href="/forgot-password"
            className="block text-sm text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300 transition-colors duration-200 hover:underline"
          >
            نسيت كلمة المرور؟
          </Link>
        </div>
      </div>
    </div>
  );
}
