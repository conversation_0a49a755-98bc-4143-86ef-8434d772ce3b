// صفحة اختبار API التقدم - <PERSON><PERSON>
'use client';

import React, { useState } from 'react';
import axios from 'axios';
import { API_BASE_URL } from '@/config/api';
import Cookies from 'js-cookie';

export default function TestProgressPage() {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);

  const addResult = (message, data = null, isError = false) => {
    setResults(prev => [...prev, {
      message,
      data,
      isError,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const testProgressAPI = async () => {
    setLoading(true);
    setResults([]);
    
    const token = Cookies.get('authToken') || localStorage.getItem('access_token');
    if (!token) {
      addResult('لا يوجد رمز مصادقة', null, true);
      setLoading(false);
      return;
    }

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    };

    // اختبار 1: جلب جميع بيانات التقدم
    try {
      addResult('🔍 اختبار جلب جميع بيانات التقدم...');
      const allProgressResponse = await axios.get(
        `${API_BASE_URL}/api/student-progress/`,
        { headers }
      );
      addResult('✅ نجح جلب جميع بيانات التقدم', allProgressResponse.data);
    } catch (error) {
      addResult('❌ فشل جلب جميع بيانات التقدم', error.response?.data || error.message, true);
    }

    // اختبار 1.5: إضافة الطالب للكورس مباشرة عبر Django Admin API
    try {
      addResult('🔍 محاولة إضافة الطالب للكورس مباشرة...');

      // جلب تفاصيل الكورس أولاً
      const courseResponse = await axios.get(
        `${API_BASE_URL}/api/courses/86a05b54-2d39-40ff-b0fa-ee6242911808/`,
        { headers }
      );

      const course = courseResponse.data;
      addResult('✅ تم جلب تفاصيل الكورس', course);

      // التحقق من تسجيل الطالب - استخدام Redux store بدلاً من API
      // نحتاج معرف المستخدم من Redux أو localStorage
      const userDataFromStorage = localStorage.getItem('user');
      let userId = null;

      if (userDataFromStorage) {
        const userData = JSON.parse(userDataFromStorage);
        userId = userData.id;
        addResult('✅ تم جلب معرف المستخدم من localStorage', { userId });
      } else {
        // جرب جلب من API users
        try {
          const usersResponse = await axios.get(`${API_BASE_URL}/api/users/`, { headers });
          const users = usersResponse.data.results || usersResponse.data;
          if (users && users.length > 0) {
            userId = users[0].id; // أخذ أول مستخدم (المستخدم الحالي)
            addResult('✅ تم جلب معرف المستخدم من API users', { userId });
          }
        } catch (userError) {
          addResult('❌ فشل جلب معرف المستخدم', userError.response?.data || userError.message, true);
          return;
        }
      }

      const isEnrolled = course.students?.some(student => student.id === userId);
      addResult(`📋 حالة التسجيل: ${isEnrolled ? 'مسجل' : 'غير مسجل'}`, { userId, isEnrolled });

      // إذا لم يكن مسجلاً، جرب التسجيل
      if (!isEnrolled) {
        try {
          addResult('🔄 محاولة التسجيل في الكورس...');
          const enrollResponse = await axios.post(
            `${API_BASE_URL}/api/courses/86a05b54-2d39-40ff-b0fa-ee6242911808/enroll/`,
            {},
            { headers }
          );
          addResult('✅ نجح التسجيل في الكورس!', enrollResponse.data);
        } catch (enrollError) {
          addResult('❌ فشل التسجيل في الكورس', {
            status: enrollError.response?.status,
            data: enrollError.response?.data,
            message: enrollError.message
          }, true);
        }
      }

    } catch (error) {
      addResult('❌ فشل التحقق من التسجيل', error.response?.data || error.message, true);
    }

    // اختبار 2: اختبار حفظ التقدم (بعد التأكد من التسجيل)
    try {
      addResult('🔍 اختبار حفظ التقدم...');
      const saveResponse = await axios.post(
        `${API_BASE_URL}/api/student-progress/update_progress/`,
        {
          lesson_id: '919c5610-12e2-43f5-80cc-a44b1bb57efb', // معرف درس موجود
          watch_time: 30,
          completion_percentage: 25
        },
        { headers }
      );
      addResult('✅ نجح حفظ التقدم!', saveResponse.data);
    } catch (error) {
      addResult('❌ فشل حفظ التقدم', error.response?.data || error.message, true);

      // إذا كان الخطأ 403، جرب التسجيل في الكورس أولاً
      if (error.response?.status === 403) {
        try {
          addResult('🔄 محاولة التسجيل في الكورس أولاً...');
          const enrollResponse = await axios.post(
            `${API_BASE_URL}/api/courses/86a05b54-2d39-40ff-b0fa-ee6242911808/enroll/`,
            {},
            { headers }
          );
          addResult('✅ نجح التسجيل! جرب حفظ التقدم مرة أخرى...', enrollResponse.data);

          // جرب حفظ التقدم مرة أخرى بعد التسجيل
          const retryResponse = await axios.post(
            `${API_BASE_URL}/api/student-progress/update_progress/`,
            {
              lesson_id: '919c5610-12e2-43f5-80cc-a44b1bb57efb',
              watch_time: 30,
              completion_percentage: 25
            },
            { headers }
          );
          addResult('✅ نجح حفظ التقدم بعد التسجيل!', retryResponse.data);
        } catch (enrollError) {
          addResult('❌ فشل التسجيل أيضاً', enrollError.response?.data || enrollError.message, true);
        }
      }
    }

    // اختبار 3: جلب تقدم الطالب بعد حفظه
    try {
      addResult('🔍 اختبار جلب التقدم المحفوظ...');
      const savedProgressResponse = await axios.get(
        `${API_BASE_URL}/api/student-progress/`,
        { headers }
      );
      const progressData = savedProgressResponse.data.results || savedProgressResponse.data;
      const studentProgress = progressData.filter(p => p.lesson === '919c5610-12e2-43f5-80cc-a44b1bb57efb');
      addResult('✅ نجح جلب التقدم المحفوظ', {
        totalProgress: progressData.length,
        studentProgress: studentProgress
      });
    } catch (error) {
      addResult('❌ فشل جلب التقدم المحفوظ', error.response?.data || error.message, true);
    }

    // اختبار 4: جلب الكورسات
    try {
      addResult('🔍 اختبار جلب الكورسات...');
      const coursesResponse = await axios.get(
        `${API_BASE_URL}/api/courses/`,
        { headers }
      );
      addResult('✅ نجح جلب الكورسات', coursesResponse.data);
    } catch (error) {
      addResult('❌ فشل جلب الكورسات', error.response?.data || error.message, true);
    }

    // اختبار 5: جلب تفاصيل كورس معين
    try {
      addResult('🔍 اختبار جلب تفاصيل كورس معين...');
      const courseDetailResponse = await axios.get(
        `${API_BASE_URL}/api/courses/86a05b54-2d39-40ff-b0fa-ee6242911808/`,
        { headers }
      );
      addResult('✅ نجح جلب تفاصيل الكورس', courseDetailResponse.data);
    } catch (error) {
      addResult('❌ فشل جلب تفاصيل الكورس', error.response?.data || error.message, true);
    }

    setLoading(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">اختبار API التقدم</h1>

        <button
          onClick={testProgressAPI}
          disabled={loading}
          className="mb-6 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg disabled:opacity-50 transition-colors"
        >
          {loading ? 'جاري الاختبار...' : 'بدء الاختبار'}
        </button>

        <div className="space-y-4">
          {results.map((result, index) => (
            <div
              key={index}
              className={`p-4 rounded-lg border ${
                result.isError
                  ? 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-700'
                  : 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700'
              }`}
            >
              <div className="flex justify-between items-start mb-2">
                <h3 className={`font-medium ${
                  result.isError ? 'text-red-800 dark:text-red-400' : 'text-green-800 dark:text-green-400'
                }`}>
                  {result.message}
                </h3>
                <span className="text-xs text-gray-500 dark:text-gray-400">{result.timestamp}</span>
              </div>

              {result.data && (
                <pre className="text-xs bg-gray-100 dark:bg-gray-800 text-foreground p-3 rounded overflow-auto max-h-40">
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              )}
            </div>
          ))}
        </div>

        {results.length === 0 && !loading && (
          <div className="text-center py-12">
            <p className="text-gray-600 dark:text-gray-400">اضغط "بدء الاختبار" لاختبار API التقدم</p>
          </div>
        )}
      </div>
    </div>
  );
}
