// hooks/useAuth.js
// useAuth: Hook لإدارة حالة مصادقة المستخدم وتسجيل الخروج
import { useSelector, useDispatch } from 'react-redux';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { selectCurrentUser, selectIsAuthenticated, logout, setCredentials } from '../store/authSlice';
// useGetUserQuery removed - user data comes from login response

export const useAuth = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const user = useSelector(selectCurrentUser);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  // User data comes from login response, no need for separate API call

  const handleLogout = () => {
    dispatch(logout());
    router.push('/login');
  };

  useEffect(() => {
    if (!isAuthenticated && typeof window !== 'undefined') {
      router.push('/login');
    }
  }, [isAuthenticated, router]);

  return {
    user,
    isAuthenticated,
    handleLogout,
  };
};