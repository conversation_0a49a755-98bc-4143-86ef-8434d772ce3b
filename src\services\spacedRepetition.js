// خدمات المراجعة المتباعدة: جميع استدعاءات API الخاصة بالمراجعة المتباعدة - zaki alkholy
import axios from "axios";
import { API_BASE_URL } from '../config/api';

// ===============================
// خدمات جدولة المراجعة - zaki alkholy
// ===============================

/**
 * الحصول على جدول المراجعة للطالب - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {Object} filters - فلاتر البحث
 * @returns {Promise} جدول المراجعة
 */
export async function fetchReviewSchedule(token, filters = {}) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/review-schedules/`, {
    headers,
    withCredentials: true,
    params: filters
  });
  
  return response.data;
}

/**
 * إنشاء جدولة مراجعة جديدة - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {Object} scheduleData - بيانات الجدولة
 * @returns {Promise} الجدولة المُنشأة
 */
export async function createReviewSchedule(token, scheduleData) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.post(`${API_BASE_URL}/api/review-schedules/`, scheduleData, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * تحديث جدولة مراجعة - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} scheduleId - معرف الجدولة
 * @param {Object} scheduleData - البيانات المحدثة
 * @returns {Promise} الجدولة المحدثة
 */
export async function updateReviewSchedule(token, scheduleId, scheduleData) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.put(`${API_BASE_URL}/api/review-schedules/${scheduleId}/`, scheduleData, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

// ===============================
// خدمات جلسات المراجعة - zaki alkholy
// ===============================

/**
 * بدء جلسة مراجعة - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} scheduleId - معرف الجدولة
 * @returns {Promise} بيانات الجلسة
 */
export async function startReviewSession(token, scheduleId) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.post(`${API_BASE_URL}/api/review-schedules/${scheduleId}/start-session/`, {}, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * إنهاء جلسة مراجعة - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} sessionId - معرف الجلسة
 * @param {Object} sessionData - بيانات الجلسة
 * @returns {Promise} نتيجة الجلسة
 */
export async function endReviewSession(token, sessionId, sessionData) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.post(`${API_BASE_URL}/api/review-sessions/${sessionId}/end/`, sessionData, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * تسجيل نتيجة مراجعة - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} scheduleId - معرف الجدولة
 * @param {Object} resultData - بيانات النتيجة
 * @returns {Promise} الجدولة المحدثة
 */
export async function recordReviewResult(token, scheduleId, resultData) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.post(`${API_BASE_URL}/api/review-schedules/${scheduleId}/record-result/`, resultData, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

// ===============================
// خدمات الجدولة التلقائية - zaki alkholy
// ===============================

/**
 * تشغيل الجدولة التلقائية للمراجعة - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {Object} settings - إعدادات الجدولة
 * @returns {Promise} نتيجة الجدولة
 */
export async function runAutoReviewScheduler(token, settings = {}) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.post(`${API_BASE_URL}/api/student/review/auto-schedule/`, settings, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * الحصول على توصيات المراجعة - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {Object} filters - فلاتر التوصيات
 * @returns {Promise} قائمة التوصيات
 */
export async function fetchReviewRecommendations(token, filters = {}) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/student/review/recommendations/`, {
    headers,
    withCredentials: true,
    params: filters
  });
  
  return response.data;
}

// ===============================
// خدمات إحصائيات المراجعة - zaki alkholy
// ===============================

/**
 * الحصول على إحصائيات المراجعة - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} period - الفترة الزمنية
 * @returns {Promise} إحصائيات المراجعة
 */
export async function fetchReviewStats(token, period = '30') {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/student/review/stats/`, {
    headers,
    withCredentials: true,
    params: { period }
  });
  
  return response.data;
}

/**
 * الحصول على المراجعة اليومية - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} date - التاريخ (اختياري)
 * @returns {Promise} بيانات المراجعة اليومية
 */
export async function fetchDailyReview(token, date = null) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const params = date ? { date } : {};
  
  const response = await axios.get(`${API_BASE_URL}/api/student/review/daily/`, {
    headers,
    withCredentials: true,
    params
  });
  
  return response.data;
}

// ===============================
// خدمات إعدادات المراجعة - zaki alkholy
// ===============================

/**
 * الحصول على إعدادات المراجعة للطالب - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @returns {Promise} إعدادات المراجعة
 */
export async function fetchReviewSettings(token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/student/review/settings/`, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

/**
 * تحديث إعدادات المراجعة - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {Object} settings - الإعدادات الجديدة
 * @returns {Promise} الإعدادات المحدثة
 */
export async function updateReviewSettings(token, settings) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  
  const response = await axios.put(`${API_BASE_URL}/api/student/review/settings/`, settings, {
    headers,
    withCredentials: true,
  });
  
  return response.data;
}

// ===============================
// خدمات تصدير البيانات - zaki alkholy
// ===============================

/**
 * تصدير بيانات المراجعة - zaki alkholy
 * @param {string} token - رمز المصادقة
 * @param {string} format - تنسيق التصدير (csv, pdf, json)
 * @param {Object} filters - فلاتر البيانات
 * @returns {Promise} ملف البيانات
 */
export async function exportReviewData(token, format = 'csv', filters = {}) {
  const headers = {
    Authorization: `Bearer ${token}`,
    Accept: "application/json",
  };
  
  const response = await axios.get(`${API_BASE_URL}/api/student/review/export/`, {
    headers,
    withCredentials: true,
    params: { format, ...filters },
    responseType: 'blob'
  });
  
  return response.data;
}
