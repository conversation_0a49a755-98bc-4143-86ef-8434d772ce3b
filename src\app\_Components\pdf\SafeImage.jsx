import { useEffect, useState } from "react";

export default function SafeImage({ src, className }) {
  const [imgData, setImgData] = useState(null);

  useEffect(() => {
    const fetchImage = async () => {
      try {
        const response = await fetch(src, { mode: "cors" });
        const blob = await response.blob();
        const reader = new FileReader();
        reader.onloadend = () => {
          setImgData(reader.result);
        };
        reader.readAsDataURL(blob);
      } catch (err) {
        console.error("فشل تحميل الصورة:", err);
        setImgData(null);
      }
    };

    fetchImage();
  }, [src]);

  if (!imgData) return null;

  return (
    <img src={imgData} alt="" className={className} />
  );
}
