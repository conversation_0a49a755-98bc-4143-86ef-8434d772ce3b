// import React, { useRef, useEffect, useState } from "react";
// import Modal from "../Modal";
// import ReactCrop from "react-image-crop";
// import "react-image-crop/dist/ReactCrop.css";

// export default function QuestionFormModal({
//   isOpen,
//   onClose,
//   onSubmit,
//   quizId,
//   formState,
//   setFormState,
//   answers: parentAnswers,
//   setAnswers: setParentAnswers,
//   loading,
// }) {
//   // الإجابات
//   const [answers, setAnswers] = useState(parentAnswers && parentAnswers.length ? parentAnswers : [
//     { text: "", is_correct: false },
//     { text: "", is_correct: false }
//   ]);
//   // الصورة
//   const [image, setImage] = useState(null); // File
//   const [imagePreview, setImagePreview] = useState(null); // DataURL
//   const [showCropper, setShowCropper] = useState(false);
//   const [crop, setCrop] = useState({ unit: "%", width: 50, height: 50, x: 25, y: 25 });
//   const [completedCrop, setCompletedCrop] = useState(null);
//   const [croppedBlob, setCroppedBlob] = useState(null);
//   const imgRef = useRef();
//   const formRef = useRef();

//   // تهيئة الإجابات والصورة عند أول فتح للمودال فقط
//   useEffect(() => {
//     if (isOpen) {
//       // إعادة تهيئة الإجابات فقط عند أول فتح
//       if (formState.question_type === "mcq") {
//         setAnswers(
//           parentAnswers && parentAnswers.length ? parentAnswers : [
//             { text: "", is_correct: false },
//             { text: "", is_correct: false }
//           ]
//         );
//         if (typeof setParentAnswers === 'function') {
//           setParentAnswers(
//             parentAnswers && parentAnswers.length ? parentAnswers : [
//               { text: "", is_correct: false },
//               { text: "", is_correct: false }
//             ]
//           );
//         }
//       } else if (formState.question_type === "true_false") {
//         setAnswers(
//           parentAnswers && parentAnswers.length ? parentAnswers : [
//             { text: "صح", is_correct: true },
//             { text: "خطأ", is_correct: false }
//           ]
//         );
//         if (typeof setParentAnswers === 'function') {
//           setParentAnswers(
//             parentAnswers && parentAnswers.length ? parentAnswers : [
//               { text: "صح", is_correct: true },
//               { text: "خطأ", is_correct: false }
//             ]
//           );
//         }
//       }
//       // إعادة تهيئة الصورة والمعاينة
//       const imgUrl = formState.image_url || formState.imageUrl || null;
//       if (imgUrl) {
//         setImage(null); // لا يوجد ملف جديد بعد
//         setImagePreview(imgUrl); // عرض الصورة القديمة
//         setCroppedBlob(null);
//       } else {
//         setImage(null);
//         setImagePreview(null);
//         setCroppedBlob(null);
//       }
//     } else {
//       // عند إغلاق المودال، إعادة تعيين كل شيء
//       setImage(null);
//       setImagePreview(null);
//       setCroppedBlob(null);
//     }
//     // eslint-disable-next-line react-hooks/exhaustive-deps
//   }, [isOpen, formState.id, formState.question_type, formState.image_url, formState.imageUrl]);

//   // تهيئة معاينة الصورة عند اختيار صورة
//   useEffect(() => {
//     if (image) {
//       const reader = new FileReader();
//       reader.onload = e => {
//         setImagePreview(e.target.result);
//         setShowCropper(true); // فتح الكروب تلقائياً عند اختيار صورة
//       };
//       reader.readAsDataURL(image);
//     } else {
//       setImagePreview(null);
//       setCroppedBlob(null);
//     }
//   }, [image]);

//   // crop الصورة
//   const getCroppedImg = async () => {
//     if (!imgRef.current || !completedCrop?.width || !completedCrop?.height) return;
//     const canvas = document.createElement("canvas");
//     const scaleX = imgRef.current.naturalWidth / imgRef.current.width;
//     const scaleY = imgRef.current.naturalHeight / imgRef.current.height;
//     canvas.width = completedCrop.width;
//     canvas.height = completedCrop.height;
//     const ctx = canvas.getContext("2d");
//     ctx.drawImage(
//       imgRef.current,
//       completedCrop.x * scaleX,
//       completedCrop.y * scaleY,
//       completedCrop.width * scaleX,
//       completedCrop.height * scaleY,
//       0,
//       0,
//       completedCrop.width,
//       completedCrop.height
//     );
//     return new Promise(resolve => {
//       canvas.toBlob(blob => {
//         setCroppedBlob(blob);
//         resolve(blob);
//       }, "image/jpeg", 0.8);
//     });
//   };

//   // عند تطبيق crop
//   const handleApplyCrop = async () => {
//     await getCroppedImg();
//     setShowCropper(false);
//   };

//   // حذف الصورة
//   const handleRemoveImage = () => {
//     setImage(null);
//     setImagePreview(null);
//     setCroppedBlob(null);
//     setShowCropper(false);
//   };

//   // عند الحفظ
//   const handleSubmit = e => {
//     e.preventDefault();
//     if (!answers.some(a => a.is_correct)) {
//       alert("يجب اختيار إجابة صحيحة قبل الحفظ");
//       return;
//     }
//     // يمكنك هنا إرسال croppedBlob مع باقي البيانات إذا أردت رفع الصورة
//     onSubmit(quizId, {
//       answers, // الإجابات الحالية
//       image: croppedBlob // الصورة بعد القص
//     });
//   };

//   return (
//     <Modal isOpen={isOpen} onClose={onClose}>
//       <form
//         ref={formRef}
//         onSubmit={handleSubmit}
//         className="flex flex-col gap-3"
//       >
//         <input
//           type="text"
//           className="border rounded px-2 py-1"
//           placeholder="نص السؤال"
//           value={formState.text || ""}
//           onChange={e => setFormState(f => ({ ...f, text: e.target.value }))}
//           required
//         />
//         <select
//           className="border rounded px-2 py-1"
//           value={formState.question_type || "mcq"}
//           onChange={e => setFormState(f => ({ ...f, question_type: e.target.value }))}
//         >
//           <option value="mcq">اختيار من متعدد</option>
//           <option value="true_false">صح أو خطأ</option>
//         </select>
//         <input
//           type="number"
//           className="border rounded px-2 py-1"
//           placeholder="الدرجة"
//           value={formState.points || ""}
//           onChange={e => setFormState(f => ({ ...f, points: e.target.value }))}
//         />
//         <input
//           type="number"
//           className="border rounded px-2 py-1"
//           placeholder="الترتيب"
//           value={formState.order || ""}
//           onChange={e => setFormState(f => ({ ...f, order: e.target.value }))}
//         />
//         {/* رفع صورة السؤال */}
//         <div className="border rounded p-2">
//           <label className="block text-sm font-bold mb-2">صورة السؤال (اختيارية)</label>
//           {!imagePreview ? (
//             <input
//               type="file"
//               accept="image/*"
//               className="border rounded px-2 py-1 w-full"
//               onChange={e => {
//                 const file = e.target.files[0];
//                 if (file) setImage(file);
//               }}
//             />
//           ) : (
//             <div className="space-y-2">
//               <img
//                 src={croppedBlob ? URL.createObjectURL(croppedBlob) : imagePreview}
//                 alt="معاينة الصورة"
//                 className="max-w-full h-32 object-contain border rounded"
//               />
//               <div className="flex gap-2">
//                 <button
//                   type="button"
//                   className="bg-red-500 text-white px-2 py-1 rounded text-xs"
//                   onClick={handleRemoveImage}
//                 >
//                   حذف الصورة
//                 </button>
//                 <button
//                   type="button"
//                   className="bg-blue-500 text-white px-2 py-1 rounded text-xs"
//                   onClick={() => setShowCropper(true)}
//                 >
//                   تعديل الصورة
//                 </button>
//               </div>
//             </div>
//           )}
//         </div>
//         {/* cropper */}
//         {showCropper && imagePreview && (
//           <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
//             <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-auto">
//               <h3 className="text-lg font-bold mb-4 text-center">تعديل الصورة - اسحب لتحديد المنطقة المطلوبة</h3>
//               <div className="mb-4 flex justify-center">
//                 <ReactCrop
//                   crop={crop}
//                   onChange={setCrop}
//                   onComplete={setCompletedCrop}
//                   aspect={undefined}
//                   minWidth={50}
//                   minHeight={50}
//                   keepSelection={true}
//                   style={{ maxWidth: '100%', maxHeight: '60vh' }}
//                 >
//                   <img
//                     ref={imgRef}
//                     src={imagePreview}
//                     alt="للتعديل"
//                     style={{
//                       maxWidth: '100%',
//                       maxHeight: '60vh',
//                       display: 'block'
//                     }}
//                   />
//                 </ReactCrop>
//               </div>
//               <div className="flex gap-2 justify-center">
//                 <button
//                   type="button"
//                   onClick={handleApplyCrop}
//                   className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 transition-colors"
//                   disabled={!completedCrop}
//                 >
//                   تطبيق القص
//                 </button>
//                 <button
//                   type="button"
//                   onClick={() => setShowCropper(false)}
//                   className="bg-gray-300 px-6 py-2 rounded hover:bg-gray-400 transition-colors"
//                 >
//                   إلغاء
//                 </button>
//               </div>
//               <div className="mt-3 text-center text-sm text-gray-600">
//                 💡 نصيحة: اسحب الزوايا لتغيير حجم المنطقة، واسحب المنطقة لتحريكها
//               </div>
//             </div>
//           </div>
//         )}
//         {/* الإجابات */}
//         <div className="bg-gray-50 p-2 rounded border mt-2">
//           <div className="font-bold text-sm mb-2">الإجابات</div>
//           {answers.map((ans, idx) => (
//             <div key={idx} className="flex items-center gap-2 mb-1">
//               <input
//                 type="text"
//                 className="border rounded px-2 py-1 flex-1"
//                 placeholder={`الإجابة ${idx + 1}`}
//                 value={ans.text}
//                 disabled={formState.question_type === "true_false"}
//                 onChange={e =>
//                   setAnswers(prev => prev.map((a, i) => i === idx ? { ...a, text: e.target.value } : a))
//                 }
//                 required
//               />
//               <input
//                 type="radio"
//                 name="correct"
//                 checked={ans.is_correct}
//                 onChange={() =>
//                   setAnswers(prev => prev.map((a, i) => ({ ...a, is_correct: i === idx })))
//                 }
//               />
//               <span className="text-xs">صحيحة</span>
//               {formState.question_type === "mcq" && answers.length > 2 && (
//                 <button
//                   type="button"
//                   className="text-red-500 text-xs ml-2"
//                   onClick={() => setAnswers(prev => prev.filter((_, i) => i !== idx))}
//                 >
//                   حذف
//                 </button>
//               )}
//             </div>
//           ))}
//           {formState.question_type === "mcq" && (
//             <button
//               type="button"
//               className="bg-gray-200 px-2 py-1 rounded text-xs mt-2"
//               onClick={() => setAnswers(prev => [...prev, { text: "", is_correct: false }])}
//               disabled={answers.length >= 6}
//             >
//               + إضافة اختيار
//             </button>
//           )}
//           <div className="text-xs text-red-500 mt-2">
//             يجب اختيار إجابة صحيحة قبل الحفظ
//           </div>
//         </div>
//         <div className="flex gap-2 mt-4">
//           <button type="submit" className="bg-blue-600 text-white px-4 py-1 rounded" disabled={loading}>
//             حفظ
//           </button>
//           <button type="button" className="bg-gray-300 px-4 py-1 rounded" onClick={onClose}>
//             إلغاء
//           </button>
//         </div>
//       </form>
//     </Modal>
//   );
// }