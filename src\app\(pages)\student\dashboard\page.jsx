"use client";
import React from "react";
import { useSelector } from "react-redux";
import { useRouter } from "next/navigation";
import { selectCurrentUser, selectIsAuthenticated } from "@/store/authSlice";
import DashboardComponant from "../../../_Components/DashboardComponent/DashboardComponent";

export default function StudentDashboard() {
  const user = useSelector(selectCurrentUser);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const router = useRouter();

  React.useEffect(() => {
    if (!isAuthenticated || !user) {
      router.push("/login");
      return;
    }
    if (!user.is_student) {
      // إذا كان مدرب أو غير طالب
      if (user.is_instructor) {
        router.push("/instructor/dashboard");
      } else {
        router.push("/");
      }
    }
  }, [isAuthenticated, user, router]);

  if (!user || !isAuthenticated) return null;

  return (
    <div className="min-h-screen pt-16">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-6 text-primary">لوحة تحكم الطالب</h1>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-2">مرحبًا بك يا {user.username} 👋</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">هذه هي لوحة تحكمك كطالب. يمكنك متابعة تقدمك وبياناتك هنا.</p>
        </div>
        {/* مكون لوحة التحكم العام (بدون أقسام المدرب) */}
        <DashboardComponant hideInstructorSections />
      </div>
    </div>
  );
}
