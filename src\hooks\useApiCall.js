import { useState, useCallback } from 'react';

/**
 * Hook مخصص لمعالجة API calls مع error handling محسن
 */
export const useApiCall = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const execute = useCallback(async (apiFunction, options = {}) => {
    const {
      onSuccess,
      onError,
      showLoading = true,
      errorMessage = 'حدث خطأ أثناء العملية'
    } = options;

    try {
      if (showLoading) setLoading(true);
      setError(null);

      const result = await apiFunction();
      
      if (onSuccess) {
        onSuccess(result);
      }
      
      return result;
    } catch (err) {
      // تحديد رسالة الخطأ
      let errorMsg = errorMessage;
      
      if (err.response) {
        // خطأ من الخادم
        const status = err.response.status;
        const data = err.response.data;
        
        switch (status) {
          case 400:
            errorMsg = data.message || 'بيانات غير صحيحة';
            break;
          case 401:
            errorMsg = 'يرجى تسجيل الدخول أولاً';
            break;
          case 403:
            errorMsg = 'ليس لديك صلاحية للقيام بهذا الإجراء';
            break;
          case 404:
            errorMsg = 'العنصر المطلوب غير موجود';
            break;
          case 500:
            errorMsg = 'خطأ في الخادم، يرجى المحاولة لاحقاً';
            break;
          default:
            errorMsg = data.message || `خطأ ${status}`;
        }
      } else if (err.request) {
        // خطأ في الشبكة
        errorMsg = 'خطأ في الاتصال، تحقق من الإنترنت';
      }
      
      const errorObj = new Error(errorMsg);
      errorObj.originalError = err;
      
      setError(errorObj);
      
      if (onError) {
        onError(errorObj);
      }
      
      throw errorObj;
    } finally {
      if (showLoading) setLoading(false);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    loading,
    error,
    execute,
    clearError
  };
};

/**
 * Hook للعمليات المتعددة مع تتبع حالة كل عملية
 */
export const useMultipleApiCalls = () => {
  const [loadingStates, setLoadingStates] = useState({});
  const [errorStates, setErrorStates] = useState({});

  const execute = useCallback(async (key, apiFunction, options = {}) => {
    const {
      onSuccess,
      onError,
      showLoading = true,
      errorMessage = 'حدث خطأ أثناء العملية'
    } = options;

    try {
      if (showLoading) {
        setLoadingStates(prev => ({ ...prev, [key]: true }));
      }
      setErrorStates(prev => ({ ...prev, [key]: null }));

      const result = await apiFunction();
      
      if (onSuccess) {
        onSuccess(result);
      }
      
      return result;
    } catch (err) {
      let errorMsg = errorMessage;
      
      if (err.response) {
        const status = err.response.status;
        const data = err.response.data;
        
        switch (status) {
          case 400:
            errorMsg = data.message || 'بيانات غير صحيحة';
            break;
          case 401:
            errorMsg = 'يرجى تسجيل الدخول أولاً';
            break;
          case 403:
            errorMsg = 'ليس لديك صلاحية للقيام بهذا الإجراء';
            break;
          case 404:
            errorMsg = 'العنصر المطلوب غير موجود';
            break;
          case 500:
            errorMsg = 'خطأ في الخادم، يرجى المحاولة لاحقاً';
            break;
          default:
            errorMsg = data.message || `خطأ ${status}`;
        }
      } else if (err.request) {
        errorMsg = 'خطأ في الاتصال، تحقق من الإنترنت';
      }
      
      const errorObj = new Error(errorMsg);
      errorObj.originalError = err;
      
      setErrorStates(prev => ({ ...prev, [key]: errorObj }));
      
      if (onError) {
        onError(errorObj);
      }
      
      throw errorObj;
    } finally {
      if (showLoading) {
        setLoadingStates(prev => ({ ...prev, [key]: false }));
      }
    }
  }, []);

  const clearError = useCallback((key) => {
    setErrorStates(prev => ({ ...prev, [key]: null }));
  }, []);

  const clearAllErrors = useCallback(() => {
    setErrorStates({});
  }, []);

  const isLoading = useCallback((key) => {
    return !!loadingStates[key];
  }, [loadingStates]);

  const getError = useCallback((key) => {
    return errorStates[key];
  }, [errorStates]);

  return {
    loadingStates,
    errorStates,
    execute,
    clearError,
    clearAllErrors,
    isLoading,
    getError
  };
};

/**
 * Hook للعمليات مع إعادة المحاولة التلقائية
 */
export const useApiCallWithRetry = (maxRetries = 3, retryDelay = 1000) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);

  const execute = useCallback(async (apiFunction, options = {}) => {
    const {
      onSuccess,
      onError,
      showLoading = true,
      errorMessage = 'حدث خطأ أثناء العملية'
    } = options;

    let currentRetry = 0;

    const attemptCall = async () => {
      try {
        if (showLoading) setLoading(true);
        setError(null);
        setRetryCount(currentRetry);

        const result = await apiFunction();
        
        if (onSuccess) {
          onSuccess(result);
        }
        
        return result;
      } catch (err) {
        // إذا كان خطأ 401 أو 403، لا نعيد المحاولة
        if (err.response && [401, 403].includes(err.response.status)) {
          throw err;
        }
        
        // إذا وصلنا للحد الأقصى من المحاولات
        if (currentRetry >= maxRetries) {
          throw err;
        }
        
        // إعادة المحاولة بعد تأخير
        currentRetry++;
        await new Promise(resolve => setTimeout(resolve, retryDelay * currentRetry));
        return attemptCall();
      }
    };

    try {
      return await attemptCall();
    } catch (err) {
      let errorMsg = errorMessage;
      
      if (err.response) {
        const status = err.response.status;
        const data = err.response.data;
        
        switch (status) {
          case 400:
            errorMsg = data.message || 'بيانات غير صحيحة';
            break;
          case 401:
            errorMsg = 'يرجى تسجيل الدخول أولاً';
            break;
          case 403:
            errorMsg = 'ليس لديك صلاحية للقيام بهذا الإجراء';
            break;
          case 404:
            errorMsg = 'العنصر المطلوب غير موجود';
            break;
          case 500:
            errorMsg = 'خطأ في الخادم، يرجى المحاولة لاحقاً';
            break;
          default:
            errorMsg = data.message || `خطأ ${status}`;
        }
      } else if (err.request) {
        errorMsg = 'خطأ في الاتصال، تحقق من الإنترنت';
      }
      
      const errorObj = new Error(errorMsg);
      errorObj.originalError = err;
      errorObj.retryCount = currentRetry;
      
      setError(errorObj);
      
      if (onError) {
        onError(errorObj);
      }
      
      throw errorObj;
    } finally {
      if (showLoading) setLoading(false);
    }
  }, [maxRetries, retryDelay]);

  const clearError = useCallback(() => {
    setError(null);
    setRetryCount(0);
  }, []);

  return {
    loading,
    error,
    retryCount,
    execute,
    clearError
  };
};
