"use client";
import React from "react";
import Link from "next/link";
import {
  Mail,
  Phone,
  MapPin,
  Facebook,
  Twitter,
  Linkedin,
  Instagram,
  Youtube,
  BookOpen,
  Users,
  Award,
  Globe,
  ChevronUp,
} from "lucide-react";

export default function Footer() {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  return (
    <footer className="bg-gray-900 text-white relative">
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-16">
        <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <div className="mb-6">
              <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-4">
                مُعَلِّمِيّ
              </h3>
              <p className="text-gray-300 leading-relaxed">
                منصة تعليمية عربية رائدة تهدف إلى تمكين المدرسين والطلاب من
                تحقيق أهدافهم التعليمية في بيئة رقمية متطورة وآمنة.
              </p>
            </div>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3 space-x-reverse">
                <Mail className="w-5 h-5 text-blue-400" />
                <span className="text-gray-300"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <Phone className="w-5 h-5 text-blue-400" />
                <span className="text-gray-300">01002925291</span>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <MapPin className="w-5 h-5 text-blue-400" />
                <span className="text-gray-300">
                  ش سلامونى بالمنزلة دقهلية
                </span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-bold mb-6 text-white">روابط سريعة</h4>
            <ul className="space-y-3">
              <li>
                <Link
                  href="/about"
                  className="text-gray-300 hover:text-blue-400 transition-colors duration-300 flex items-center"
                >
                  <span>من نحن</span>
                </Link>
              </li>
              <li>
                <Link
                  href="/courses"
                  className="text-gray-300 hover:text-blue-400 transition-colors duration-300 flex items-center"
                >
                  <BookOpen className="w-4 h-4 ml-2" />
                  <span>استعراض الكورسات</span>
                </Link>
              </li>
              <li>
                <Link
                  href="/how-it-works"
                  className="text-gray-300 hover:text-blue-400 transition-colors duration-300 flex items-center"
                >
                  <span>كيف تعمل المنصة</span>
                </Link>
              </li>
              <li>
                <Link
                  href="/areYouInstructor"
                  className="text-gray-300 hover:text-blue-400 transition-colors duration-300 flex items-center"
                >
                  <Users className="w-4 h-4 ml-2" />
                  <span>انضم كمدرّس</span>
                </Link>
              </li>
              <li>
                <Link
                  href="/instructors"
                  className="text-gray-300 hover:text-blue-400 transition-colors duration-300 flex items-center"
                >
                  <Award className="w-4 h-4 ml-2" />
                  <span>المدرسين</span>
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h4 className="text-lg font-bold mb-6 text-white">
              الدعم والمساعدة
            </h4>
            <ul className="space-y-3">
              <li>
                <Link
                  href="/help"
                  className="text-gray-300 hover:text-blue-400 transition-colors duration-300"
                >
                  مركز المساعدة
                </Link>
              </li>
              <li>
                <Link
                  href="/ContactUs"
                  className="text-gray-300 hover:text-blue-400 transition-colors duration-300"
                >
                  تواصل معنا
                </Link>
              </li>
              <li>
                <Link
                  href="/complaints"
                  className="text-gray-300 hover:text-blue-400 transition-colors duration-300"
                >
                  الشكاوى والاقتراحات
                </Link>
              </li>
              <li>
                <Link
                  href="/faq"
                  className="text-gray-300 hover:text-blue-400 transition-colors duration-300"
                >
                  الأسئلة الشائعة
                </Link>
              </li>
              <li>
                <Link
                  href="/technical-support"
                  className="text-gray-300 hover:text-blue-400 transition-colors duration-300"
                >
                  الدعم التقني
                </Link>
              </li>
            </ul>
          </div>

          {/* Legal & Language */}
          <div>
            <h4 className="text-lg font-bold mb-6 text-white">قانوني ولغة</h4>
            <ul className="space-y-3 mb-6">
              <li>
                <Link
                  href="/TermsOfService"
                  className="text-gray-300 hover:text-blue-400 transition-colors duration-300"
                >
                  الشروط والأحكام
                </Link>
              </li>
              <li>
                <Link
                  href="/PrivacyPolicy"
                  className="text-gray-300 hover:text-blue-400 transition-colors duration-300"
                >
                  سياسة الخصوصية
                </Link>
              </li>
              <li>
                <Link
                  href="/RefundPolicy"
                  className="text-gray-300 hover:text-blue-400 transition-colors duration-300"
                >
                  سياسة الاسترداد
                </Link>
              </li>
              <li>
                <Link
                  href="/ShippingPolicy"
                  className="text-gray-300 hover:text-blue-400 transition-colors duration-300"
                >
                  سياسة التسليم
                </Link>
              </li>
              {/* <li>
                <Link
                  href="/cookies"
                  className="text-gray-300 hover:text-blue-400 transition-colors duration-300"
                >
                  سياسة ملفات تعريف الارتباط
                </Link>
              </li> */}
            </ul>

            {/* Language Selector */}
            <div className="mb-6">
              <h5 className="text-sm font-semibold mb-3 text-gray-400">
                اختر اللغة
              </h5>
              <div className="flex space-x-2 space-x-reverse">
                <button className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium">
                  <Globe className="w-4 h-4 ml-2" />
                  العربية
                </button>
                <button className="flex items-center px-3 py-2 bg-gray-700 text-gray-300 hover:bg-gray-600 rounded-lg text-sm font-medium transition-colors duration-300">
                  <Globe className="w-4 h-4 ml-2" />
                  English
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Social Media & Newsletter */}
        <div className="border-t border-gray-700 pt-8 mt-12">
          <div className="grid md:grid-cols-2 gap-8 items-center">
            {/* Social Media */}
            <div>
              <h4 className="text-lg font-bold mb-4 text-white">تابعنا على</h4>
              <div className="flex space-x-4 space-x-reverse">
                <a
                  href="https://facebook.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-blue-600 hover:bg-blue-700 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110"
                >
                  <Facebook className="w-5 h-5" />
                </a>
                <a
                  href="https://twitter.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-sky-500 hover:bg-sky-600 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110"
                >
                  <Twitter className="w-5 h-5" />
                </a>
                <a
                  href="https://linkedin.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-blue-700 hover:bg-blue-800 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110"
                >
                  <Linkedin className="w-5 h-5" />
                </a>
                <a
                  href="https://instagram.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110"
                >
                  <Instagram className="w-5 h-5" />
                </a>
                <a
                  href="https://youtube.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-red-600 hover:bg-red-700 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110"
                >
                  <Youtube className="w-5 h-5" />
                </a>
              </div>
            </div>

            {/* Newsletter */}
            <div>
              <h4 className="text-lg font-bold mb-4 text-white">
                اشترك في النشرة الإخبارية
              </h4>
              <div className="flex">
                <input
                  type="email"
                  placeholder="أدخل بريدك الإلكتروني"
                  className="flex-1 px-4 py-3 bg-gray-800 border border-gray-600 rounded-r-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400"
                />
                <button className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 rounded-l-lg font-medium transition-all duration-300 hover:scale-105">
                  اشترك
                </button>
              </div>
              <p className="text-gray-400 text-sm mt-2">
                احصل على آخر الأخبار والعروض الخاصة
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="bg-gray-800 border-t border-gray-700">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-400 text-sm mb-4 md:mb-0">
              © 2024 مُعَلِّمِيّ. جميع الحقوق محفوظة.
            </div>

            {/* Back to Top */}
            <button
              onClick={scrollToTop}
              className="flex items-center space-x-2 space-x-reverse text-gray-400 hover:text-blue-400 transition-colors duration-300"
            >
              <ChevronUp className="w-4 h-4" />
              <span className="text-sm">العودة للأعلى</span>
            </button>
          </div>
        </div>
      </div>
    </footer>
  );
}
