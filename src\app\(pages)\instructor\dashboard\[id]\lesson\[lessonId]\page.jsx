"use client";
import React, { useEffect, useState, useRef } from "react";
import { useParams, useRouter } from "next/navigation";
import { motion } from "framer-motion";
import Cookies from "js-cookie";
import Hls from "hls.js";
import Plyr from "plyr";
import "plyr/dist/plyr.css";
import {
  fetchLessonsByCourse,
  fetchLessonVideoUrl,
} from "../../../../../../../services/instructor";
import VideoInfo from "../../../../../../../components/VideoInfo";

export default function LessonDetails() {
  // ================== المتغيرات الرئيسية ==================
  const theUrl = "https://res.cloudinary.com/djwhgnwp5/";
  const params = useParams();
  const router = useRouter();
  const { id: courseId, lessonId } = params;
  const videoRef = useRef(null);
  const playerRef = useRef(null);
  const [lessonData, setLessonData] = useState(null);
  const [videoUrl, setVideoUrl] = useState(null);
  const [videoToken, setVideoToken] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isClient, setIsClient] = useState(false);
  // ================== جلب بيانات الدرس ==================
  useEffect(() => {
    setIsClient(true);
    fetchLessonData();
    // eslint-disable-next-line
  }, [courseId, lessonId]);

  /**
   * جلب بيانات الدرس من الخدمة
   */
  async function fetchLessonData() {
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }
    try {
      setLoading(true);
      const lessons = await fetchLessonsByCourse(courseId, token);
      const lesson = lessons.find((l) => String(l.id) === String(lessonId));
      if (lesson) {
        setLessonData(lesson);
        if (lesson.lesson_type === "video") {
          fetchVideo(lessonId, token);
        }
      } else {
        setError("لم يتم العثور على الدرس");
      }
    } catch (err) {
      setError("فشل في جلب بيانات الدرس");
    } finally {
      setLoading(false);
    }
  }

  /**
   * جلب رابط الفيديو المؤمن - نظام مختلط - زكي الخولي
   * يدعم Cloudinary و Bunny Stream
   */
  async function fetchVideo(lessonId, token) {
    try {
      const data = await fetchLessonVideoUrl(lessonId, token);
      if (data.video_url) {
        setVideoUrl(data.video_url);
        setVideoToken(data.token);

        // تسجيل معلومات الفيديو للمطور - زكي الخولي
        // console.log("معلومات الفيديو - زكي الخولي:", {
        //   url: data.video_url,
        //   platform: data.platform,
        //   video_type: data.video_type,
        //   security_level: data.security_level,
        //   user_type: data.user_type,
        //   watermark: data.watermark,
        // });
      }
    } catch (err) {
      console.error(
        "خطأ في جلب الفيديو - زكي الخولي:",
        err.response?.data || err.message
      );
      setError("فشل في جلب رابط الفيديو");
    }
  }

  // ================== تهيئة مشغل الفيديو المختلط - زكي الخولي ==================
  useEffect(() => {
    if (videoUrl && isClient) {
      const video = document.getElementById("lesson-video");
      if (!video) return;
      if (playerRef.current) {
        playerRef.current.destroy();
      }

      // تحديد نوع الفيديو من الرابط - زكي الخولي
      const isBunnyStream =
        videoUrl.includes("mediadelivery.net") ||
        videoUrl.includes("b-cdn.net");
      const isCloudinary = videoUrl.includes("cloudinary.com");

      console.log("نوع الفيديو - زكي الخولي:", {
        isBunnyStream,
        isCloudinary,
        url: videoUrl,
      });

      video.crossOrigin = "anonymous";

      if (isBunnyStream) {
        // Bunny Stream - فيديو محمي - زكي الخولي
        console.log("تشغيل فيديو Bunny Stream محمي - زكي الخولي");

        // للفيديوهات المحمية، نستخدم iframe للمعلم - زكي الخولي
        if (videoUrl.includes("/embed/")) {
          // إنشاء iframe للمعلم - زكي الخولي
          const videoContainer = video.parentElement;
          const iframe = document.createElement("iframe");
          iframe.src = videoUrl;
          iframe.style.width = "100%";
          iframe.style.height = "400px";
          iframe.style.border = "none";
          iframe.style.borderRadius = "8px";
          iframe.allow = "autoplay; fullscreen; encrypted-media";
          iframe.allowFullscreen = true;
          iframe.setAttribute(
            "sandbox",
            "allow-scripts allow-same-origin allow-presentation"
          );

          // استبدال عنصر الفيديو بـ iframe
          videoContainer.replaceChild(iframe, video);
          console.log("تم تحميل Bunny Stream iframe للمعلم - زكي الخولي");
          return;
        } else {
          video.src = videoUrl;
          video.addEventListener("loadedmetadata", function () {
            playerRef.current = new Plyr(video, {
              controls: [
                "play-large",
                "play",
                "progress",
                "current-time",
                "mute",
                "volume",
                "settings",
                "fullscreen",
              ],
              download: false, // منع التحميل للفيديوهات المحمية - زكي الخولي
              hideControls: true,
              keyboard: { focused: true, global: true },
              tooltips: { controls: true, seek: true },
              disableContextMenu: true, // منع القائمة اليمنى - زكي الخولي
            });
          });
        }
      } else if (isCloudinary || Hls.isSupported()) {
        // Cloudinary أو HLS عادي - زكي الخولي
        console.log("تشغيل فيديو HLS - زكي الخولي");

        const hls = new Hls();
        hls.loadSource(videoUrl);
        hls.attachMedia(video);
        hls.on(Hls.Events.MANIFEST_PARSED, function () {
          playerRef.current = new Plyr(video, {
            controls: [
              "play-large",
              "play",
              "progress",
              "current-time",
              "mute",
              "volume",
              "captions",
              "settings",
              "pip",
              "airplay",
              "fullscreen",
            ],
            download: false,
            hideControls: true,
            keyboard: { focused: true, global: true },
            tooltips: { controls: true, seek: true },
            captions: { active: true, language: "auto", update: true },
            quality: {
              default: 720,
              options: [4320, 2880, 2160, 1440, 1080, 720, 576, 480, 360, 240],
            },
          });
        });
      } else if (video.canPlayType("application/vnd.apple.mpegurl")) {
        video.src = videoUrl.replace(".m3u8", ".mp4");
        video.addEventListener("loadedmetadata", function () {
          playerRef.current = new Plyr(video, {
            controls: [
              "play-large",
              "play",
              "progress",
              "current-time",
              "mute",
              "volume",
              "captions",
              "settings",
              "pip",
              "airplay",
              "fullscreen",
            ],
            download: false,
            hideControls: true,
            keyboard: { focused: true, global: true },
            tooltips: { controls: true, seek: true },
            captions: { active: true, language: "auto", update: true },
            quality: {
              default: 720,
              options: [4320, 2880, 2160, 1440, 1080, 720, 576, 480, 360, 240],
            },
          });
        });
      } else {
        setError("المتصفح لا يدعم تشغيل فيديوهات HLS");
      }

      video.addEventListener("error", function () {
        setError("حدث خطأ أثناء تشغيل الفيديو");
      });
    }
    return () => {
      if (playerRef.current) {
        playerRef.current.destroy();
      }
    };
  }, [videoUrl, isClient]);

  // ================== عرض شاشة التحميل ==================
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 dark:border-blue-400 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
            جاري تحميل البيانات...
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            يرجى الانتظار قليلاً
          </p>
        </div>
      </div>
    );
  }

  // ================== عرض رسالة الخطأ ==================
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-xl p-6 max-w-md mx-auto">
            <h2 className="text-xl font-bold text-red-600 dark:text-red-400 mb-2">
              حدث خطأ
            </h2>
            <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-red-600 dark:bg-red-500 text-white rounded-lg hover:bg-red-700 dark:hover:bg-red-600 transition-colors"
            >
              إعادة المحاولة
            </button>
          </div>
        </div>
      </div>
    );
  }

  // ================== عرض رسالة عدم وجود بيانات ==================
  if (!lessonData) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-xl p-6 max-w-md mx-auto">
            <h2 className="text-xl font-bold text-yellow-600 dark:text-yellow-400 mb-2">
              لم يتم العثور على البيانات
            </h2>
            <p className="text-yellow-600 dark:text-yellow-400">
              لم يتم العثور على بيانات الدرس
            </p>
          </div>
        </div>
      </div>
    );
  }
  console.log("lessonData", lessonData);
  console.log("🔗 الفيديو HLS URL:", videoUrl);

  // ================== واجهة المستخدم الرئيسية ==================
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* رأس الصفحة */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg dark:hover:shadow-gray-900/20 transition-all duration-300">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              {lessonData.title}
            </h1>
            <div className="flex flex-wrap items-center gap-4 text-gray-600 dark:text-gray-400">
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full"></span>
                <span>
                  نوع الدرس:{" "}
                  {lessonData.lesson_type === "video" ? "درس فيديو" : "درس نصي"}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 dark:bg-green-400 rounded-full"></span>
                <span>المدة: {lessonData.duration} دقيقة</span>
              </div>
              {lessonData.is_preview && (
                <span className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 px-3 py-1 rounded-full text-sm border border-green-200 dark:border-green-700">
                  درس تجريبي
                </span>
              )}
            </div>
          </div>
        </motion.div>

        {/* معلومات نوع الفيديو للمعلم - زكي الخولي */}
        {/* {lessonData.lesson_type === "video" && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-6"
          >
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg dark:hover:shadow-gray-900/20 transition-all duration-300">
              <h3 className="text-lg font-semibold text-blue-600 dark:text-blue-400 mb-4 flex items-center">
                <span className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full mr-2"></span>
                معلومات الفيديو - زكي الخولي
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <span className="font-medium text-gray-700 dark:text-gray-300">
                    المنصة:{" "}
                  </span>
                  <span
                    className={
                      lessonData.is_preview
                        ? "text-orange-600 dark:text-orange-400"
                        : "text-green-600 dark:text-green-400"
                    }
                  >
                    {lessonData.is_preview
                      ? "Cloudinary (ترويجي)"
                      : "Bunny Stream (محمي)"}
                  </span>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <span className="font-medium text-gray-700 dark:text-gray-300">
                    مستوى الحماية:{" "}
                  </span>
                  <span
                    className={
                      lessonData.is_preview
                        ? "text-orange-600 dark:text-orange-400"
                        : "text-green-600 dark:text-green-400"
                    }
                  >
                    {lessonData.is_preview ? "أساسي" : "عالي"}
                  </span>
                </div>
                {!lessonData.is_preview && (
                  <>
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                      <span className="font-medium text-gray-700 dark:text-gray-300">
                        التشفير:{" "}
                      </span>
                      <span className="text-green-600 dark:text-green-400">
                        DRM + HLS
                      </span>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                      <span className="font-medium text-gray-700 dark:text-gray-300">
                        Watermark:{" "}
                      </span>
                      <span className="text-green-600 dark:text-green-400">
                        اسم الطالب
                      </span>
                    </div>
                  </>
                )}
              </div>
            </div>
          </motion.div>
        )} */}

        {/* معلومات الفيديو للمعلم - زكي الخولي */}
        {/* {lessonData.lesson_type === "video" && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mb-6"
          >
            <VideoInfo
              lesson={lessonData}
              userType="instructor"
              className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg dark:hover:shadow-gray-900/20 transition-all duration-300"
            />
          </motion.div>
        )} */}

        {/* محتوى الدرس */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="mb-8"
        >
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg dark:hover:shadow-gray-900/20 transition-all duration-300">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <span className="w-2 h-2 bg-purple-500 dark:bg-purple-400 rounded-full mr-2"></span>
              محتوى الدرس
            </h2>
            <div className="prose max-w-none text-gray-700 dark:text-gray-300">
              <div dangerouslySetInnerHTML={{ __html: lessonData.content }} />
            </div>
          </div>
        </motion.div>

        {/* مشغل الفيديو المحمي (HLS/DRM) */}
        {isClient && lessonData.lesson_type === "video" && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mb-8"
          >
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg dark:hover:shadow-gray-900/20 transition-all duration-300">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                <span className="w-2 h-2 bg-red-500 dark:bg-red-400 rounded-full mr-2"></span>
                مشغل الفيديو
              </h2>
              <div className="aspect-w-16 aspect-h-9 bg-black rounded-xl overflow-hidden">
                <video
                  id="lesson-video"
                  ref={videoRef}
                  className="w-full h-full rounded-xl"
                  playsInline
                  controls
                  crossOrigin="anonymous"
                  type="application/x-mpegURL" // ✅ النوع
                />
              </div>
            </div>
          </motion.div>
        )}
        {/* الموارد */}
        {lessonData.resources && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="mb-8"
          >
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg dark:hover:shadow-gray-900/20 transition-all duration-300">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                <span className="w-2 h-2 bg-green-500 dark:bg-green-400 rounded-full mr-2"></span>
                الموارد والملفات
              </h2>
              <div className="space-y-3">
                {Array.isArray(lessonData.resources) ? (
                  lessonData.resources.length > 0 ? (
                    lessonData.resources.map((resource, index) => (
                      <a
                        key={index}
                        href={resource}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors group"
                      >
                        <div className="flex-shrink-0">
                          <svg
                            className="w-6 h-6 text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>
                        </div>
                        <div className="flex-1">
                          <span className="text-gray-900 dark:text-gray-100 font-medium">
                            الملف {index + 1}
                          </span>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            انقر للتحميل
                          </p>
                        </div>
                        <div className="flex-shrink-0">
                          <svg
                            className="w-5 h-5 text-gray-400 dark:text-gray-500 group-hover:text-gray-600 dark:group-hover:text-gray-300"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                            />
                          </svg>
                        </div>
                      </a>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <div className="text-gray-500 dark:text-gray-400">
                        لا يوجد ملفات مرفقة
                      </div>
                    </div>
                  )
                ) : lessonData.resources ? (
                  <a
                    href={lessonData.resources}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors group"
                  >
                    <div className="flex-shrink-0">
                      <svg
                        className="w-6 h-6 text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <span className="text-gray-900 dark:text-gray-100 font-medium">
                        الملف المرفق
                      </span>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        انقر للتحميل
                      </p>
                    </div>
                    <div className="flex-shrink-0">
                      <svg
                        className="w-5 h-5 text-gray-400 dark:text-gray-500 group-hover:text-gray-600 dark:group-hover:text-gray-300"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                        />
                      </svg>
                    </div>
                  </a>
                ) : (
                  <div className="text-center py-8">
                    <div className="text-gray-500 dark:text-gray-400">
                      لا يوجد ملفات مرفقة
                    </div>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}
