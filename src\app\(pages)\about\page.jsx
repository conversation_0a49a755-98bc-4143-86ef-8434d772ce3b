"use client";
import React from "react";

export default function About() {
  return (
    <div className="min-h-screen pt-16 bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold text-center text-foreground mb-8">
            من نحن
          </h1>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 md:p-8 mb-8">
            <h2 className="text-2xl md:text-3xl font-semibold text-primary mb-6">
              منصة مُعَلِّمِيّ التعليمية
            </h2>

            <div className="prose prose-lg dark:prose-invert max-w-none">
              <p className="text-foreground leading-relaxed mb-6">
                منصة مُعَلِّمِيّ هي منصة تعليمية متطورة تهدف إلى تمكين المعلمين
                من نشر دوراتهم التعليمية ومتابعة تقدم طلابهم بطريقة سهلة وفعالة.
                نحن نؤمن بأن التعليم هو أساس التقدم والنمو.
              </p>

              <h3 className="text-xl md:text-2xl font-semibold text-primary mb-4">
                رؤيتنا
              </h3>
              <p className="text-foreground leading-relaxed mb-6">
                أن نكون المنصة الرائدة في مجال التعليم الإلكتروني في المنطقة
                العربية، ونساهم في بناء جيل متعلم ومبدع قادر على مواجهة تحديات
                المستقبل.
              </p>

              <h3 className="text-xl md:text-2xl font-semibold text-primary mb-4">
                مهمتنا
              </h3>
              <p className="text-foreground leading-relaxed mb-6">
                توفير بيئة تعليمية تفاعلية وآمنة تمكن المعلمين من إنشاء ونشر
                محتوى تعليمي عالي الجودة، وتساعد الطلاب على التعلم بطريقة ممتعة
                وفعالة.
              </p>

              <h3 className="text-xl md:text-2xl font-semibold text-primary mb-4">
                ما نقدمه
              </h3>
              <ul className="list-disc list-inside text-foreground space-y-2 mb-6">
                <li>إنشاء وإدارة الدورات التعليمية</li>
                <li>نظام اختبارات تفاعلي</li>
                <li>متابعة تقدم الطلاب</li>
                <li>تحليلات مفصلة للأداء</li>
                <li>بيئة تعليمية آمنة</li>
                <li>دعم فني متواصل</li>
              </ul>
            </div>
          </div>

          <div className="text-center">
            <p className="text-secondary text-lg">
              انضم إلينا اليوم وكن جزءاً من رحلة التعليم المتطور
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
