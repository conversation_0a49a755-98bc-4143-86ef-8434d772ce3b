"use client";
import React, { useState, useEffect } from "react";
import axios from "axios";
import {
  CheckCircle,
  DollarSign,
  Calendar,
  User,
  FileText,
  Download,
  Eye,
  AlertCircle,
  Filter,
  Search,
  RotateCcw,
  X,
  Upload
} from "lucide-react";

export default function CompletedPayouts() {
  const [completedPayouts, setCompletedPayouts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");

  // حالات مودال الاسترجاع
  const [showReversalModal, setShowReversalModal] = useState(false);
  const [selectedPayout, setSelectedPayout] = useState(null);
  const [reversalReason, setReversalReason] = useState("");
  const [processingReversal, setProcessingReversal] = useState(false);

  // حالات رفع الوصل
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadingFile, setUploadingFile] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);

  useEffect(() => {
    fetchCompletedPayouts();

    // إضافة event listener للتحديث عند إكمال تحويل جديد من صفحة المبيعات المعلقة
    const handlePayoutCompleted = () => {
      console.log('Payout completed, refreshing completed payouts...');
      fetchCompletedPayouts();
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('payoutCompleted', handlePayoutCompleted);
    }

    // تنظيف event listener عند إلغاء تحميل المكون
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('payoutCompleted', handlePayoutCompleted);
      }
    };
  }, []);

  const fetchCompletedPayouts = async () => {
    try {
      const token = localStorage.getItem("admin_token");
      const response = await axios.get("http://127.0.0.1:8000/api/admin-dashboard/completed-payouts/", {
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      setCompletedPayouts(response.data.completed_payouts || []);
    } catch (error) {
      console.error("Error fetching completed payouts:", error);
      setError("حدث خطأ في تحميل البيانات");
    } finally {
      setLoading(false);
    }
  };

  const filteredPayouts = completedPayouts.filter(payout => {
    const instructorName = payout.instructor?.username || '';
    const instructorEmail = payout.instructor?.email || '';

    const matchesSearch = instructorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         instructorEmail.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = filterStatus === "all" || payout.status === filterStatus;

    return matchesSearch && matchesFilter;
  });

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'failed':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'مكتمل';
      case 'pending':
        return 'معلق';
      case 'failed':
        return 'فاشل';
      default:
        return 'غير معروف';
    }
  };

  const handleDownloadReceipt = async (payout) => {
    try {
      if (payout.receipt_image) {
        const token = localStorage.getItem("admin_token");

        // تنزيل الصورة كـ blob
        const response = await fetch(payout.receipt_image, {
          headers: {
            "Authorization": `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error('فشل في تحميل الصورة');
        }

        const blob = await response.blob();

        // إنشاء رابط التنزيل
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // تحديد اسم الملف مع امتداد صحيح
        // استخراج امتداد الملف من Content-Type أو من URL
        let fileExtension = 'jpg'; // افتراضي

        const contentType = response.headers.get('content-type');
        if (contentType) {
          if (contentType.includes('jpeg') || contentType.includes('jpg')) {
            fileExtension = 'jpg';
          } else if (contentType.includes('png')) {
            fileExtension = 'png';
          } else if (contentType.includes('webp')) {
            fileExtension = 'webp';
          }
        } else {
          // محاولة استخراج الامتداد من URL (قبل معاملات Cloudinary)
          const urlParts = payout.receipt_image.split('?')[0]; // إزالة معاملات URL
          const urlExtension = urlParts.split('.').pop();
          if (['jpg', 'jpeg', 'png', 'webp'].includes(urlExtension.toLowerCase())) {
            fileExtension = urlExtension.toLowerCase() === 'jpeg' ? 'jpg' : urlExtension.toLowerCase();
          }
        }

        const fileName = `receipt_${payout.instructor?.username || 'instructor'}_${payout.id}.${fileExtension}`;
        link.download = fileName;

        // إخفاء الرابط وإضافته للصفحة
        link.style.display = 'none';
        document.body.appendChild(link);

        // تنزيل الملف
        link.click();

        // تنظيف الموارد
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

      } else {
        alert("لا يوجد وصل متاح للتنزيل");
      }

    } catch (error) {
      console.error("Error downloading receipt:", error);
      alert("حدث خطأ في تنزيل الوصل");
    }
  };

  const handleReversePayout = async () => {
    if (!selectedPayout || !reversalReason.trim()) {
      alert('يرجى إدخال سبب الإرجاع');
      return;
    }

    setProcessingReversal(true);
    try {
      const token = localStorage.getItem("admin_token");
      const response = await axios.post(`http://127.0.0.1:8000/api/admin-dashboard/reverse-payout/${selectedPayout.id}/`, {
        reason: reversalReason
      }, {
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      alert(`تم إرجاع التحويل للمبيعات المعلقة بنجاح!\nالمبلغ: ${response.data.amount_returned} جنيه\nالسبب: ${response.data.reason}\n\nسيظهر التحويل الآن في صفحة المبيعات المعلقة`);

      // إعادة تحميل البيانات وإغلاق المودال
      fetchCompletedPayouts();
      setShowReversalModal(false);
      setSelectedPayout(null);
      setReversalReason("");

      // إشعار صفحة المبيعات المعلقة بالتحديث
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('payoutReversed', {
          detail: { payoutId: selectedPayout.id }
        }));
      }
    } catch (error) {
      console.error("Error reversing payout:", error);
      alert("حدث خطأ في إرجاع التحويل");
    } finally {
      setProcessingReversal(false);
    }
  };

  const handleUploadReceipt = async () => {
    if (!uploadingFile || !selectedPayout) return;

    const formData = new FormData();
    formData.append('receipt_image', uploadingFile);

    try {
      setUploadProgress(0);
      const token = localStorage.getItem("admin_token");

      const xhr = new XMLHttpRequest();

      // تتبع التقدم
      xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
          const progress = (e.loaded / e.total) * 100;
          setUploadProgress(progress);
        }
      });

      xhr.open('POST', `http://127.0.0.1:8000/api/admin-dashboard/upload-receipt/${selectedPayout.id}/`);
      xhr.setRequestHeader('Authorization', `Bearer ${token}`);

      xhr.onload = function() {
        if (xhr.status === 200) {
          alert('تم رفع وصل التحويل بنجاح!');

          // إعادة تحميل البيانات وإغلاق المودال
          fetchCompletedPayouts();
          setShowUploadModal(false);
          setSelectedPayout(null);
          setUploadingFile(null);
          setUploadProgress(0);
        } else {
          try {
            const errorResponse = JSON.parse(xhr.responseText);
            alert(`فشل في رفع الوصل: ${errorResponse.error || 'خطأ غير معروف'}`);
          } catch (e) {
            alert('فشل في رفع الوصل: خطأ في الخادم');
          }
        }
      };

      xhr.onerror = function() {
        alert('حدث خطأ في رفع الملف');
        setUploadProgress(0);
      };

      xhr.send(formData);

    } catch (error) {
      console.error("Error uploading receipt:", error);
      alert("حدث خطأ في رفع الوصل");
      setUploadProgress(0);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2 space-x-reverse">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="text-gray-600 dark:text-gray-400">جاري تحميل التحويلات المكتملة...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div className="flex items-center">
          <AlertCircle className="w-5 h-5 text-red-500 ml-2" />
          <span className="text-red-700 dark:text-red-400">{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              التحويلات المكتملة
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              قائمة جميع التحويلات التي تم إنجازها للمعلمين
            </p>
          </div>
          <div className="flex items-center space-x-2 space-x-reverse">
            <CheckCircle className="w-5 h-5 text-green-500" />
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {completedPayouts.length} تحويل مكتمل
            </span>
          </div>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي التحويلات</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{completedPayouts.length}</p>
            </div>
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/20">
              <FileText className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي المبلغ المحول</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {completedPayouts.reduce((sum, payout) => sum + parseFloat(payout.amount_paid), 0).toFixed(2)} جنيه
              </p>
            </div>
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/20">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">المعلمين المستفيدين</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {new Set(completedPayouts.map(p => p.instructor_id)).size}
              </p>
            </div>
            <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900/20">
              <User className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">متوسط التحويل</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {completedPayouts.length > 0 
                  ? (completedPayouts.reduce((sum, payout) => sum + parseFloat(payout.amount_paid), 0) / completedPayouts.length).toFixed(2)
                  : 0
                } جنيه
              </p>
            </div>
            <div className="p-3 rounded-full bg-orange-100 dark:bg-orange-900/20">
              <Calendar className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="البحث بالاسم أو البريد الإلكتروني..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pr-10 pl-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Status Filter */}
          <div className="md:w-48">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">جميع الحالات</option>
              <option value="completed">مكتمل</option>
              <option value="pending">معلق</option>
              <option value="failed">فاشل</option>
            </select>
          </div>
        </div>
      </div>

      {/* Payouts List */}
      {filteredPayouts.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12 text-center">
          <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            لا توجد تحويلات
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {searchTerm || filterStatus !== "all" 
              ? "لا توجد نتائج تطابق البحث أو الفلتر المحدد"
              : "لم يتم إجراء أي تحويلات بعد"
            }
          </p>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    المعلم
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    المبلغ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    تاريخ التحويل
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    وصل التحويل
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    إجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredPayouts.map((payout) => (
                  <tr key={payout.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                          <span className="text-white font-semibold text-sm">
                            {(payout.instructor?.username || 'U').charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div className="mr-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {payout.instructor?.username || 'مستخدم غير معروف'}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {payout.instructor?.email || 'بريد غير متوفر'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {parseFloat(payout.amount_paid).toFixed(2)} جنيه
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {payout.instructor?.payment_method || 'غير محدد'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {payout.completed_at ? new Date(payout.completed_at).toLocaleDateString('ar-EG') : 'غير محدد'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(payout.status)}`}>
                        {getStatusText(payout.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {payout.receipt_image ? (
                        <div className="flex items-center">
                          <CheckCircle className="w-4 h-4 text-green-500 ml-2" />
                          <span className="text-green-600">متوفر</span>
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <AlertCircle className="w-4 h-4 text-orange-500 ml-2" />
                          <span className="text-orange-600">غير متوفر</span>
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                          <Eye className="w-4 h-4" />
                        </button>
                        {payout.receipt_image ? (
                          <button
                            onClick={() => handleDownloadReceipt(payout)}
                            className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                            title="تنزيل وصل التحويل"
                          >
                            <Download className="w-4 h-4" />
                          </button>
                        ) : payout.status === 'completed' && (
                          <button
                            onClick={() => {
                              setSelectedPayout(payout);
                              setShowUploadModal(true);
                            }}
                            className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                            title="رفع وصل التحويل"
                          >
                            <Upload className="w-4 h-4" />
                          </button>
                        )}
                        {payout.status === 'completed' && (
                          <button
                            onClick={() => {
                              setSelectedPayout(payout);
                              setShowReversalModal(true);
                            }}
                            className="text-orange-600 hover:text-orange-900 dark:text-orange-400 dark:hover:text-orange-300"
                            title="إرجاع للمبيعات المعلقة"
                          >
                            <RotateCcw className="w-4 h-4" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* مودال استرجاع التحويل */}
      {showReversalModal && selectedPayout && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                إرجاع التحويل للمبيعات المعلقة
              </h3>
              <button
                onClick={() => {
                  setShowReversalModal(false);
                  setSelectedPayout(null);
                  setReversalReason("");
                }}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="mb-4">
              <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4 mb-4">
                <div className="flex items-center">
                  <AlertCircle className="w-5 h-5 text-orange-500 ml-2" />
                  <span className="text-orange-700 dark:text-orange-400 font-medium">تنبيه</span>
                </div>
                <p className="text-orange-600 dark:text-orange-400 text-sm mt-1">
                  سيتم إرجاع التحويل للمبيعات المعلقة وإزالة الوصل. سيظهر مرة أخرى في صفحة المبيعات المعلقة.
                </p>
              </div>

              <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
                <p><strong>المعلم:</strong> {selectedPayout.instructor?.username}</p>
                <p><strong>المبلغ:</strong> {parseFloat(selectedPayout.amount_paid).toFixed(2)} جنيه</p>
                <p><strong>تاريخ التحويل:</strong> {selectedPayout.completed_at ? new Date(selectedPayout.completed_at).toLocaleDateString('ar-EG') : 'غير محدد'}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  سبب الإرجاع *
                </label>
                <textarea
                  value={reversalReason}
                  onChange={(e) => setReversalReason(e.target.value)}
                  placeholder="اكتب سبب إرجاع التحويل للمبيعات المعلقة..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  rows={3}
                  required
                />
              </div>
            </div>

            <div className="flex items-center space-x-3 space-x-reverse">
              <button
                onClick={handleReversePayout}
                disabled={processingReversal || !reversalReason.trim()}
                className="flex-1 flex items-center justify-center px-4 py-2 bg-orange-600 hover:bg-orange-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                {processingReversal ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                    جاري الإرجاع...
                  </>
                ) : (
                  <>
                    <RotateCcw className="w-4 h-4 ml-2" />
                    إرجاع للمبيعات المعلقة
                  </>
                )}
              </button>
              <button
                onClick={() => {
                  setShowReversalModal(false);
                  setSelectedPayout(null);
                  setReversalReason("");
                }}
                disabled={processingReversal}
                className="px-4 py-2 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}

      {/* مودال رفع الوصل */}
      {showUploadModal && selectedPayout && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                رفع وصل التحويل
              </h3>
              <button
                onClick={() => {
                  setShowUploadModal(false);
                  setSelectedPayout(null);
                  setUploadingFile(null);
                  setUploadProgress(0);
                }}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="mb-4">
              <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
                <p><strong>المعلم:</strong> {selectedPayout.instructor?.username}</p>
                <p><strong>المبلغ:</strong> {parseFloat(selectedPayout.amount_paid).toFixed(2)} جنيه</p>
                <p><strong>تاريخ التحويل:</strong> {selectedPayout.completed_at ? new Date(selectedPayout.completed_at).toLocaleDateString('ar-EG') : 'غير محدد'}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  اختر صورة الوصل
                </label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => setUploadingFile(e.target.files[0])}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {uploadProgress > 0 && (
                <div className="mt-4">
                  <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                    <span>جاري الرفع...</span>
                    <span>{Math.round(uploadProgress)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-3 space-x-reverse">
              <button
                onClick={handleUploadReceipt}
                disabled={!uploadingFile || uploadProgress > 0}
                className="flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                <Upload className="w-4 h-4 ml-2" />
                رفع الوصل
              </button>
              <button
                onClick={() => {
                  setShowUploadModal(false);
                  setSelectedPayout(null);
                  setUploadingFile(null);
                  setUploadProgress(0);
                }}
                disabled={uploadProgress > 0}
                className="px-4 py-2 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}

      {/* مودال رفع الوصل */}
      {showUploadModal && selectedPayout && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                رفع وصل التحويل
              </h3>
              <button
                onClick={() => {
                  setShowUploadModal(false);
                  setSelectedPayout(null);
                  setUploadingFile(null);
                  setUploadProgress(0);
                }}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="mb-4">
              <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
                <p><strong>المعلم:</strong> {selectedPayout.instructor?.username}</p>
                <p><strong>المبلغ:</strong> {parseFloat(selectedPayout.amount_paid).toFixed(2)} جنيه</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  اختر صورة الوصل
                </label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => setUploadingFile(e.target.files[0])}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              {uploadProgress > 0 && (
                <div className="mt-4">
                  <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                    <span>جاري الرفع...</span>
                    <span>{Math.round(uploadProgress)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-3 space-x-reverse">
              <button
                onClick={handleUploadReceipt}
                disabled={!uploadingFile || uploadProgress > 0}
                className="flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                <Upload className="w-4 h-4 ml-2" />
                رفع الوصل
              </button>
              <button
                onClick={() => {
                  setShowUploadModal(false);
                  setSelectedPayout(null);
                  setUploadingFile(null);
                  setUploadProgress(0);
                }}
                disabled={uploadProgress > 0}
                className="px-4 py-2 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
