import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_API_URL;

/**
 * خدمات إدارة الكويزات والأسئلة
 */
export class QuizService {
  
  /**
   * إنشاء كويز جديد
   * @param {Object} quizData - بيانات الكويز
   * @param {string} token - رمز المصادقة
   */
  static async createQuiz(quizData, token) {
    const response = await axios.post(
      `${API_URL}/api/quizzes/`,
      quizData,
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    );
    return response.data;
  }

  /**
   * تحديث كويز
   * @param {string} quizId - معرف الكويز
   * @param {Object} quizData - البيانات المحدثة
   * @param {string} token - رمز المصادقة
   */
  static async updateQuiz(quizId, quizData, token) {
    const response = await axios.patch(
      `${API_URL}/api/quizzes/${quizId}/`,
      quizData,
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    );
    return response.data;
  }

  /**
   * حذف كويز
   * @param {string} quizId - معرف الكويز
   * @param {string} token - رمز المصادقة
   */
  static async deleteQuiz(quizId, token) {
    await axios.delete(
      `${API_URL}/api/quizzes/${quizId}/`,
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    );
  }

  /**
   * جلب كويز محدد
   * @param {string} quizId - معرف الكويز
   * @param {string} token - رمز المصادقة
   */
  static async getQuiz(quizId, token) {
    const response = await axios.get(
      `${API_URL}/api/quizzes/${quizId}/`,
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    );
    return response.data;
  }

  /**
   * تبديل حالة النشر للكويز
   * @param {string} quizId - معرف الكويز
   * @param {boolean} isPublished - حالة النشر الجديدة
   * @param {string} token - رمز المصادقة
   */
  static async toggleQuizPublish(quizId, isPublished, token) {
    const response = await axios.patch(
      `${API_URL}/api/quizzes/${quizId}/`,
      { is_published: isPublished },
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    );
    return response.data;
  }
}

/**
 * خدمات إدارة الأسئلة
 */
export class QuestionService {
  
  /**
   * إنشاء سؤال جديد
   * @param {FormData} questionData - بيانات السؤال (مع الصورة إن وجدت)
   * @param {string} token - رمز المصادقة
   */
  static async createQuestion(questionData, token) {
    const response = await axios.post(
      `${API_URL}/api/questions/`,
      questionData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "multipart/form-data",
        },
      }
    );
    return response.data;
  }

  /**
   * تحديث سؤال
   * @param {string} questionId - معرف السؤال
   * @param {FormData} questionData - البيانات المحدثة
   * @param {string} token - رمز المصادقة
   */
  static async updateQuestion(questionId, questionData, token) {
    const response = await axios.patch(
      `${API_URL}/api/questions/${questionId}/`,
      questionData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "multipart/form-data",
        },
      }
    );
    return response.data;
  }

  /**
   * حذف سؤال
   * @param {string} questionId - معرف السؤال
   * @param {string} token - رمز المصادقة
   */
  static async deleteQuestion(questionId, token) {
    await axios.delete(
      `${API_URL}/api/questions/${questionId}/`,
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    );
  }
}

/**
 * خدمات إدارة الإجابات
 */
export class AnswerService {
  
  /**
   * إنشاء إجابة جديدة
   * @param {Object} answerData - بيانات الإجابة
   * @param {string} token - رمز المصادقة
   */
  static async createAnswer(answerData, token) {
    const response = await axios.post(
      `${API_URL}/api/answers/`,
      answerData,
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    );
    return response.data;
  }

  /**
   * تحديث إجابة
   * @param {string} answerId - معرف الإجابة
   * @param {Object} answerData - البيانات المحدثة
   * @param {string} token - رمز المصادقة
   */
  static async updateAnswer(answerId, answerData, token) {
    const response = await axios.patch(
      `${API_URL}/api/answers/${answerId}/`,
      answerData,
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    );
    return response.data;
  }

  /**
   * حذف إجابة
   * @param {string} answerId - معرف الإجابة
   * @param {string} token - رمز المصادقة
   */
  static async deleteAnswer(answerId, token) {
    await axios.delete(
      `${API_URL}/api/answers/${answerId}/`,
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    );
  }

  /**
   * إنشاء عدة إجابات دفعة واحدة
   * @param {Array} answers - مصفوفة الإجابات
   * @param {string} questionId - معرف السؤال
   * @param {string} token - رمز المصادقة
   */
  static async createMultipleAnswers(answers, questionId, token) {
    const promises = answers.map(answer => 
      this.createAnswer({
        question: questionId,
        text: answer.text,
        is_correct: answer.is_correct,
      }, token)
    );
    
    return Promise.all(promises);
  }
}

/**
 * خدمات مساعدة للكويزات
 */
export class QuizHelperService {
  
  /**
   * تحديث الدرجة النهائية للواجبات تلقائياً - zaki alkholy
   * @param {string} quizId - معرف الكويز
   * @param {number} questionCount - عدد الأسئلة
   * @param {string} token - رمز المصادقة
   */
  static async updateAssignmentMaxScore(quizId, questionCount, token) {
    return QuizService.updateQuiz(quizId, {
      max_score: questionCount  // النظام الجديد - zaki alkholy
    }, token);
  }

  /**
   * التحقق من نوع الكويز وتحديث الدرجة النهائية إذا لزم الأمر - zaki alkholy
   * @param {Object} quiz - بيانات الكويز
   * @param {string} token - رمز المصادقة
   */
  static async handleAssignmentScoring(quiz, token) {
    if (quiz.quiz_type === "assignment") {
      const questionCount = quiz.questions?.length || 0;
      if (questionCount > 0 && quiz.max_score !== questionCount) {
        await this.updateAssignmentMaxScore(quiz.id, questionCount, token);
        return true; // يشير إلى أنه تم التحديث
      }
    }
    return false; // لم يتم التحديث
  }
}

// تصدير جميع الخدمات
export default {
  QuizService,
  QuestionService,
  AnswerService,
  QuizHelperService,
};
