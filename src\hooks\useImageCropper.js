import { useState, useCallback } from 'react';
import { handleImageSelect, getCroppedImg } from '@/utils/imageHelpers';

export const useImageCropper = () => {
  const [croppedImages, setCroppedImages] = useState({});
  const [questionImages, setQuestionImages] = useState({});
  const [imagePreview, setImagePreview] = useState({});
  const [showImageCropper, setShowImageCropper] = useState({});

  const handleQuestionImageSelect = useCallback((quizId, event) => {
    handleImageSelect(
      event,
      quizId,
      setQuestionImages,
      setImagePreview,
      setShowImageCropper
    );
  }, []);

  const handleCropComplete = useCallback(async (quizId, croppedAreaPixels) => {
    try {
      const croppedImage = await getCroppedImg(
        imagePreview[quizId],
        croppedAreaPixels
      );
      setCroppedImages((prev) => ({ ...prev, [quizId]: croppedImage }));
      setShowImageCropper((prev) => ({ ...prev, [quizId]: false }));
    } catch (error) {
      console.error("Error cropping image:", error);
    }
  }, [imagePreview]);

  const removeQuestionImage = useCallback((quizId) => {
    setCroppedImages((prev) => {
      const newImages = { ...prev };
      delete newImages[quizId];
      return newImages;
    });
    setQuestionImages((prev) => {
      const newImages = { ...prev };
      delete newImages[quizId];
      return newImages;
    });
    setImagePreview((prev) => {
      const newPreviews = { ...prev };
      delete newPreviews[quizId];
      return newPreviews;
    });
    setShowImageCropper((prev) => {
      const newCroppers = { ...prev };
      delete newCroppers[quizId];
      return newCroppers;
    });
  }, []);

  const resetImageStates = useCallback(() => {
    setCroppedImages({});
    setQuestionImages({});
    setImagePreview({});
    setShowImageCropper({});
  }, []);

  return {
    croppedImages,
    setCroppedImages,
    questionImages,
    setQuestionImages,
    imagePreview,
    setImagePreview,
    showImageCropper,
    setShowImageCropper,
    handleQuestionImageSelect,
    handleCropComplete,
    removeQuestionImage,
    resetImageStates,
  };
};
