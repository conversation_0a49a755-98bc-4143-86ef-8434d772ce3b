import { useState, useCallback } from 'react';
import axios from 'axios';
import Cookies from 'js-cookie';
import { fetchInstructorLessons } from '../services/instructor';

// ملاحظة: هذا الملف لرفع الملفات العادية - زكي الخولي
// رفع الفيديوهات يتم عبر instructor.js مع دعم النظام المختلط (Cloudinary + Bunny Stream)

export const useFileUpload = (courseId, setLessons, setError) => {
  const [isUploading, setIsUploading] = useState({});
  const [uploadProgress, setUploadProgress] = useState({});
  const [uploadSuccess, setUploadSuccess] = useState({});
  const [uploadError, setUploadError] = useState({});
  const [uploadedFile, setUploadedFile] = useState({});

  const handleAddFile = useCallback(async (lessonId, fileInputRefs) => {
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }

    const file = fileInputRefs.current[lessonId]?.files[0];
    console.log("📄 الملف المحدد:", file);
    if (!file) {
      setError("يرجى اختيار ملف");
      return;
    }

    const formData = new FormData();
    formData.append("resources", file);

    // ⬇️ تهيئة الحالات
    setIsUploading((prev) => ({ ...prev, [lessonId]: true }));
    setUploadProgress((prev) => ({ ...prev, [lessonId]: 0 }));
    setUploadSuccess((prev) => ({ ...prev, [lessonId]: false }));
    setUploadError((prev) => ({ ...prev, [lessonId]: null }));

    try {
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL}/api/lessons/${lessonId}/upload-resource/`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
          onUploadProgress: (progressEvent) => {
            const percent = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            console.log("📦 progress:", percent);
            setUploadProgress((prev) => ({ ...prev, [lessonId]: percent }));
          },
        }
      );

      // ✅ حفظ رابط الملف
      const fileUrl = response.data?.resources;
      if (fileUrl) {
        setUploadedFile((prev) => ({ ...prev, [lessonId]: fileUrl }));
      }

      setUploadSuccess((prev) => ({ ...prev, [lessonId]: true }));

      // تحديث الدروس
      const lessonsList = await fetchInstructorLessons(courseId, token);
      setLessons(lessonsList);
      console.log(lessonsList);
    } catch (error) {
      console.error("❌ رفع الملف فشل:", error);
      setUploadError((prev) => ({ ...prev, [lessonId]: "فشل في رفع الملف" }));
    } finally {
      setIsUploading((prev) => ({ ...prev, [lessonId]: false }));
      setTimeout(() => {
        setUploadSuccess((prev) => ({ ...prev, [lessonId]: false }));
        setUploadError((prev) => ({ ...prev, [lessonId]: null }));
        setUploadProgress((prev) => ({ ...prev, [lessonId]: 0 }));
      }, 2000);
    }
  }, [courseId, setLessons, setError]);

  const handleDeleteResource = useCallback(async (lessonId, resourceUrl) => {
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }

    try {
      await axios.delete(
        `${process.env.NEXT_PUBLIC_API_URL}/api/lessons/${lessonId}/delete_resource/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
          data: { resource_url: resourceUrl },
        }
      );

      // تحديث الدروس
      const lessonsList = await fetchInstructorLessons(courseId, token);
      setLessons(lessonsList);
    } catch (error) {
      console.error("❌ حذف الملف فشل:", error);
      setError("فشل في حذف الملف");
    }
  }, [courseId, setLessons, setError]);

  return {
    isUploading,
    uploadProgress,
    uploadSuccess,
    uploadError,
    uploadedFile,
    handleAddFile,
    handleDeleteResource,
  };
};
