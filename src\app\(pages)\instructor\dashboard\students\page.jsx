// صفحة إدارة الطلاب للمعلم مع ربط الباك اند - zaki alkholy
'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useSelector } from 'react-redux';
import Cookies from 'js-cookie';
import { toast } from 'react-hot-toast';
import {
  Users,
  Search,
  Filter,
  BookOpen,
  Clock,
  Award,
  TrendingUp,
  Eye,
  Mail,
  Phone,
  Calendar,
  Target
} from 'lucide-react';

import { selectCurrentUser, selectIsAuthenticated } from '@/store/authSlice';
import { API_BASE_URL } from '@/config/api';
import axios from 'axios';
import { PageLoader } from '@/components/common/UniversalLoader';

// مكون لعرض بطاقة طالب - zaki alkholy
const StudentCard = ({ student }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-300"
    >
      <div className="flex items-start justify-between">
        <div className="flex items-center">
          <img
            src={student.profile_image || '/images/default-course.jpg'}
            alt={student.name}
            className="w-12 h-12 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600"
          />
          <div className="mr-4">
            <h3 className="font-semibold text-gray-900 dark:text-white">{student.name}</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">{student.email}</p>
            {/* إضافة رقم الهاتف - zaki alkholy */}
            <p className="text-sm text-blue-600 dark:text-blue-400">📱 {student.phone_number}</p>
            <div className="flex items-center mt-1">
              <Calendar className="w-4 h-4 text-gray-400 mr-1" />
              <span className="text-xs text-gray-500 dark:text-gray-400">
                انضم في {new Date(student.enrolled_at).toLocaleDateString('ar-EG')}
              </span>
            </div>
          </div>
        </div>
        <div className="text-right">
          <div className="flex items-center mb-2">
            <BookOpen className="w-4 h-4 text-blue-500 dark:text-blue-400 mr-1" />
            <span className="text-sm font-medium text-foreground">{student.course?.title || 'كورس'}</span>
          </div>
          <div className="flex items-center mb-2">
            <Target className="w-4 h-4 text-green-500 dark:text-green-400 mr-1" />
            <span className="text-sm font-medium text-foreground">{student.progress || 0}% مكتمل</span>
          </div>
          <div className="flex items-center mb-2">
            <Award className="w-4 h-4 text-purple-500 dark:text-purple-400 mr-1" />
            <span className="text-sm font-medium text-foreground">
              {student.completed_lessons || 0}/{student.total_lessons || 0} دروس
            </span>
          </div>
          {student.total_quizzes > 0 && (
            <div className="flex items-center mb-2">
              <Target className="w-4 h-4 text-green-500 dark:text-green-400 mr-1" />
              <span className="text-sm font-medium text-foreground">
                {student.average_quiz_score || 0}% متوسط الامتحانات
              </span>
            </div>
          )}
          <div className="flex items-center">
            <Clock className="w-4 h-4 text-orange-500 dark:text-orange-400 mr-1" />
            <span className="text-sm font-medium text-foreground">{student.last_activity || 'غير متاح'}</span>
          </div>
        </div>
      </div>

      {/* شريط التقدم */}
      <div className="mt-4">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">التقدم العام</span>
          <span className="text-sm text-gray-500 dark:text-gray-400">{student.progress || 0}%</span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className="bg-blue-500 dark:bg-blue-400 h-2 rounded-full transition-all duration-300"
            style={{ width: `${student.progress || 0}%` }}
          />
        </div>
      </div>

      {/* درجات الامتحانات والواجبات - zaki alkholy */}
      {((student.exam_scores && student.exam_scores.length > 0) ||
        (student.assignment_scores && student.assignment_scores.length > 0) ||
        (student.quiz_scores && student.quiz_scores.length > 0)) && (
        <div className="mt-4 space-y-4">

          {/* الامتحانات */}
          {student.exam_scores && student.exam_scores.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                <Target className="w-4 h-4 text-red-500 mr-1" />
                الامتحانات ({student.exam_scores.length}):
              </h4>
              <div className="space-y-2">
                {student.exam_scores.slice(0, 2).map((exam, index) => (
                  <div key={index} className="flex justify-between items-center p-2 bg-red-50 rounded">
                    <span className="text-xs text-gray-600">{exam.quiz_title}</span>
                    <div className="flex items-center gap-2">
                      <span className={`text-xs font-medium ${
                        exam.passed ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {exam.score}/{exam.max_score}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        exam.passed
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {Math.round(exam.percentage)}%
                      </span>
                    </div>
                  </div>
                ))}
                {student.exam_scores.length > 2 && (
                  <p className="text-xs text-gray-500">
                    +{student.exam_scores.length - 2} امتحانات أخرى
                  </p>
                )}
              </div>
            </div>
          )}

          {/* الواجبات */}
          {student.assignment_scores && student.assignment_scores.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                <BookOpen className="w-4 h-4 text-blue-500 mr-1" />
                الواجبات ({student.assignment_scores.length}):
              </h4>
              <div className="space-y-2">
                {student.assignment_scores.slice(0, 2).map((assignment, index) => (
                  <div key={index} className="flex justify-between items-center p-2 bg-blue-50 rounded">
                    <span className="text-xs text-gray-600">{assignment.quiz_title}</span>
                    <div className="flex items-center gap-2">
                      <span className={`text-xs font-medium ${
                        assignment.passed ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {assignment.score}/{assignment.max_score}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        assignment.passed
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {Math.round(assignment.percentage)}%
                      </span>
                    </div>
                  </div>
                ))}
                {student.assignment_scores.length > 2 && (
                  <p className="text-xs text-gray-500">
                    +{student.assignment_scores.length - 2} واجبات أخرى
                  </p>
                )}
              </div>
            </div>
          )}

          {/* عرض البيانات القديمة إذا لم تكن البيانات الجديدة متوفرة - zaki alkholy */}
          {(!student.exam_scores && !student.assignment_scores && student.quiz_scores && student.quiz_scores.length > 0) && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                <Target className="w-4 h-4 text-gray-500 mr-1" />
                الاختبارات ({student.quiz_scores.length}):
              </h4>
              <div className="space-y-2">
                {student.quiz_scores.slice(0, 3).map((quiz, index) => (
                  <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                    <span className="text-xs text-gray-600">{quiz.quiz_title}</span>
                    <div className="flex items-center gap-2">
                      <span className={`text-xs font-medium ${
                        quiz.passed ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {quiz.score}/{quiz.max_score}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        quiz.passed
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {Math.round(quiz.percentage)}%
                      </span>
                    </div>
                  </div>
                ))}
                {student.quiz_scores.length > 3 && (
                  <p className="text-xs text-gray-500">
                    +{student.quiz_scores.length - 3} اختبارات أخرى
                  </p>
                )}
              </div>
            </div>
          )}

          {/* مجموع الدرجات الإجمالي - zaki alkholy */}
          {(student.total_actual_score !== undefined && student.total_max_score !== undefined) && (
            <div className="mt-4 p-3 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg border border-purple-200">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">مجموع الدرجات:</span>
                <div className="flex items-center gap-2">
                  <span className="text-lg font-bold text-purple-600">
                    {student.total_actual_score} من {student.total_max_score}
                  </span>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    student.total_actual_score >= (student.total_max_score / 2)
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {student.total_max_score > 0
                      ? Math.round((student.total_actual_score / student.total_max_score) * 100)
                      : 0}%
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* الكورس المسجل به */}
      <div className="mt-4">
        <h4 className="text-sm font-medium text-gray-700 mb-2">الكورس:</h4>
        <div className="flex flex-wrap gap-2">
          {student.course && (
            <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
              {student.course.title}
            </span>
          )}
        </div>
      </div>

      {/* معلومات إضافية */}
      {student.total_watch_time > 0 && (
        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-600">إجمالي وقت المشاهدة:</span>
            <span className="font-medium text-blue-600">{student.total_watch_time} دقيقة</span>
          </div>
        </div>
      )}
    </motion.div>
  );
};

// مكون لعرض إحصائيات الطلاب - zaki alkholy
const StudentsStats = ({ stats }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-xl border-2 border-blue-200 dark:border-blue-700"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الطلاب</p>
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{stats.total_students || 0}</p>
          </div>
          <Users className="w-8 h-8 text-blue-600 dark:text-blue-400" />
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-green-50 dark:bg-green-900/20 p-6 rounded-xl border-2 border-green-200 dark:border-green-700"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">الطلاب النشطون</p>
            <p className="text-2xl font-bold text-green-600 dark:text-green-400">{stats.active_students || 0}</p>
          </div>
          <TrendingUp className="w-8 h-8 text-green-600 dark:text-green-400" />
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-purple-50 dark:bg-purple-900/20 p-6 rounded-xl border-2 border-purple-200 dark:border-purple-700"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">متوسط التقدم</p>
            <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">{stats.average_progress || 0}%</p>
          </div>
          <Target className="w-8 h-8 text-purple-600 dark:text-purple-400" />
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-orange-50 dark:bg-orange-900/20 p-6 rounded-xl border-2 border-orange-200 dark:border-orange-700"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">معدل الإكمال</p>
            <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">{stats.completion_rate || 0}%</p>
          </div>
          <Award className="w-8 h-8 text-orange-600 dark:text-orange-400" />
        </div>
      </motion.div>
    </div>
  );
};

// الصفحة الرئيسية لإدارة الطلاب - zaki alkholy
export default function InstructorStudentsPage() {
  const [studentsData, setStudentsData] = useState([]);
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCourse, setFilterCourse] = useState('all');
  const [courses, setCourses] = useState([]);

  // الحصول على بيانات المستخدم من Redux - zaki alkholy
  const user = useSelector(selectCurrentUser);
  const isAuthenticated = useSelector(selectIsAuthenticated);

  // جلب بيانات الطلاب من API - zaki alkholy
  useEffect(() => {
    const fetchStudentsData = async () => {
      if (!isAuthenticated || !user || !user.is_instructor) {
        setLoading(false);
        return;
      }

      try {
        const token = Cookies.get('authToken') || localStorage.getItem('access_token');
        if (!token) {
          throw new Error('لا يوجد رمز مصادقة');
        }

        const headers = {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        };

        // استخدام الـ API الجديد المحسن - zaki alkholy
        const response = await axios.get(
          `${API_BASE_URL}/api/instructor/students/`,
          { headers }
        );

        const data = response.data;
        console.log('بيانات الطلاب من API الجديد - zaki alkholy:', data);

        // تحديث البيانات
        setStudentsData(data.students || []);
        setStats(data.stats || {});
        setCourses(data.courses || []);



      } catch (error) {
        console.error('خطأ في جلب بيانات الطلاب:', error);
        setError(error.message);
        toast.error('حدث خطأ في جلب البيانات');
      } finally {
        setLoading(false);
      }
    };

    fetchStudentsData();
  }, [isAuthenticated, user]);

  // فلترة الطلاب حسب البحث والكورس - zaki alkholy
  const filteredStudents = studentsData.filter(student => {
    const matchesSearch = student.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.username?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCourse = filterCourse === 'all' ||
                         student.course?.id === filterCourse;
    return matchesSearch && matchesCourse;
  });

  // عرض شاشة التحميل - zaki alkholy
  if (loading) {
    return <PageLoader text="جاري تحميل بيانات الطلاب..." />;
  }

  // عرض رسالة خطأ إذا لم يكن المستخدم معلم - zaki alkholy
  if (!isAuthenticated || !user || !user.is_instructor) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">غير مصرح لك بالوصول</h2>
          <p className="text-gray-600 dark:text-gray-400">هذه الصفحة متاحة للمعلمين فقط</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">إدارة الطلاب</h1>
          <p className="text-gray-600 dark:text-gray-400">تتبع تقدم طلابك وإدارة تفاعلهم مع كورساتك</p>
        </div>

        {/* الإحصائيات */}
        <StudentsStats stats={stats} />

        {/* أدوات البحث والفلترة */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="البحث عن طالب..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-foreground rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <select
                value={filterCourse}
                onChange={(e) => setFilterCourse(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-foreground"
              >
                <option value="all">جميع الكورسات</option>
                {courses.map(course => (
                  <option key={course.id} value={course.id}>{course.title}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* قائمة الطلاب */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredStudents.length > 0 ? (
            filteredStudents.map((student) => (
              <StudentCard key={student.id} student={student} />
            ))
          ) : (
            <div className="col-span-full text-center py-12">
              <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">لا يوجد طلاب</h3>
              <p className="text-gray-600 dark:text-gray-400">
                {searchTerm || filterCourse !== 'all'
                  ? 'لا توجد نتائج تطابق البحث'
                  : 'لم يسجل أي طالب في كورساتك بعد'}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
