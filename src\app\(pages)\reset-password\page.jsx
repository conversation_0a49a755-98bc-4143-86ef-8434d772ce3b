"use client";
import { useState, useEffect, Suspense } from "react";
import { useSearchParams, useParams } from "next/navigation";
import axios from "axios";
import { useRouter } from "next/navigation";
import { resetPassword } from "../../../services/anyUserDataChange";
import { ButtonLoader } from "@/components/common/UniversalLoader";
import Link from "next/link";

function ResetPasswordForm() {
  const { id } = useParams();
  const router = useRouter();
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [message, setMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  const validatePassword = (password) => {
    const errors = {};
    if (password.length < 8) {
      errors.password = "كلمة المرور يجب أن تكون 8 أحرف على الأقل";
    }
    if (!/(?=.*[a-z])/.test(password)) {
      errors.password = "كلمة المرور يجب أن تحتوي على حرف صغير";
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      errors.password = "كلمة المرور يجب أن تحتوي على حرف كبير";
    }
    if (!/(?=.*\d)/.test(password)) {
      errors.password = "كلمة المرور يجب أن تحتوي على رقم";
    }
    return errors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrors({});

    // التحقق من صحة كلمة المرور
    const passwordErrors = validatePassword(newPassword);
    if (Object.keys(passwordErrors).length > 0) {
      setErrors(passwordErrors);
      return;
    }

    // التحقق من تطابق كلمات المرور
    if (newPassword !== confirmPassword) {
      setErrors({ confirm: "كلمات المرور غير متطابقة" });
      return;
    }

    setIsLoading(true);
    try {
      await resetPassword(token, newPassword);
      setMessage("تم تغيير كلمة المرور بنجاح.");
      setTimeout(() => {
        router.push("/login");
      }, 2000);
    } catch {
      setMessage("الرابط غير صالح أو منتهي.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-foreground mb-2">
            إعادة تعيين كلمة المرور
          </h2>
          <p className="text-secondary">
            أدخل كلمة المرور الجديدة
          </p>
        </div>

        <form onSubmit={handleSubmit} className="mt-8 space-y-6">
          <div>
            <label htmlFor="newPassword" className="block text-sm font-medium text-foreground mb-2">
              كلمة المرور الجديدة
            </label>
            <input
              id="newPassword"
              type="password"
              placeholder="أدخل كلمة المرور الجديدة"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-foreground placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
            />
            {errors.password && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.password}</p>
            )}
          </div>

          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-medium text-foreground mb-2">
              تأكيد كلمة المرور
            </label>
            <input
              id="confirmPassword"
              type="password"
              placeholder="أعد إدخال كلمة المرور"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-foreground placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
            />
            {errors.confirm && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.confirm}</p>
            )}
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50"
          >
            {isLoading ? <ButtonLoader size="small" /> : "تأكيد"}
          </button>

          {message && (
            <div className={`text-center p-3 rounded-md ${
              message.includes("بنجاح")
                ? "bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300"
                : "bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300"
            }`}>
              {message}
            </div>
          )}

          <div className="text-center">
            <Link
              href="/login"
              className="text-sm text-primary hover:text-primary/80 font-medium"
            >
              العودة إلى تسجيل الدخول
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
}

export default function ResetPassword() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p className="text-foreground">جاري التحميل...</p>
        </div>
      </div>
    }>
      <ResetPasswordForm />
    </Suspense>
  );
}
