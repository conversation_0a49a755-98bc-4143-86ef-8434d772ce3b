"use client";
import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { useRouter, useParams } from "next/navigation";
import Link from "next/link";
import Cookies from "js-cookie";
import { motion } from "framer-motion";
import {
  User,
  Mail,
  Phone,
  Calendar,
  MapPin,
  CreditCard,
  BookOpen,
  Star,
  Users,
  Award,
  Globe,
  Wallet,
  CheckCircle,
  Clock,
  TrendingUp,
  MessageCircle,
  Play,
} from "lucide-react";
import { fetchInstructorProfile } from "../../../../services/instructor";
import {
  selectCurrentUser,
  selectIsAuthenticated,
} from "../../../../store/authSlice";

// دوال مساعدة لتنسيق البيانات
const formatPrice = (price) => {
  if (!price) return "مجاني";
  return `${price} جنيه`;
};

const formatDuration = (duration) => {
  if (!duration) return "غير محدد";
  if (duration < 60) return `${duration} دقيقة`;
  const hours = Math.floor(duration / 60);
  const minutes = duration % 60;
  return minutes > 0 ? `${hours}س ${minutes}د` : `${hours} ساعة`;
};

const renderStars = (rating) => {
  const stars = [];
  const fullStars = Math.floor(rating || 0);
  const hasHalfStar = (rating || 0) % 1 !== 0;

  for (let i = 0; i < 5; i++) {
    if (i < fullStars) {
      stars.push(
        <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
      );
    } else if (i === fullStars && hasHalfStar) {
      stars.push(
        <Star
          key={i}
          className="w-4 h-4 text-yellow-400 fill-current opacity-50"
        />
      );
    } else {
      stars.push(
        <Star key={i} className="w-4 h-4 text-gray-300 dark:text-gray-600" />
      );
    }
  }
  return stars;
};

const truncateDescription = (text, maxLength = 50) => {
  if (!text) return "";
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + "...";
};

// مكون لعرض إحصائيات سريعة - محسن للمعلم
const StatCard = ({ icon: Icon, title, value, subtitle, color = "blue" }) => {
  const colorClasses = {
    blue: "bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 border-blue-200 dark:border-blue-700",
    green:
      "bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 border-green-200 dark:border-green-700",
    purple:
      "bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 border-purple-200 dark:border-purple-700",
    orange:
      "bg-orange-50 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 border-orange-200 dark:border-orange-700",
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`p-6 rounded-xl border-2 ${colorClasses[color]} hover:shadow-lg transition-all duration-300`}
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
            {title}
          </p>
          <p className="text-2xl font-bold mt-1 text-gray-900 dark:text-white">
            {value}
          </p>
          {subtitle && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {subtitle}
            </p>
          )}
        </div>
        <Icon className="w-8 h-8" />
      </div>
    </motion.div>
  );
};

// مكون لعرض معلومات الاتصال
const ContactInfo = ({ icon: Icon, label, value, color = "gray" }) => {
  if (!value) return null;

  const iconColorClasses = {
    gray: "text-gray-500 dark:text-gray-400",
    blue: "text-blue-500 dark:text-blue-400",
    green: "text-green-500 dark:text-green-400",
    purple: "text-purple-500 dark:text-purple-400",
    orange: "text-orange-500 dark:text-orange-400",
  };

  return (
    <div className="flex items-center space-x-3 space-x-reverse p-3 rounded-lg bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
      <Icon className={`w-5 h-5 ${iconColorClasses[color]}`} />
      <div>
        <p className="text-xs text-gray-500 dark:text-gray-400">{label}</p>
        <p className="text-sm font-medium text-gray-900 dark:text-white">
          {value}
        </p>
      </div>
    </div>
  );
};

export default function InstructorCourses() {
  const theUrl = "https://res.cloudinary.com/djwhgnwp5/";
  const { id } = useParams();
  const router = useRouter();
  const user = useSelector(selectCurrentUser);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const [instructorProfile, setInstructorProfile] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [errorMsg, setErrorMsg] = useState(null);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/login");
      return;
    }
    // تم حذف شرط التحويل عند محاولة عرض صفحة معلم آخر
  }, [isAuthenticated, user, id, router]);

  useEffect(() => {
    const loadInstructorData = async () => {
      try {
        setIsLoading(true);
        setErrorMsg(null);
        const token = Cookies.get("authToken");
        if (!token) {
          setErrorMsg("يرجى تسجيل الدخول لعرض الدورات");
          setIsLoading(false);
          return;
        }
        const profile = await fetchInstructorProfile(id, token);
        console.log("Instructor Profile Data:", profile);
        if (!profile) {
          setErrorMsg("لم يتم العثور على المعلم");
          setIsLoading(false);
          return;
        }
        setInstructorProfile(profile);
      } catch (err) {
        if (err.response?.status === 401) {
          setErrorMsg("انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى");
        } else {
          setErrorMsg("حدث خطأ أثناء جلب بيانات المعلم");
        }
      } finally {
        setIsLoading(false);
      }
    };
    loadInstructorData();
  }, [id]);

  if (isLoading) {
    return (
      <div className="min-h-screen pt-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 max-w-md mx-auto"
            >
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary border-t-transparent mx-auto mb-6"></div>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                جاري تحميل الملف الشخصي...
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                يرجى الانتظار قليلاً
              </p>
              <div className="mt-4 flex justify-center space-x-1 space-x-reverse">
                <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
                <div
                  className="w-2 h-2 bg-primary rounded-full animate-bounce"
                  style={{ animationDelay: "0.1s" }}
                ></div>
                <div
                  className="w-2 h-2 bg-primary rounded-full animate-bounce"
                  style={{ animationDelay: "0.2s" }}
                ></div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    );
  }

  if (errorMsg) {
    return (
      <div className="min-h-screen pt-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-50 dark:bg-red-900/20 border-2 border-red-200 dark:border-red-700 rounded-2xl p-8 max-w-md mx-auto text-center"
          >
            <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <MessageCircle className="w-8 h-8 text-red-600 dark:text-red-400" />
            </div>
            <h3 className="text-lg font-bold text-red-800 dark:text-red-300 mb-2">
              حدث خطأ
            </h3>
            <p className="text-red-600 dark:text-red-400 mb-6">{errorMsg}</p>
            <Link
              href="/login"
              className="inline-flex items-center px-6 py-3 bg-primary hover:bg-primary-dark text-white font-medium rounded-lg transition-colors duration-200"
            >
              تسجيل الدخول
            </Link>
          </motion.div>
        </div>
      </div>
    );
  }

  if (!instructorProfile && user) {
    // عرض بيانات المستخدم من store إذا لم يتم جلبها من API
    return (
      <div className="min-h-screen pt-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 max-w-2xl mx-auto"
          >
            <div className="text-center mb-8">
              <div className="relative inline-block mb-6">
                <img
                  src={user.profile_image || "/images/default-course.jpg"}
                  alt="الصورة الشخصية"
                  className="w-32 h-32 rounded-full object-cover border-4 border-primary shadow-lg"
                />
                <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white dark:border-gray-800 flex items-center justify-center">
                  <CheckCircle className="w-4 h-4 text-white" />
                </div>
              </div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {user.first_name} {user.last_name}
              </h1>
              {/* <p className="text-lg text-gray-600 dark:text-gray-400 mb-6">
                @{user.username}
              </p> */}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <ContactInfo
                icon={Mail}
                label="البريد الإلكتروني"
                value={user.email}
                color="blue"
              />
              <ContactInfo
                icon={Phone}
                label="رقم الهاتف"
                value={user.phone_number}
                color="green"
              />
              <ContactInfo
                icon={Globe}
                label="اللغة"
                value={user.language === "ar" ? "العربية" : "English"}
                color="purple"
              />
              <ContactInfo
                icon={CreditCard}
                label="طريقة الدفع"
                value={user.payment_method}
                color="orange"
              />
            </div>

            {user.has_wallet && (
              <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <Wallet className="w-5 h-5 text-green-600 dark:text-green-400" />
                  <span className="text-green-800 dark:text-green-300 font-medium">
                    المحفظة مفعلة
                  </span>
                </div>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    );
  }

  if (!instructorProfile) {
    return (
      <div className="min-h-screen pt-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-yellow-50 dark:bg-yellow-900/20 border-2 border-yellow-200 dark:border-yellow-700 rounded-2xl p-8 max-w-md mx-auto text-center"
          >
            <div className="w-16 h-16 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <User className="w-8 h-8 text-yellow-600 dark:text-yellow-400" />
            </div>
            <h3 className="text-lg font-bold text-yellow-800 dark:text-yellow-300 mb-2">
              المعلم غير موجود
            </h3>
            <p className="text-yellow-600 dark:text-yellow-400 mb-6">
              لم يتم العثور على المعلم المطلوب
            </p>
            <Link
              href="/courses"
              className="inline-flex items-center px-6 py-3 bg-primary hover:bg-primary-dark text-white font-medium rounded-lg transition-colors duration-200"
            >
              تصفح الكورسات
            </Link>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-16 bg-white dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* بطاقة معلومات المعلم المحسنة */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 mb-8"
        >
          <div className="flex flex-col lg:flex-row items-center lg:items-start space-y-6 lg:space-y-0 lg:space-x-8 lg:space-x-reverse">
            {/* الصورة الشخصية */}
            <div className="relative">
              <img
                src={
                  instructorProfile.profile_image ||
                  "/images/default-course.jpg"
                }
                alt={`${instructorProfile.first_name} ${instructorProfile.last_name}`}
                className="w-32 h-32 lg:w-40 lg:h-40 rounded-full object-cover border-4 border-primary shadow-lg"
              />
              <div className="absolute -bottom-2 -right-2 w-10 h-10 bg-green-500 rounded-full border-4 border-white dark:border-gray-800 flex items-center justify-center">
                <CheckCircle className="w-5 h-5 text-white" />
              </div>
            </div>

            {/* معلومات المعلم */}
            <div className="flex-1 text-center lg:text-right">
              <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                {instructorProfile.first_name} {instructorProfile.last_name}
              </h1>
              {/* <p className="text-xl text-gray-600 dark:text-gray-400 mb-4">
                @{instructorProfile.username}
              </p> */}

              {/* الإحصائيات السريعة */}
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <StatCard
                  icon={BookOpen}
                  title="الدورات"
                  value={
                    instructorProfile.courses
                      ? instructorProfile.courses.length
                      : 0
                  }
                  color="blue"
                />
                <StatCard
                  icon={Users}
                  title="الطلاب"
                  value="0"
                  subtitle="إجمالي الطلاب"
                  color="green"
                />
                <StatCard
                  icon={Star}
                  title="التقييم"
                  value="5.0"
                  subtitle="من 5 نجوم"
                  color="orange"
                />
                <StatCard
                  icon={Award}
                  title="الخبرة"
                  value="خبير"
                  subtitle="مستوى المعلم"
                  color="purple"
                />
              </div>
            </div>
          </div>

          {/* معلومات الاتصال */}
          <div className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <MessageCircle className="w-5 h-5 mr-2 text-primary" />
              معلومات الاتصال
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <ContactInfo
                icon={Mail}
                label="البريد الإلكتروني"
                value={instructorProfile.email}
                color="blue"
              />
              <ContactInfo
                icon={Phone}
                label="رقم الهاتف"
                value={instructorProfile.phone_number}
                color="green"
              />
              <ContactInfo
                icon={Globe}
                label="اللغة"
                value={
                  instructorProfile.language === "ar" ? "العربية" : "English"
                }
                color="purple"
              />
              <ContactInfo
                icon={CreditCard}
                label="طريقة الدفع"
                value={instructorProfile.payment_method}
                color="orange"
              />
            </div>

            {/* معلومات إضافية */}
            <div className="mt-6 flex flex-wrap gap-4">
              {instructorProfile.has_wallet && (
                <div className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
                  <Wallet className="w-5 h-5 text-green-600 dark:text-green-400" />
                  <span className="text-green-800 dark:text-green-300 font-medium">
                    المحفظة مفعلة
                  </span>
                </div>
              )}
              {instructorProfile.is_instructor && (
                <div className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                  <Award className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  <span className="text-blue-800 dark:text-blue-300 font-medium">
                    معلم معتمد
                  </span>
                </div>
              )}
              {/* {instructorProfile.is_student && (
                <div className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-700">
                  <BookOpen className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                  <span className="text-purple-800 dark:text-purple-300 font-medium">
                    طالب أيضاً
                  </span>
                </div>
              )} */}
            </div>
          </div>
        </motion.div>

        {/* قائمة الدورات المحسنة */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
              <BookOpen className="w-6 h-6 mr-2 text-primary" />
              دورات المعلم
            </h2>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {instructorProfile.courses ? instructorProfile.courses.length : 0}{" "}
              دورة متاحة
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {instructorProfile.courses &&
            instructorProfile.courses.length > 0 ? (
              instructorProfile.courses.map((course, index) => (
                <motion.div
                  key={course.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 * index }}
                >
                  <div className="group bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden">
                    {/* Course Image */}
                    <div className="relative overflow-hidden">
                      <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 relative">
                        {course.thumbnail ? (
                          <img
                            src={`${theUrl}${course.thumbnail}`}
                            alt={course.title}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              e.target.style.display = "none";
                              e.target.nextSibling.style.display = "flex";
                            }}
                          />
                        ) : null}
                        <div className="absolute inset-0 flex items-center justify-center">
                          <Play className="w-16 h-16 text-blue-500 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300" />
                        </div>
                      </div>

                      {/* Overlay */}
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <Link
                            href={`/student/course/${course.slug}`}
                            className="bg-white text-blue-600 px-4 py-2 rounded-full font-medium shadow-lg transform scale-90 group-hover:scale-100 transition-transform duration-300"
                          >
                            معاينة الكورس
                          </Link>
                        </div>
                      </div>

                      {/* Discount Badge */}
                      {course.discount_price &&
                        course.price &&
                        course.discount_price < course.price && (
                          <div className="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                            خصم{" "}
                            {Math.round(
                              ((course.price - course.discount_price) /
                                course.price) *
                                100
                            )}
                            %
                          </div>
                        )}
                    </div>

                    {/* Course Content */}
                    <div className="p-6">
                      {/* Title */}
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                        {course.title}
                      </h3>

                      {/* Description */}
                      <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">
                        {truncateDescription(
                          course.short_description || course.description
                        )}
                      </p>

                      {/* Instructor */}
                      <div className="flex items-center mb-4">
                        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center ml-3">
                          <span className="text-white text-sm font-bold">
                            {instructorProfile.first_name?.charAt(0) || "م"}
                          </span>
                        </div>
                        <span className="text-gray-700 dark:text-gray-300 text-sm">
                          {instructorProfile.first_name +
                            " " +
                            instructorProfile.last_name ||
                            instructorProfile.username}
                        </span>
                      </div>

                      {/* Rating and Stats */}
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <div className="flex items-center">
                            {renderStars(course.rating)}
                          </div>
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            ({course.rating || "0.0"})
                          </span>
                        </div>
                        <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                          <Users className="w-4 h-4 ml-1" />
                          {course.students_count || 0}
                        </div>
                      </div>

                      {/* Duration and Level */}
                      <div className="flex items-center justify-between mb-4 text-sm text-gray-500 dark:text-gray-400">
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 ml-1" />
                          {formatDuration(course.duration)}
                        </div>
                        <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs">
                          {course.level === "beginner"
                            ? "مبتدئ"
                            : course.level === "intermediate"
                            ? "متوسط"
                            : course.level === "advanced"
                            ? "متقدم"
                            : course.level || "غير محدد"}
                        </span>
                      </div>

                      {/* Price and CTA */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                            {course.discount_price
                              ? formatPrice(course.discount_price)
                              : formatPrice(course.price)}
                          </span>
                          {course.discount_price &&
                            course.price &&
                            course.discount_price < course.price && (
                              <span className="text-sm text-gray-500 dark:text-gray-400 line-through">
                                {formatPrice(course.price)}
                              </span>
                            )}
                        </div>
                        <Link
                          href={`/student/course/${course.slug}`}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                        >
                          ابدأ الآن
                        </Link>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="col-span-full text-center py-16"
              >
                <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 max-w-md mx-auto">
                  <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                    <BookOpen className="w-8 h-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    لا توجد دورات متاحة
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    لم يقم هذا المعلم بإنشاء أي دورات حتى الآن
                  </p>
                </div>
              </motion.div>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
}
