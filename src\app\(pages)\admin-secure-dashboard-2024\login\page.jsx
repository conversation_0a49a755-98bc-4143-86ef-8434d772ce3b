"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import axios from "axios";
import { Eye, EyeOff, Shield, Lock, User } from "lucide-react";

export default function AdminLogin() {
  const [formData, setFormData] = useState({
    username: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const router = useRouter();

  // التحقق من وجود session مسبق
  useEffect(() => {
    const adminToken = localStorage.getItem("admin_token");
    const adminExpiry = localStorage.getItem("admin_token_expiry");
    
    if (adminToken && adminExpiry) {
      const now = new Date().getTime();
      if (now < parseInt(adminExpiry)) {
        // Token صالح، توجيه للدashboard
        router.push("/admin-secure-dashboard-2024");
        return;
      } else {
        // Token منتهي الصلاحية
        localStorage.removeItem("admin_token");
        localStorage.removeItem("admin_token_expiry");
      }
    }
  }, [router]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError(""); // مسح الخطأ عند الكتابة
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const response = await axios.post("http://127.0.0.1:8000/api/auth/login/", formData);
      const data = response.data;
      console.log("Full API response:", data); // للتشخيص

      // جلب بيانات المستخدم الكاملة للتحقق من الصلاحيات
      try {
        const userResponse = await axios.get(`http://127.0.0.1:8000/api/users/${data.user.id}/`, {
          headers: {
            "Authorization": `Bearer ${data.access}`,
            "Content-Type": "application/json",
          },
        });

        const userData = userResponse.data;
            console.log("Full user data:", userData);

            // التحقق من أن المستخدم admin
            if (userData.is_superuser || userData.is_staff) {
              // تسجيل دخول Admin Dashboard في Audit Log
              try {
                await axios.post("http://127.0.0.1:8000/api/admin-dashboard/audit-log/", {
                  action_type: "dashboard_login",
                  description: `تسجيل دخول Admin Dashboard بواسطة ${userData.username}`,
                  severity: "low"
                }, {
                  headers: {
                    "Authorization": `Bearer ${data.access}`,
                    "Content-Type": "application/json",
                  },
                });
              } catch (auditError) {
                console.warn("Failed to log admin login:", auditError);
              }

              // حفظ التوكن مع انتهاء صلاحية (30 دقيقة)
              const expiryTime = new Date().getTime() + (30 * 60 * 1000);
              localStorage.setItem("admin_token", data.access);
              localStorage.setItem("admin_token_expiry", expiryTime.toString());
              localStorage.setItem("admin_user", JSON.stringify(userData));

              // توجيه للدashboard
              router.push("/admin-secure-dashboard-2024");
            } else {
              setError(`غير مصرح لك بالوصول لهذه الصفحة. is_superuser: ${userData.is_superuser}, is_staff: ${userData.is_staff}`);
            }
        } catch (userError) {
          console.error("Error fetching user data:", userError);
          setError("حدث خطأ في التحقق من الصلاحيات");
        }
    } catch (error) {
      console.error("Login error:", error);
      setError("حدث خطأ في الاتصال");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-red-600 rounded-full mb-4">
            <Shield className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">
            Admin Dashboard
          </h1>
          <p className="text-gray-300">
            منطقة محظورة - مصرح للمديرين فقط
          </p>
        </div>

        {/* Login Form */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Username Field */}
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-white mb-2">
                اسم المستخدم
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <User className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  id="username"
                  name="username"
                  value={formData.username}
                  onChange={handleInputChange}
                  required
                  className="block w-full pr-10 pl-3 py-3 border border-white/30 rounded-lg bg-white/10 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="أدخل اسم المستخدم"
                />
              </div>
            </div>

            {/* Password Field */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-white mb-2">
                كلمة المرور
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                  className="block w-full pr-10 pl-10 py-3 border border-white/30 rounded-lg bg-white/10 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="أدخل كلمة المرور"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 left-0 pl-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400 hover:text-white" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400 hover:text-white" />
                  )}
                </button>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-3">
                <p className="text-red-200 text-sm text-center">{error}</p>
              </div>
            )}

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-600 disabled:to-gray-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-300 flex items-center justify-center"
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  جاري تسجيل الدخول...
                </div>
              ) : (
                "تسجيل الدخول"
              )}
            </button>
          </form>

          {/* Security Notice */}
          <div className="mt-6 p-4 bg-yellow-500/20 border border-yellow-500/50 rounded-lg">
            <p className="text-yellow-200 text-xs text-center">
              🔒 هذه منطقة آمنة محمية. جميع محاولات الدخول مسجلة ومراقبة.
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-gray-400 text-sm">
            © 2024 معلمى - Admin Dashboard
          </p>
        </div>
      </div>
    </div>
  );
}
