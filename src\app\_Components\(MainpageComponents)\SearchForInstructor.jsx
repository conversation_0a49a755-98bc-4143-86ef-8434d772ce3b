import React from "react";
import { useState, useEffect, useRef } from "react";
import axios from "axios";
import Cookies from "js-cookie";
import { API_BASE_URL } from "../../../config/api";
import { X, Search } from "lucide-react";
import { useRouter } from "next/navigation";
export default function SearchForInstructor({ showSearch, setShowSearch }) {
  const [searchQuery, setSearchQuery] = useState("");
  const [instructors, setInstructors] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const modalRef = useRef(null);
  const router = useRouter();

  const searchInstructors = async (query) => {
    if (!query.trim()) {
      setInstructors([]);
      setError(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const token = Cookies.get("authToken");

      const response = await axios.get(
        `${API_BASE_URL}/api/users/search-instructors/?q=${encodeURIComponent(
          query
        )}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      console.log("Search response:", response.data);
      const results =
        response.data.results || response.data.users || response.data || [];
      setInstructors(Array.isArray(results) ? results : []);
    } catch (error) {
      console.error("Error searching instructors:", error);
      setError(
        error.response?.data?.message || error.message || "حدث خطأ أثناء البحث"
      );
      setInstructors([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle click outside to close modal
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        setShowSearch(false);
      }
    };

    if (showSearch) {
      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [showSearch, setShowSearch]);

  // Handle search with debounce
  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      searchInstructors(searchQuery);
    }, 300);

    return () => clearTimeout(delayDebounceFn);
  }, [searchQuery]);

  // Handle instructor selection
  const handleInstructorSelect = (instructor) => {
    console.log("Selected instructor:", instructor);
    // Navigate to instructor profile or courses
    if (instructor.id) {
      router.push(`/instructor/${instructor.id}`);
    }
    setShowSearch(false);
  };
  return (
    <div>
      {/* Search Modal */}
      {showSearch && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-start justify-center pt-20">
          <div
            ref={modalRef}
            className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-2xl mx-4 p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                البحث في المنصة
              </h3>
              <button
                onClick={() => setShowSearch(false)}
                className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <div className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="ابحث عن الكورسات، المدرسين، أو المواضيع..."
                className="w-full p-4 pr-12 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                autoFocus
                onKeyDown={(e) => {
                  if (e.key === "Escape") {
                    setShowSearch(false);
                  }
                }}
              />
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            </div>

            {/* Search Results */}
            {searchQuery && (
              <div className="mt-4 max-h-96 overflow-y-auto">
                {loading && (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <span className="mr-2 text-gray-600 dark:text-gray-400">
                      جاري البحث...
                    </span>
                  </div>
                )}

                {error && (
                  <div className="text-center py-4 text-red-500 dark:text-red-400">
                    حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.
                  </div>
                )}

                {!loading && !error && instructors.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      المدرسين ({instructors.length})
                    </h4>
                    {instructors.map((instructor) => (
                      <div
                        key={instructor.id}
                        className="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                        onClick={() => handleInstructorSelect(instructor)}
                      >
                        <div className="flex-shrink-0 w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 dark:text-blue-400 font-medium">
                            {instructor.first_name?.charAt(0) ||
                              instructor.username?.charAt(0) ||
                              "M"}
                          </span>
                        </div>
                        <div className="mr-3 flex-1">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {instructor.first_name && instructor.last_name
                              ? `${instructor.first_name} ${instructor.last_name}`
                              : instructor.username}
                          </p>
                          {instructor.email && (
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {instructor.email}
                            </p>
                          )}
                        </div>
                        <Search className="w-4 h-4 text-gray-400" />
                      </div>
                    ))}
                  </div>
                )}

                {!loading &&
                  !error &&
                  searchQuery &&
                  instructors.length === 0 && (
                    <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                      <Search className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                      <p>لم يتم العثور على مدرسين</p>
                      <p className="text-sm">جرب البحث بكلمات مختلفة</p>
                    </div>
                  )}
              </div>
            )}

            <div className="mt-4 text-sm text-gray-500 dark:text-gray-400">
              اضغط Enter للبحث أو Esc للإغلاق
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
