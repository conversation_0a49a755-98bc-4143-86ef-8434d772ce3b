// store/authApi.js
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { setCredentials, logout, setError } from './authSlice';
import { API_BASE_URL } from '../config/api';

// التحقق من وجود عنوان الخادم
const API_URL = API_BASE_URL || 'http://127.0.0.1:8000';
if (!API_URL) {
  console.error('تحذير: لم يتم العثور على عنوان الخادم. يرجى إضافة NEXT_PUBLIC_API_URL في ملف .env.local');
}

// إضافة CSRF token للطلبات
const getCsrfToken = () => {
  const csrfToken = document.cookie
    .split('; ')
    .find(row => row.startsWith('csrftoken='))
    ?.split('=')[1];
  return csrfToken;
};

const baseQuery = fetchBaseQuery({
  baseUrl: API_URL,
  prepareHeaders: (headers, { getState }) => {
    // إضافة CSRF token
    const csrfToken = getCsrfToken();
    if (csrfToken) {
      headers.set('X-CSRFToken', csrfToken);
    }

    // إضافة التوكن
    const token = getState().auth.token;
    if (token) {
      headers.set('Authorization', `Bearer ${token}`);
    }

    // إضافة الرؤوس الأساسية
    headers.set('Content-Type', 'application/json');
    headers.set('Accept', 'application/json');

    return headers;
  },
  credentials: 'include',
});

// معالجة تجديد التوكن التلقائي
const baseQueryWithReauth = async (args, api, extraOptions) => {
  try {
    let result = await baseQuery(args, api, extraOptions);

    if (result?.error?.status === 401) {
      // محاولة تجديد التوكن
      const refreshToken = getState().auth.refreshToken;
      if (!refreshToken) {
        api.dispatch(logout());
        return result;
      }

      const refreshResult = await baseQuery(
        {
          url: 'auth/refresh/',
          method: 'POST',
          body: { refresh: refreshToken }
        },
        api,
        extraOptions
      );

      if (refreshResult?.data) {
        // تخزين التوكن الجديد
        api.dispatch(setCredentials(refreshResult.data));
        // إعادة المحاولة مع التوكن الجديد
        result = await baseQuery(args, api, extraOptions);
      } else {
        // تسجيل الخروج إذا فشل تجديد التوكن
        api.dispatch(logout());
      }
    }

    return result;
  } catch (error) {
    console.error('خطأ في معالجة الطلب:', error);
    return {
      error: {
        status: 'FETCH_ERROR',
        data: {
          message: 'حدث خطأ في الاتصال بالخادم',
          details: error.message
        }
      }
    };
  }
};

export const authApi = createApi({

  reducerPath: 'authApi',
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    login: builder.mutation({
      query: (credentials) => ({
        url: 'api/auth/login/',
        method: 'POST',
        body: credentials,
      }),
      transformResponse: (response) => {
        console.log('استجابة الخادم:', response);

        if (!response) {
          throw new Error("لم يتم استلام استجابة من الخادم");
        }

        // معالجة التنسيق الجديد للاستجابة
        const token = response.access || response.token || response.access_token;
        const user = response.user || response;

        if (!token) {
          console.error('استجابة غير صالحة:', response);
          throw new Error("لم يتم استلام التوكن من الخادم");
        }

        return {
          token,
          user
        };
      },
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled;
          dispatch(setCredentials(data));
        } catch (error) {
          console.error('خطأ في تسجيل الدخول:', error);
          console.log('تفاصيل الخطأ:', {
            error,
            data: error.data,
            errorData: error.error?.data,
            status: error.error?.status
          });
          dispatch(setError(error.message));
        }
      },
    }),
    register: builder.mutation({
      query: (userData) => ({
        url: 'api/auth/register/',
        method: 'POST',
        body: userData,
      }),
    }),
    logout: builder.mutation({
      query: () => ({
        url: 'api/auth/logout/',
        method: 'POST',
      }),
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;
          dispatch(logout());
        } catch (error) {
          console.error('خطأ في تسجيل الخروج:', error);
          dispatch(setError(error.message));
        }
      },
    }),
    refreshToken: builder.mutation({
      query: (refreshToken) => ({
        url: 'api/auth/refresh/',
        method: 'POST',
        body: { refresh: refreshToken },
      }),
    }),
    // getUser endpoint removed - user data comes from login response
    googleLogin: builder.mutation({
      query: (googleData) => ({
        url: 'api/google-login/',
        method: 'POST',
        body: googleData,
      }),
      transformResponse: (response) => {
        console.log("💡 Raw response from server:", response);

        const token = response.access || response.token || response.access_token;
        const refresh = response.refresh;
        const user = response.user;

        if (!token || !refresh) {
          throw new Error("Missing token or refresh from server");
        }

        return {
          token,
          refreshToken: refresh,
          user: user || null,  // 👈 مهم علشان setCredentials تشتغل
        };
      },
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled;
          console.log('Google login successful:', data);
          dispatch(setCredentials(data));  // 🧠 خزّن التوكن في store
        } catch (error) {
          console.error("Google login failed:", error);
          dispatch(setError(error.message));
        }
      },
    }),


  }),
});

export const {
  useLoginMutation,
  useRegisterMutation,
  useLogoutMutation,
  useRefreshTokenMutation,
  // useGetUserQuery removed - user data comes from login response
  useGoogleLoginMutation, // 👈 ضيف ده
} = authApi;